const router = require("express").Router();
const Exam = require("../models/examModel");
const authMiddleware = require("../middlewares/authMiddleware");
const Question = require("../models/questionModel");
const NotificationService = require("../services/notificationService");
const AWS = require("aws-sdk");
const { v4: uuidv4 } = require("uuid");
const User = require("../models/userModel");
const multer = require("multer");
const Report = require("../models/reportModel");
// Configure Multer Memory Storage
const storage = multer.memoryStorage();
const upload = multer({ storage });
const mongoose = require("mongoose");

const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

// add exam

router.post("/add", authMiddleware, async (req, res) => {
  try {
    // check if exam already exists
    const examExists = await Exam.findOne({ name: req.body.name });
    if (examExists) {
      return res
        .status(200)
        .send({ message: "Exam already exists", success: false });
    }
    req.body.questions = [];
    const newExam = new Exam(req.body);
    await newExam.save();

    // Send notification about new exam
    try {
      await NotificationService.notifyNewExam(newExam);
    } catch (notifError) {
      console.error('Error sending new exam notification:', notifError);
      // Don't fail the exam creation if notification fails
    }

    res.send({
      message: "Exam added successfully",
      success: true,
      data: newExam, // Return the created exam data
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

router.post("/get-all-exams", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    if (user.isAdmin) {
      const exams = await Exam.find();
      return res.send({
        message: "Exams fetched successfully (Admin)",
        data: exams,
        success: true,
      });
    }

    const userLevel = user.level || 'Primary';

    let exams;

    // Fetch exams based on user's level
    if (userLevel === "Secondary") {
      exams = await Exam.find({ level: "Secondary" });
    } else if (userLevel === "Advance") {
      exams = await Exam.find({ level: "Advance" });
    } else {
      // Primary level - include only primary level exams
      exams = await Exam.find({ level: "Primary" });
    }

    res.send({
      message: "Exams fetched successfully",
      data: exams,
      success: true,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

router.post("/get-exam-by-id", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const examId = req.body.examId;

    // Validate examId
    if (!examId || examId === 'undefined' || examId === 'null') {
      return res.status(400).send({
        message: "Invalid exam ID provided",
        success: false,
      });
    }

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(examId)) {
      return res.status(400).send({
        message: "Invalid exam ID format",
        success: false,
      });
    }

    // Get user to check their level
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    const exam = await Exam.findById(examId).populate("questions");
    if (!exam) {
      return res.status(404).send({
        message: "Exam not found",
        success: false,
      });
    }

    // Debug: Check exam questions
    console.log("=== GET EXAM DEBUG ===");
    console.log("Exam ID:", examId);
    console.log("Exam name:", exam.name);
    console.log("Questions count:", exam.questions.length);
    console.log("Question IDs:", exam.questions.map(q => q._id || q));
    if (exam.questions.length > 0) {
      console.log("First question sample:", exam.questions[0]);
    }
    console.log("======================");

    // Check if user has access to this exam based on their level
    if (!user.isAdmin) {
      const userLevel = user.level || 'Primary';
      const examLevel = exam.level || 'Primary';

      // Users can only access exams of their own level
      if (userLevel !== examLevel) {
        return res.status(403).send({
          message: `Access denied. You can only access ${userLevel} level exams. This exam is for ${examLevel} level.`,
          success: false,
        });
      }
    }

    res.send({
      message: "Exam fetched successfully",
      data: exam,
      success: true,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// edit exam by id
router.post("/edit-exam-by-id", authMiddleware, async (req, res) => {
  try {
    await Exam.findByIdAndUpdate(req.body.examId, req.body);
    res.send({
      message: "Exam edited successfully",
      success: true,
    });
  } catch (error) {
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// delete exam by id

router.post("/delete-exam-by-id", authMiddleware, async (req, res) => {
  const session = await mongoose.startSession(); 
  session.startTransaction();

  try {
    const deletedExam = await Exam.findByIdAndDelete(req.body.examId).session(session);

    if (!deletedExam) {
      throw new Error("Exam not found");
    }

    const deleteReportsResult = await Report.deleteMany({ exam: req.body.examId }).session(session);

    await session.commitTransaction();
    session.endSession();

    res.send({
      message: "Exam and related reports deleted successfully",
      success: true,
      deletedReportsCount: deleteReportsResult.deletedCount, 
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();

    res.status(500).send({
      message: error.message || "Failed to delete exam and reports",
      data: error,
      success: false,
    });
  }
});


// add question to exam

router.post(
  "/add-question-to-exam",
  authMiddleware,
  upload.single("image"),
  async (req, res) => {
    try {
      // Get the uploaded image file
      const questionImage = req.file;

      let imageUrl = null;

      // Define the folder name in S3
      if (questionImage) {
        const folderName = "Questions";

        // Generate a unique filename with the original extension
        const filename = `${folderName}/${uuidv4()}-${
          questionImage.originalname
        }`;

        // S3 upload parameters
        const params = {
          Bucket: process.env.AWS_S3_BUCKET_NAME,
          Key: filename,
          Body: questionImage.buffer,
          ContentType: questionImage.mimetype || "application/octet-stream",
        };

        // Upload image to S3
        const s3Response = await s3.upload(params).promise();

        // Get the S3 image URL
        imageUrl = s3Response.Location;
      }

      // Add question to the Questions collection
      const newQuestion = new Question({
        ...req.body,
        image: imageUrl, // Include the image URL
      });

      const question = await newQuestion.save();

      // Add question to the exam
      const exam = await Exam.findById(req.body.exam);
      if (!exam) {
        return res.status(404).send({
          message: "Exam not found.",
          success: false,
        });
      }
      exam.questions.push(question._id);
      await exam.save();

      // Success response
      res.send({
        message: "Question added successfully",
        success: true,
        data: question,
      });
    } catch (error) {
      // Error response
      res.status(500).send({
        message: error.message,
        data: error,
        success: false,
      });
    }
  }
);

// edit question in exam
router.post(
  "/edit-question-in-exam",
  authMiddleware,
  upload.single("image"),
  async (req, res) => {
    try {
      const { questionId } = req.body;
      const updateData = { ...req.body };

      // Handle image upload if present
      if (req.file) {
        // Define the folder name in S3
        const folderName = "Questions";

        // Generate a unique filename with the original extension
        const filename = `${folderName}/${uuidv4()}-${req.file.originalname}`;

        // S3 upload parameters
        const params = {
          Bucket: process.env.AWS_S3_BUCKET_NAME,
          Key: filename,
          Body: req.file.buffer,
          ContentType: req.file.mimetype || "application/octet-stream",
        };

        // Upload image to S3
        const s3Response = await s3.upload(params).promise();

        // Add the image URL to update data
        updateData.image = s3Response.Location;
      } else {
        // Remove image field if no new image is uploaded
        delete updateData.image;
      }

      // Parse options if it's a string (from FormData)
      if (typeof updateData.options === "string") {
        try {
          updateData.options = JSON.parse(updateData.options);
        } catch (parseError) {
          // If parsing fails, set to null or handle as needed
          updateData.options = null;
        }
      }

      // Remove questionId from update data to prevent mongoose error
      delete updateData.questionId;

      // Update question in Questions collection
      const updatedQuestion = await Question.findByIdAndUpdate(
        questionId,
        updateData,
        { new: true } // Return the updated document
      );

      if (!updatedQuestion) {
        return res.status(404).send({
          message: "Question not found",
          success: false,
        });
      }

      res.send({
        message: "Question edited successfully",
        success: true,
        data: updatedQuestion,
      });
    } catch (error) {
      res.status(500).send({
        message: error.message,
        data: error,
        success: false,
      });
    }
  }
);

// delete question in exam
router.post("/delete-question-in-exam", authMiddleware, async (req, res) => {
  try {
    // delete question in Questions collection
    await Question.findByIdAndDelete(req.body.questionId);

    // delete question in exam
    const exam = await Exam.findById(req.body.examId);
    exam.questions = exam.questions.filter(
      (question) => question._id != req.body.questionId
    );
    await exam.save();
    res.send({
      message: "Question deleted successfully",
      success: true,
    });
  } catch (error) {}
});

// Repair endpoint to fix orphaned questions
router.post("/repair-exam-questions", authMiddleware, async (req, res) => {
  try {
    const { examId } = req.body;

    console.log("🔧 Repairing Exam Questions for ID:", examId);

    // Find the exam
    const exam = await Exam.findById(examId);
    if (!exam) {
      return res.status(404).send({
        success: false,
        message: "Exam not found"
      });
    }

    // Find all questions that belong to this exam but aren't in the exam.questions array
    const orphanedQuestions = await Question.find({
      exam: examId,
      _id: { $nin: exam.questions || [] }
    });

    console.log("Found orphaned questions:", orphanedQuestions.length);

    if (orphanedQuestions.length > 0) {
      // Add orphaned questions to the exam
      const orphanedIds = orphanedQuestions.map(q => q._id);
      exam.questions.push(...orphanedIds);
      await exam.save();

      console.log("✅ Repaired exam - added", orphanedQuestions.length, "questions");

      res.send({
        success: true,
        message: `Repaired exam: added ${orphanedQuestions.length} orphaned questions`,
        data: {
          addedQuestions: orphanedQuestions.length,
          totalQuestions: exam.questions.length
        }
      });
    } else {
      res.send({
        success: true,
        message: "No orphaned questions found - exam is already correct",
        data: {
          totalQuestions: exam.questions.length
        }
      });
    }

  } catch (error) {
    console.error("Repair error:", error);
    res.status(500).send({
      success: false,
      message: error.message
    });
  }
});

// Debug endpoint to check exam questions
router.post("/debug-exam-questions", authMiddleware, async (req, res) => {
  try {
    const { examId } = req.body;

    console.log("🔍 Debugging Exam Questions for ID:", examId);

    // 1. Check the exam
    const exam = await Exam.findById(examId);
    console.log("Exam found:", !!exam);
    console.log("Questions in exam:", exam?.questions?.length || 0);

    // 2. Check all questions for this exam
    const questionsForExam = await Question.find({ exam: examId });
    console.log("Questions in database for this exam:", questionsForExam.length);

    // 3. Check AI generations for this exam
    const { AIQuestionGeneration } = require("../models/aiQuestionGenerationModel");
    const aiGenerations = await AIQuestionGeneration.find({ examId: examId });
    console.log("AI generations found:", aiGenerations.length);

    // 4. Check for orphaned questions
    const orphanedQuestions = await Question.find({
      exam: examId,
      _id: { $nin: exam?.questions || [] }
    });
    console.log("Orphaned questions found:", orphanedQuestions.length);

    res.send({
      success: true,
      data: {
        exam: {
          found: !!exam,
          name: exam?.name,
          questionsCount: exam?.questions?.length || 0,
          questionIds: exam?.questions || []
        },
        questionsInDatabase: {
          count: questionsForExam.length,
          questions: questionsForExam.map(q => ({
            id: q._id,
            name: q.name,
            answerType: q.answerType,
            isAIGenerated: q.isAIGenerated,
            hasOptions: !!q.options,
            correctOption: q.correctOption,
            correctAnswer: q.correctAnswer
          }))
        },
        aiGenerations: {
          count: aiGenerations.length,
          generations: aiGenerations.map(gen => ({
            id: gen._id,
            status: gen.generationStatus,
            totalQuestions: gen.generatedQuestions.length,
            approvedQuestions: gen.generatedQuestions.filter(q => q.approved).length
          }))
        },
        orphanedQuestions: {
          count: orphanedQuestions.length,
          questions: orphanedQuestions.map(q => ({
            id: q._id,
            name: q.name,
            answerType: q.answerType
          }))
        }
      }
    });

  } catch (error) {
    console.error("Debug error:", error);
    res.status(500).send({
      success: false,
      message: error.message
    });
  }
});

// Get exam statistics (pass count, total attempts, etc.)
router.get("/stats/:id", authMiddleware, async (req, res) => {
  try {
    const examId = req.params.id;
    const Report = require("../models/reportModel");

    // Get all reports for this exam
    const reports = await Report.find({ exam: examId });

    // Calculate statistics
    const totalAttempts = reports.length;
    const passedCount = reports.filter(report => report.result?.verdict === 'Pass').length;
    const failedCount = totalAttempts - passedCount;
    const passRate = totalAttempts > 0 ? Math.round((passedCount / totalAttempts) * 100) : 0;

    // Get unique users who passed
    const uniquePassedUsers = new Set();
    reports.forEach(report => {
      if (report.result?.verdict === 'Pass') {
        uniquePassedUsers.add(report.user.toString());
      }
    });

    const stats = {
      totalAttempts,
      passedCount,
      failedCount,
      passRate,
      uniquePassedUsers: uniquePassedUsers.size
    };

    res.send({
      message: "Exam statistics retrieved successfully",
      success: true,
      data: stats
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

module.exports = router;
