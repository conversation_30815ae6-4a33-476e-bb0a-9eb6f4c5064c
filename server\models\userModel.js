const mongoose = require("mongoose");
const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    school: {
      type: String,
      required: true,
    },
    level: {
      type: String,
      enum: ["primary", "secondary", "advance", "Primary", "Secondary", "Advance"],
      default: "Primary",
      required: false,
    },
    class: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
    },
    phoneNumber: {
      type: String,
      required: false, // Made optional to fix validation issues
      unique: true,
      sparse: true, // Allow multiple null values
    },
    paymentRequired: {
      type: Boolean,
      required: false,
      default: false,
    },
    // Enhanced subscription tracking
    subscriptionStatus: {
      type: String,
      enum: ["free", "premium", "active", "expired"],
      default: "free",
    },
    subscriptionStartDate: {
      type: Date,
    },
    subscriptionEndDate: {
      type: Date,
    },
    subscriptionPlan: {
      type: String,
      enum: ["basic", "premium", "pro"],
    },
    profileImage: {
      type: String,
    },
    // User statistics for ranking
    totalQuizzesTaken: {
      type: Number,
      default: 0,
    },
    totalPointsEarned: {
      type: Number,
      default: 0,
    },
    averageScore: {
      type: Number,
      default: 0,
    },
    bestStreak: {
      type: Number,
      default: 0,
    },
    currentStreak: {
      type: Number,
      default: 0,
    },

    // New XP System Fields
    totalXP: {
      type: Number,
      default: 0,
    },
    currentLevel: {
      type: Number,
      default: 1,
    },
    xpToNextLevel: {
      type: Number,
      default: 100,
    },
    lifetimeXP: {
      type: Number,
      default: 0,
    },
    seasonXP: {
      type: Number,
      default: 0,
    },
    currentSeason: {
      type: String,
      default: "2024-S1",
    },

    // Enhanced Achievement System
    achievements: [{
      id: {
        type: String,
        required: true,
      },
      name: {
        type: String,
        required: true,
      },
      description: String,
      icon: String,
      xpReward: {
        type: Number,
        default: 0,
      },
      rarity: {
        type: String,
        enum: ["common", "rare", "epic", "legendary"],
        default: "common",
      },
      category: {
        type: String,
        enum: ["learning", "streak", "subject", "social", "special"],
        default: "learning",
      },
      earnedAt: {
        type: Date,
        default: Date.now,
      },
      subject: String, // For subject-specific achievements
      metadata: Object, // Additional achievement data
    }],

    // Level Progress Tracking
    levelHistory: [{
      level: Number,
      reachedAt: {
        type: Date,
        default: Date.now,
      },
      xpAtLevel: Number,
    }],

    // Enhanced User Activity Tracking
    activityTracking: {
      // Login Streaks
      dailyLoginStreak: {
        type: Number,
        default: 0,
      },
      bestLoginStreak: {
        type: Number,
        default: 0,
      },
      lastLoginDate: {
        type: Date,
        default: Date.now,
      },
      totalLoginDays: {
        type: Number,
        default: 0,
      },

      // Quiz Activity Streaks
      quizCompletionStreak: {
        type: Number,
        default: 0,
      },
      bestQuizStreak: {
        type: Number,
        default: 0,
      },
      perfectScoreStreak: {
        type: Number,
        default: 0,
      },
      bestPerfectStreak: {
        type: Number,
        default: 0,
      },

      // Study Time Tracking
      totalStudyTimeMinutes: {
        type: Number,
        default: 0,
      },
      averageStudySessionMinutes: {
        type: Number,
        default: 0,
      },
      longestStudySessionMinutes: {
        type: Number,
        default: 0,
      },

      // Performance Metrics
      quizzesPassed: {
        type: Number,
        default: 0,
      },
      quizzesFailed: {
        type: Number,
        default: 0,
      },
      totalQuestionsAnswered: {
        type: Number,
        default: 0,
      },
      totalCorrectAnswers: {
        type: Number,
        default: 0,
      },

      // Subject-wise Performance
      subjectPerformance: [{
        subject: {
          type: String,
          required: true,
        },
        quizzesTaken: {
          type: Number,
          default: 0,
        },
        quizzesPassed: {
          type: Number,
          default: 0,
        },
        averageScore: {
          type: Number,
          default: 0,
        },
        totalXP: {
          type: Number,
          default: 0,
        },
        streak: {
          type: Number,
          default: 0,
        },
        bestStreak: {
          type: Number,
          default: 0,
        },
        lastAttempt: {
          type: Date,
        },
      }],

      // Weekly/Monthly Activity
      weeklyStats: {
        currentWeek: {
          type: String, // Format: "2024-W01"
          default: "",
        },
        quizzesThisWeek: {
          type: Number,
          default: 0,
        },
        xpThisWeek: {
          type: Number,
          default: 0,
        },
        studyTimeThisWeek: {
          type: Number,
          default: 0,
        },
      },

      monthlyStats: {
        currentMonth: {
          type: String, // Format: "2024-01"
          default: "",
        },
        quizzesThisMonth: {
          type: Number,
          default: 0,
        },
        xpThisMonth: {
          type: Number,
          default: 0,
        },
        studyTimeThisMonth: {
          type: Number,
          default: 0,
        },
      },
    },

    // XP Statistics (Enhanced)
    xpStats: {
      dailyXP: {
        type: Number,
        default: 0,
      },
      weeklyXP: {
        type: Number,
        default: 0,
      },
      monthlyXP: {
        type: Number,
        default: 0,
      },
      lastXPGain: {
        type: Date,
        default: Date.now,
      },
      averageXPPerQuiz: {
        type: Number,
        default: 0,
      },
      bestXPGain: {
        type: Number,
        default: 0,
      },
      xpFromQuizzes: {
        type: Number,
        default: 0,
      },
      xpFromStreaks: {
        type: Number,
        default: 0,
      },
      xpFromAchievements: {
        type: Number,
        default: 0,
      },
      xpFromConsistency: {
        type: Number,
        default: 0,
      },
    },
    password: {
      type: String,
      required: true,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
    isBlocked: {
      type: Boolean,
      default: false,
    }
  },
  // Online status tracking
  isOnline: {
    type: Boolean,
    default: false,
  },
  lastSeen: {
    type: Date,
    default: Date.now,
  },
  lastActivity: {
    type: Date,
    default: Date.now,
  },
  },
  {
    timestamps: true,
  }
);

const userModel = mongoose.model("users", userSchema);

module.exports = userModel;