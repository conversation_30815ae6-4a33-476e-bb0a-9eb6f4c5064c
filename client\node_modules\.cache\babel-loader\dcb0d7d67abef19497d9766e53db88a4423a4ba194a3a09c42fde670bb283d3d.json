{"ast": null, "code": "import axiosInstance from \"./index\";\n\n// Generate questions using AI\nexport const generateQuestions = async payload => {\n  try {\n    console.log(\"🔗 Making API call to generate questions...\");\n    const response = await axiosInstance.post(\"/api/ai-questions/generate-questions\", payload, {\n      timeout: 600000 // 10 minutes timeout specifically for AI generation\n    });\n\n    console.log(\"✅ API call successful:\", response.data);\n    return response.data;\n  } catch (error) {\n    console.error(\"❌ API call failed:\", error);\n    if (error.response) {\n      console.error(\"📊 Error response:\", error.response.data);\n      return error.response.data;\n    } else if (error.request) {\n      console.error(\"📡 Network error:\", error.request);\n      return {\n        success: false,\n        message: \"Network error - please check your connection\"\n      };\n    } else {\n      console.error(\"⚠️ Request setup error:\", error.message);\n      return {\n        success: false,\n        message: error.message\n      };\n    }\n  }\n};\n\n// Get generation history\nexport const getGenerationHistory = async (params = {}) => {\n  try {\n    const response = await axiosInstance.get(\"/api/ai-questions/generation-history\", {\n      params\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get specific generation details\nexport const getGenerationDetails = async generationId => {\n  try {\n    const response = await axiosInstance.get(`/api/ai-questions/generation/${generationId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Approve generated questions\nexport const approveQuestions = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-questions/approve-questions\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Preview generated questions\nexport const previewQuestions = async generationId => {\n  try {\n    const response = await axiosInstance.get(`/api/ai-questions/preview/${generationId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get available subjects for a level (now uses syllabus-based subjects)\nexport const getSubjectsForLevel = async (level, className = null) => {\n  try {\n    console.log(`🔍 Fetching subjects for level: ${level}, class: ${className}`);\n\n    // First try to get subjects from uploaded syllabuses\n    const queryParams = className ? `?class=${className}` : '';\n    console.log(`📚 Trying syllabus endpoint: /api/syllabus/subjects/${level}${queryParams}`);\n    const syllabusResponse = await axiosInstance.get(`/api/syllabus/subjects/${level}${queryParams}`);\n    console.log('📚 Syllabus response:', syllabusResponse.data);\n    if (syllabusResponse.data.success && syllabusResponse.data.data.length > 0) {\n      console.log(`✅ Using syllabus-based subjects for ${level}:`, syllabusResponse.data.data);\n      return syllabusResponse.data;\n    }\n\n    // Fallback to hardcoded subjects if no syllabuses found\n    console.log(`📖 No syllabus subjects found, falling back to hardcoded subjects for ${level}`);\n    const fallbackResponse = await axiosInstance.get(`/api/ai-questions/subjects/${level}`);\n    console.log('📖 Fallback response:', fallbackResponse.data);\n    return fallbackResponse.data;\n  } catch (error) {\n    var _error$response;\n    console.error('❌ Error fetching subjects:', error);\n    console.error('Error details:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n\n    // Try fallback on error\n    try {\n      console.log(`🔄 Trying fallback due to error...`);\n      const fallbackResponse = await axiosInstance.get(`/api/ai-questions/subjects/${level}`);\n      console.log('🔄 Fallback response:', fallbackResponse.data);\n      return fallbackResponse.data;\n    } catch (fallbackError) {\n      console.error('❌ Fallback also failed:', fallbackError);\n      return {\n        success: false,\n        message: 'Failed to fetch subjects',\n        data: []\n      };\n    }\n  }\n};\n\n// Get Tanzania syllabus topics for level, class, and subject\nexport const getSyllabusTopics = async (level, className, subject) => {\n  try {\n    // First try to get topics from uploaded syllabuses\n    const syllabusResponse = await axiosInstance.get(`/api/syllabus/ai-content/${level}/${className}/${subject}`);\n    if (syllabusResponse.data.success) {\n      console.log(`📚 Using syllabus-based topics for ${level} Class ${className} ${subject}`);\n      // Convert syllabus format to expected format\n      const topics = Object.keys(syllabusResponse.data.data.topics || {}).map(topicName => ({\n        topicName,\n        subtopics: syllabusResponse.data.data.topics[topicName].subtopics || [],\n        difficulty: syllabusResponse.data.data.topics[topicName].difficulty || 'medium'\n      }));\n      return {\n        success: true,\n        data: {\n          level,\n          class: className,\n          subject,\n          topics\n        }\n      };\n    }\n\n    // Fallback to hardcoded topics if no syllabus found\n    console.log(`📖 Falling back to hardcoded topics for ${level} Class ${className} ${subject}`);\n    const fallbackResponse = await axiosInstance.get(`/api/ai-questions/syllabus-topics/${level}/${className}/${subject}`);\n    return fallbackResponse.data;\n  } catch (error) {\n    var _error$response2;\n    console.error('Error fetching syllabus topics:', error);\n    return ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || {\n      success: false,\n      message: 'Failed to fetch topics'\n    };\n  }\n};\n\n// Generate exam name\nexport const generateExamName = async (level, className, subjects) => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-questions/generate-exam-name\", {\n      level,\n      className,\n      subjects\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};", "map": {"version": 3, "names": ["axiosInstance", "generateQuestions", "payload", "console", "log", "response", "post", "timeout", "data", "error", "request", "success", "message", "getGenerationHistory", "params", "get", "getGenerationDetails", "generationId", "approveQuestions", "previewQuestions", "getSubjectsForLevel", "level", "className", "queryParams", "syllabusResponse", "length", "fallbackResponse", "_error$response", "fallback<PERSON><PERSON>r", "getSyllabusTopics", "subject", "topics", "Object", "keys", "map", "topicName", "subtopics", "difficulty", "class", "_error$response2", "generateExamName", "subjects"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/aiQuestions.js"], "sourcesContent": ["import axiosInstance from \"./index\";\n\n// Generate questions using AI\nexport const generateQuestions = async (payload) => {\n  try {\n    console.log(\"🔗 Making API call to generate questions...\");\n    const response = await axiosInstance.post(\"/api/ai-questions/generate-questions\", payload, {\n      timeout: 600000, // 10 minutes timeout specifically for AI generation\n    });\n    console.log(\"✅ API call successful:\", response.data);\n    return response.data;\n  } catch (error) {\n    console.error(\"❌ API call failed:\", error);\n\n    if (error.response) {\n      console.error(\"📊 Error response:\", error.response.data);\n      return error.response.data;\n    } else if (error.request) {\n      console.error(\"📡 Network error:\", error.request);\n      return { success: false, message: \"Network error - please check your connection\" };\n    } else {\n      console.error(\"⚠️ Request setup error:\", error.message);\n      return { success: false, message: error.message };\n    }\n  }\n};\n\n// Get generation history\nexport const getGenerationHistory = async (params = {}) => {\n  try {\n    const response = await axiosInstance.get(\"/api/ai-questions/generation-history\", { params });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get specific generation details\nexport const getGenerationDetails = async (generationId) => {\n  try {\n    const response = await axiosInstance.get(`/api/ai-questions/generation/${generationId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Approve generated questions\nexport const approveQuestions = async (payload) => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-questions/approve-questions\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Preview generated questions\nexport const previewQuestions = async (generationId) => {\n  try {\n    const response = await axiosInstance.get(`/api/ai-questions/preview/${generationId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get available subjects for a level (now uses syllabus-based subjects)\nexport const getSubjectsForLevel = async (level, className = null) => {\n  try {\n    console.log(`🔍 Fetching subjects for level: ${level}, class: ${className}`);\n\n    // First try to get subjects from uploaded syllabuses\n    const queryParams = className ? `?class=${className}` : '';\n    console.log(`📚 Trying syllabus endpoint: /api/syllabus/subjects/${level}${queryParams}`);\n\n    const syllabusResponse = await axiosInstance.get(`/api/syllabus/subjects/${level}${queryParams}`);\n    console.log('📚 Syllabus response:', syllabusResponse.data);\n\n    if (syllabusResponse.data.success && syllabusResponse.data.data.length > 0) {\n      console.log(`✅ Using syllabus-based subjects for ${level}:`, syllabusResponse.data.data);\n      return syllabusResponse.data;\n    }\n\n    // Fallback to hardcoded subjects if no syllabuses found\n    console.log(`📖 No syllabus subjects found, falling back to hardcoded subjects for ${level}`);\n    const fallbackResponse = await axiosInstance.get(`/api/ai-questions/subjects/${level}`);\n    console.log('📖 Fallback response:', fallbackResponse.data);\n    return fallbackResponse.data;\n  } catch (error) {\n    console.error('❌ Error fetching subjects:', error);\n    console.error('Error details:', error.response?.data);\n\n    // Try fallback on error\n    try {\n      console.log(`🔄 Trying fallback due to error...`);\n      const fallbackResponse = await axiosInstance.get(`/api/ai-questions/subjects/${level}`);\n      console.log('🔄 Fallback response:', fallbackResponse.data);\n      return fallbackResponse.data;\n    } catch (fallbackError) {\n      console.error('❌ Fallback also failed:', fallbackError);\n      return { success: false, message: 'Failed to fetch subjects', data: [] };\n    }\n  }\n};\n\n// Get Tanzania syllabus topics for level, class, and subject\nexport const getSyllabusTopics = async (level, className, subject) => {\n  try {\n    // First try to get topics from uploaded syllabuses\n    const syllabusResponse = await axiosInstance.get(`/api/syllabus/ai-content/${level}/${className}/${subject}`);\n\n    if (syllabusResponse.data.success) {\n      console.log(`📚 Using syllabus-based topics for ${level} Class ${className} ${subject}`);\n      // Convert syllabus format to expected format\n      const topics = Object.keys(syllabusResponse.data.data.topics || {}).map(topicName => ({\n        topicName,\n        subtopics: syllabusResponse.data.data.topics[topicName].subtopics || [],\n        difficulty: syllabusResponse.data.data.topics[topicName].difficulty || 'medium'\n      }));\n\n      return {\n        success: true,\n        data: {\n          level,\n          class: className,\n          subject,\n          topics\n        }\n      };\n    }\n\n    // Fallback to hardcoded topics if no syllabus found\n    console.log(`📖 Falling back to hardcoded topics for ${level} Class ${className} ${subject}`);\n    const fallbackResponse = await axiosInstance.get(`/api/ai-questions/syllabus-topics/${level}/${className}/${subject}`);\n    return fallbackResponse.data;\n  } catch (error) {\n    console.error('Error fetching syllabus topics:', error);\n    return error.response?.data || { success: false, message: 'Failed to fetch topics' };\n  }\n};\n\n// Generate exam name\nexport const generateExamName = async (level, className, subjects) => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-questions/generate-exam-name\", {\n      level,\n      className,\n      subjects\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,SAAS;;AAEnC;AACA,OAAO,MAAMC,iBAAiB,GAAG,MAAOC,OAAO,IAAK;EAClD,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC1D,MAAMC,QAAQ,GAAG,MAAML,aAAa,CAACM,IAAI,CAAC,sCAAsC,EAAEJ,OAAO,EAAE;MACzFK,OAAO,EAAE,MAAM,CAAE;IACnB,CAAC,CAAC;;IACFJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAACG,IAAI,CAAC;IACpD,OAAOH,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAE1C,IAAIA,KAAK,CAACJ,QAAQ,EAAE;MAClBF,OAAO,CAACM,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAACJ,QAAQ,CAACG,IAAI,CAAC;MACxD,OAAOC,KAAK,CAACJ,QAAQ,CAACG,IAAI;IAC5B,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,EAAE;MACxBP,OAAO,CAACM,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAACC,OAAO,CAAC;MACjD,OAAO;QAAEC,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAA+C,CAAC;IACpF,CAAC,MAAM;MACLT,OAAO,CAACM,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAACG,OAAO,CAAC;MACvD,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAEH,KAAK,CAACG;MAAQ,CAAC;IACnD;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,oBAAoB,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;EACzD,IAAI;IACF,MAAMT,QAAQ,GAAG,MAAML,aAAa,CAACe,GAAG,CAAC,sCAAsC,EAAE;MAAED;IAAO,CAAC,CAAC;IAC5F,OAAOT,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACJ,QAAQ,CAACG,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMQ,oBAAoB,GAAG,MAAOC,YAAY,IAAK;EAC1D,IAAI;IACF,MAAMZ,QAAQ,GAAG,MAAML,aAAa,CAACe,GAAG,CAAE,gCAA+BE,YAAa,EAAC,CAAC;IACxF,OAAOZ,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACJ,QAAQ,CAACG,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,gBAAgB,GAAG,MAAOhB,OAAO,IAAK;EACjD,IAAI;IACF,MAAMG,QAAQ,GAAG,MAAML,aAAa,CAACM,IAAI,CAAC,qCAAqC,EAAEJ,OAAO,CAAC;IACzF,OAAOG,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACJ,QAAQ,CAACG,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMW,gBAAgB,GAAG,MAAOF,YAAY,IAAK;EACtD,IAAI;IACF,MAAMZ,QAAQ,GAAG,MAAML,aAAa,CAACe,GAAG,CAAE,6BAA4BE,YAAa,EAAC,CAAC;IACrF,OAAOZ,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACJ,QAAQ,CAACG,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,mBAAmB,GAAG,MAAAA,CAAOC,KAAK,EAAEC,SAAS,GAAG,IAAI,KAAK;EACpE,IAAI;IACFnB,OAAO,CAACC,GAAG,CAAE,mCAAkCiB,KAAM,YAAWC,SAAU,EAAC,CAAC;;IAE5E;IACA,MAAMC,WAAW,GAAGD,SAAS,GAAI,UAASA,SAAU,EAAC,GAAG,EAAE;IAC1DnB,OAAO,CAACC,GAAG,CAAE,uDAAsDiB,KAAM,GAAEE,WAAY,EAAC,CAAC;IAEzF,MAAMC,gBAAgB,GAAG,MAAMxB,aAAa,CAACe,GAAG,CAAE,0BAAyBM,KAAM,GAAEE,WAAY,EAAC,CAAC;IACjGpB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoB,gBAAgB,CAAChB,IAAI,CAAC;IAE3D,IAAIgB,gBAAgB,CAAChB,IAAI,CAACG,OAAO,IAAIa,gBAAgB,CAAChB,IAAI,CAACA,IAAI,CAACiB,MAAM,GAAG,CAAC,EAAE;MAC1EtB,OAAO,CAACC,GAAG,CAAE,uCAAsCiB,KAAM,GAAE,EAAEG,gBAAgB,CAAChB,IAAI,CAACA,IAAI,CAAC;MACxF,OAAOgB,gBAAgB,CAAChB,IAAI;IAC9B;;IAEA;IACAL,OAAO,CAACC,GAAG,CAAE,yEAAwEiB,KAAM,EAAC,CAAC;IAC7F,MAAMK,gBAAgB,GAAG,MAAM1B,aAAa,CAACe,GAAG,CAAE,8BAA6BM,KAAM,EAAC,CAAC;IACvFlB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsB,gBAAgB,CAAClB,IAAI,CAAC;IAC3D,OAAOkB,gBAAgB,CAAClB,IAAI;EAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAkB,eAAA;IACdxB,OAAO,CAACM,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClDN,OAAO,CAACM,KAAK,CAAC,gBAAgB,GAAAkB,eAAA,GAAElB,KAAK,CAACJ,QAAQ,cAAAsB,eAAA,uBAAdA,eAAA,CAAgBnB,IAAI,CAAC;;IAErD;IACA,IAAI;MACFL,OAAO,CAACC,GAAG,CAAE,oCAAmC,CAAC;MACjD,MAAMsB,gBAAgB,GAAG,MAAM1B,aAAa,CAACe,GAAG,CAAE,8BAA6BM,KAAM,EAAC,CAAC;MACvFlB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsB,gBAAgB,CAAClB,IAAI,CAAC;MAC3D,OAAOkB,gBAAgB,CAAClB,IAAI;IAC9B,CAAC,CAAC,OAAOoB,aAAa,EAAE;MACtBzB,OAAO,CAACM,KAAK,CAAC,yBAAyB,EAAEmB,aAAa,CAAC;MACvD,OAAO;QAAEjB,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE,0BAA0B;QAAEJ,IAAI,EAAE;MAAG,CAAC;IAC1E;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMqB,iBAAiB,GAAG,MAAAA,CAAOR,KAAK,EAAEC,SAAS,EAAEQ,OAAO,KAAK;EACpE,IAAI;IACF;IACA,MAAMN,gBAAgB,GAAG,MAAMxB,aAAa,CAACe,GAAG,CAAE,4BAA2BM,KAAM,IAAGC,SAAU,IAAGQ,OAAQ,EAAC,CAAC;IAE7G,IAAIN,gBAAgB,CAAChB,IAAI,CAACG,OAAO,EAAE;MACjCR,OAAO,CAACC,GAAG,CAAE,sCAAqCiB,KAAM,UAASC,SAAU,IAAGQ,OAAQ,EAAC,CAAC;MACxF;MACA,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACT,gBAAgB,CAAChB,IAAI,CAACA,IAAI,CAACuB,MAAM,IAAI,CAAC,CAAC,CAAC,CAACG,GAAG,CAACC,SAAS,KAAK;QACpFA,SAAS;QACTC,SAAS,EAAEZ,gBAAgB,CAAChB,IAAI,CAACA,IAAI,CAACuB,MAAM,CAACI,SAAS,CAAC,CAACC,SAAS,IAAI,EAAE;QACvEC,UAAU,EAAEb,gBAAgB,CAAChB,IAAI,CAACA,IAAI,CAACuB,MAAM,CAACI,SAAS,CAAC,CAACE,UAAU,IAAI;MACzE,CAAC,CAAC,CAAC;MAEH,OAAO;QACL1B,OAAO,EAAE,IAAI;QACbH,IAAI,EAAE;UACJa,KAAK;UACLiB,KAAK,EAAEhB,SAAS;UAChBQ,OAAO;UACPC;QACF;MACF,CAAC;IACH;;IAEA;IACA5B,OAAO,CAACC,GAAG,CAAE,2CAA0CiB,KAAM,UAASC,SAAU,IAAGQ,OAAQ,EAAC,CAAC;IAC7F,MAAMJ,gBAAgB,GAAG,MAAM1B,aAAa,CAACe,GAAG,CAAE,qCAAoCM,KAAM,IAAGC,SAAU,IAAGQ,OAAQ,EAAC,CAAC;IACtH,OAAOJ,gBAAgB,CAAClB,IAAI;EAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAA8B,gBAAA;IACdpC,OAAO,CAACM,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,OAAO,EAAA8B,gBAAA,GAAA9B,KAAK,CAACJ,QAAQ,cAAAkC,gBAAA,uBAAdA,gBAAA,CAAgB/B,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAyB,CAAC;EACtF;AACF,CAAC;;AAED;AACA,OAAO,MAAM4B,gBAAgB,GAAG,MAAAA,CAAOnB,KAAK,EAAEC,SAAS,EAAEmB,QAAQ,KAAK;EACpE,IAAI;IACF,MAAMpC,QAAQ,GAAG,MAAML,aAAa,CAACM,IAAI,CAAC,sCAAsC,EAAE;MAChFe,KAAK;MACLC,SAAS;MACTmB;IACF,CAAC,CAAC;IACF,OAAOpC,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACJ,QAAQ,CAACG,IAAI;EAC5B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}