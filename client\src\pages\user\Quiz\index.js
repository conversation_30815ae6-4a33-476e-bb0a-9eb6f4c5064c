import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import {
  Tb<PERSON><PERSON>ch,
  Tb<PERSON>ilter,
  TbClock,
  TbQuestionMark,
  TbTrophy,
  TbPlayerPlay,
  TbBrain,
  TbTarget,
  TbCheck,
  TbX,
  TbStar,
  TbHome,
  TbBolt
} from 'react-icons/tb';
import { getAllExams } from '../../../apicalls/exams';
import { getAllReportsByUser } from '../../../apicalls/reports';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import QuizCard from '../../../components/modern/QuizCard';
import './responsive.css';
import './style.css';

const Quiz = () => {
  const [exams, setExams] = useState([]);
  const [filteredExams, setFilteredExams] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [userResults, setUserResults] = useState({});
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  const getUserResults = async () => {
    try {
      if (!user?._id) return;

      const response = await getAllReportsByUser({ userId: user._id });

      if (response.success) {
        const resultsMap = {};
        response.data.forEach(report => {
          const examId = report.exam?._id;
          if (!examId || !report.result) return;

          // Extract data from the result object
          const result = report.result;

          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {
            resultsMap[examId] = {
              verdict: result.verdict,
              percentage: result.percentage,
              correctAnswers: result.correctAnswers,
              wrongAnswers: result.wrongAnswers,
              totalQuestions: result.totalQuestions,
              obtainedMarks: result.obtainedMarks,
              totalMarks: result.totalMarks,
              score: result.score,
              points: result.points,
              xpEarned: result.xpEarned || result.points || result.xpGained || 0,
              timeTaken: report.timeTaken,
              completedAt: report.createdAt,
            };
          }
        });
        setUserResults(resultsMap);
      }
    } catch (error) {
      console.error('Error fetching user results:', error);
    }
  };

  useEffect(() => {
    const getExams = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getAllExams();
        dispatch(HideLoading());

        if (response.success) {
          // Filter exams by user's level
          const userLevelExams = response.data.filter(exam => {
            if (!exam.level || !user.level) return false;
            return exam.level.toLowerCase() === user.level.toLowerCase();
          });

          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
          setExams(sortedExams);

          // Set default class filter to user's class
          if (user?.class) {
            setSelectedClass(String(user.class));
          }
        } else {
          message.error(response.message);
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
      } finally {
        setLoading(false);
      }
    };

    getExams();
    getUserResults();
  }, [dispatch, user?.level, user?.class, user?._id]);

  // Real-time updates for quiz completion
  useEffect(() => {
    // Listen for real-time updates from quiz completion
    const handleRankingUpdate = () => {
      console.log('🔄 Quiz listing - refreshing data after quiz completion...');
      getUserResults(); // Refresh user results to show updated XP
    };

    // Listen for window focus to refresh data when returning from quiz
    const handleWindowFocus = () => {
      console.log('🎯 Quiz listing - window focused, refreshing data...');
      getUserResults();
    };

    window.addEventListener('rankingUpdate', handleRankingUpdate);
    window.addEventListener('focus', handleWindowFocus);

    return () => {
      window.removeEventListener('rankingUpdate', handleRankingUpdate);
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, []);

  useEffect(() => {
    let filtered = exams;
    if (searchTerm) {
      filtered = filtered.filter(exam =>
        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    if (selectedClass) {
      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));
    }
    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    setFilteredExams(filtered);
  }, [exams, searchTerm, selectedClass]);

  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();

  const handleQuizStart = (quiz) => {
    if (!quiz || !quiz._id) {
      message.error('Invalid quiz selected. Please try again.');
      return;
    }
    navigate(`/quiz/${quiz._id}/play`);
  };

  const handleQuizView = (quiz) => {
    if (!quiz || !quiz._id) {
      message.error('Invalid quiz selected. Please try again.');
      return;
    }
    // Check if user has attempted this quiz
    const userResult = userResults[quiz._id];
    if (!userResult) {
      message.info('You need to attempt this quiz first to view results.');
      return;
    }
    navigate(`/quiz/${quiz._id}/result`);
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading quizzes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-responsive">

      <div className="container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8 sm:mb-12"
        >
          <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg">
            <TbBrain className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
          </div>
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            Challenge Your Brain, Beat the Rest
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4">
            Test your knowledge with our comprehensive quizzes designed for You!
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>{filteredExams.length} Available Quizzes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span>Level: {user?.level || 'All Levels'}</span>
            </div>
          </div>
        </motion.div>

        {/* Search and Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="max-w-4xl mx-auto mb-8 sm:mb-12"
        >
          <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                  <TbSearch className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search quizzes by name or subject..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base"
                />
              </div>
              <div className="sm:w-48 md:w-64">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                    <TbFilter className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  </div>
                  <select
                    value={selectedClass}
                    onChange={(e) => setSelectedClass(e.target.value)}
                    className="block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base"
                  >
                    <option value="">All Classes</option>
                    {availableClasses.map((className) => (
                      <option key={className} value={className}>Class {className}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quiz Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="quiz-listing-content"
        >
          {filteredExams.length === 0 ? (
            <div className="text-center py-12 sm:py-16">
              <div className="bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto">
                <TbTarget className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">No Quizzes Found</h3>
                <p className="text-gray-600 text-sm sm:text-base">
                  {searchTerm || selectedClass
                    ? "Try adjusting your search or filter criteria."
                    : "No quizzes are available for your level at the moment."
                  }
                </p>
              </div>
            </div>
          ) : (
            <div className="quiz-grid-container">
              {filteredExams.map((quiz, index) => (
                <motion.div
                  key={quiz._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: Math.min(index * 0.1, 0.8) }}
                  className="h-full"
                >
                  <QuizCard
                    quiz={quiz}
                    userResult={userResults[quiz._id]}
                    showResults={true}
                    onStart={handleQuizStart}
                    onView={() => handleQuizView(quiz)}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default Quiz;
