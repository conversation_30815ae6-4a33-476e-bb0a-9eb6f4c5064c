{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\AIQuestionGeneration\\\\AutoGenerateExamModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { Modal, Form, Row, Col, Input, Select, InputNumber, Button, message, Alert, Divider } from \"antd\";\nimport { FaRobot, FaMagic } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addExam } from \"../../../apicalls/exams\";\nimport { generateExamName } from \"../../../apicalls/aiQuestions\";\nimport { getSubjectsForLevel } from \"../../../apicalls/aiQuestions\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction AutoGenerateExamModal({\n  visible,\n  onCancel,\n  onSuccess,\n  prefilledData = {}\n}) {\n  _s();\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [level, setLevel] = useState(prefilledData.level || \"\");\n  // Remove unused selectedSubjects state - cleaned up\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [autoGeneratedName, setAutoGeneratedName] = useState(\"\");\n  const [isGeneratingName, setIsGeneratingName] = useState(false);\n  const [passRate, setPassRate] = useState(0);\n  const calculatePassRate = useCallback(values => {\n    const totalMarks = (values === null || values === void 0 ? void 0 : values.totalMarks) || 0;\n    const passingMarks = (values === null || values === void 0 ? void 0 : values.passingMarks) || 0;\n    if (totalMarks > 0 && passingMarks > 0) {\n      return Math.round(passingMarks / totalMarks * 100);\n    }\n    return 0;\n  }, []);\n  const handleLevelChange = useCallback(async selectedLevel => {\n    console.log(`🎯 Level changed to: ${selectedLevel}`);\n    setLevel(selectedLevel);\n    form.setFieldsValue({\n      class: undefined,\n      category: \"\"\n    });\n\n    // First set hardcoded subjects as fallback immediately\n    let fallbackSubjects = [];\n    switch (selectedLevel) {\n      case \"primary\":\n        fallbackSubjects = [...primarySubjects, \"science and technology\"]; // Add known syllabus subject\n        break;\n      case \"secondary\":\n        fallbackSubjects = secondarySubjects;\n        break;\n      case \"advance\":\n        fallbackSubjects = advanceSubjects;\n        break;\n      default:\n        fallbackSubjects = [];\n    }\n    setAvailableSubjects(fallbackSubjects);\n    console.log(`🔄 Set initial subjects for ${selectedLevel}:`, fallbackSubjects);\n\n    // Then try to fetch syllabus-based subjects\n    try {\n      console.log(`🔍 Fetching syllabus subjects for level: ${selectedLevel}`);\n      const response = await getSubjectsForLevel(selectedLevel);\n      console.log(`📊 Syllabus response received:`, response);\n      if (response.success && response.data && response.data.length > 0) {\n        // Combine syllabus subjects with hardcoded ones (remove duplicates)\n        const combinedSubjects = [...new Set([...response.data, ...fallbackSubjects])];\n        setAvailableSubjects(combinedSubjects);\n        console.log(`✅ Updated with syllabus subjects for ${selectedLevel}:`, combinedSubjects);\n      } else {\n        console.warn(`⚠️ No syllabus subjects found for ${selectedLevel}, using fallback`);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching syllabus subjects:', error);\n      console.log(`🔄 Keeping fallback subjects for ${selectedLevel}`);\n    }\n  }, [form]);\n  const handleFormValuesChange = useCallback((changedValues, allValues) => {\n    if (changedValues.totalMarks !== undefined || changedValues.passingMarks !== undefined) {\n      setPassRate(calculatePassRate(allValues));\n    }\n  }, [calculatePassRate]);\n  useEffect(() => {\n    if (visible) {\n      // Reset form when modal opens\n      form.resetFields();\n      setLevel(prefilledData.level || \"\");\n\n      // Set initial values if provided\n      if (prefilledData.level) {\n        var _prefilledData$subjec;\n        form.setFieldsValue({\n          level: prefilledData.level,\n          class: prefilledData.class,\n          category: ((_prefilledData$subjec = prefilledData.subjects) === null || _prefilledData$subjec === void 0 ? void 0 : _prefilledData$subjec[0]) || \"\"\n        });\n        handleLevelChange(prefilledData.level);\n      }\n\n      // Initialize pass rate with default values\n      setPassRate(calculatePassRate({\n        totalMarks: 100,\n        passingMarks: 50\n      }));\n    }\n  }, [visible, prefilledData, form, handleLevelChange, calculatePassRate]);\n  const handleAutoGenerateName = async () => {\n    const currentValues = form.getFieldsValue();\n    const {\n      level: formLevel,\n      class: formClass,\n      category\n    } = currentValues;\n    if (!formLevel || !formClass || !category) {\n      message.warning(\"Please select level, class, and category first\");\n      return;\n    }\n    try {\n      setIsGeneratingName(true);\n      const response = await generateExamName(formLevel, formClass, [category]);\n      if (response.success) {\n        setAutoGeneratedName(response.data.examName);\n        form.setFieldsValue({\n          name: response.data.examName\n        });\n        message.success(\"Exam name generated successfully!\");\n      } else {\n        message.error(\"Failed to generate exam name\");\n      }\n    } catch (error) {\n      message.error(\"Error generating exam name\");\n    } finally {\n      setIsGeneratingName(false);\n    }\n  };\n  const onFinish = async values => {\n    try {\n      dispatch(ShowLoading());\n\n      // Prepare exam data following the same structure as manual creation\n      const examData = {\n        name: values.name,\n        duration: values.duration,\n        level: values.level,\n        category: values.category,\n        class: values.class,\n        totalMarks: values.totalMarks,\n        passingMarks: values.passingMarks,\n        description: values.description || `Auto-generated exam for ${values.category} - ${values.level} Level Class ${values.class}`,\n        isPublic: false,\n        // Default to private\n        questions: [] // Start with empty questions array\n      };\n\n      const response = await addExam(examData);\n      if (response.success) {\n        message.success(\"Exam created successfully!\");\n        // Ensure we have valid exam data before passing it back\n        if (response.data && response.data._id) {\n          onSuccess(response.data); // Pass the created exam data back\n        } else {\n          // If no data returned, create a minimal exam object for the UI\n          const fallbackExam = {\n            _id: Date.now().toString(),\n            // Temporary ID\n            name: examData.name,\n            category: examData.category,\n            level: examData.level,\n            class: examData.class\n          };\n          onSuccess(fallbackExam);\n        }\n        form.resetFields();\n        setAutoGeneratedName(\"\");\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to create exam\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const getClassOptions = () => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"];\n      case \"advance\":\n        return [\"Form-5\", \"Form-6\"];\n      default:\n        return [];\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 12\n      },\n      children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n        style: {\n          color: \"#1890ff\",\n          fontSize: 20\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Auto-Generate Exam\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 9\n    }, this),\n    open: visible,\n    onCancel: onCancel,\n    footer: null,\n    width: 800,\n    destroyOnClose: true,\n    focusTriggerAfterClose: false,\n    maskClosable: false,\n    children: [/*#__PURE__*/_jsxDEV(Alert, {\n      message: \"Create New Exam\",\n      description: \"This will create a new exam following the same structure as manual exam creation. You can then use this exam for AI question generation.\",\n      type: \"info\",\n      showIcon: true,\n      style: {\n        marginBottom: 24\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: form,\n      layout: \"vertical\",\n      onFinish: onFinish,\n      onValuesChange: handleFormValuesChange,\n      initialValues: {\n        duration: 3600,\n        // Default 1 hour\n        totalMarks: 100,\n        passingMarks: 50\n      },\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"Exam Name\",\n            rules: [{\n              required: true,\n              message: \"Please enter exam name\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"Enter exam name or auto-generate\",\n              suffix: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(FaMagic, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 27\n                }, this),\n                onClick: handleAutoGenerateName,\n                loading: isGeneratingName,\n                size: \"small\",\n                children: \"Auto-Generate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"duration\",\n            label: \"Exam Duration (Seconds)\",\n            rules: [{\n              required: true,\n              message: \"Please enter duration\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 300 // Minimum 5 minutes\n              ,\n              max: 14400 // Maximum 4 hours\n              ,\n              style: {\n                width: \"100%\"\n              },\n              placeholder: \"Duration in seconds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"level\",\n            label: \"Level\",\n            rules: [{\n              required: true,\n              message: \"Please select level\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Level\",\n              onChange: handleLevelChange,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"Primary\",\n                children: \"Primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Secondary\",\n                children: \"Secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Advance\",\n                children: \"Advance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"class\",\n            label: \"Class\",\n            rules: [{\n              required: true,\n              message: \"Please select class\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Class\",\n              disabled: !level,\n              children: getClassOptions().map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                value: cls,\n                children: cls\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"category\",\n            label: \"Category (Subject)\",\n            rules: [{\n              required: true,\n              message: \"Please select category\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Category\",\n              disabled: !level,\n              children: availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(Option, {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"totalMarks\",\n            label: \"Total Marks\",\n            rules: [{\n              required: true,\n              message: \"Please enter total marks\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 1,\n              max: 1000,\n              style: {\n                width: \"100%\"\n              },\n              placeholder: \"Total marks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"passingMarks\",\n            label: \"Passing Marks\",\n            rules: [{\n              required: true,\n              message: \"Please enter passing marks\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 1,\n              max: 1000,\n              style: {\n                width: \"100%\"\n              },\n              placeholder: \"Passing marks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              paddingTop: 30\n            },\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              message: `Pass Rate: ${passRate}%`,\n              type: \"info\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"description\",\n            label: \"Description (Optional)\",\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n              rows: 3,\n              placeholder: \"Enter exam description (optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), autoGeneratedName && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Auto-Generated Name\",\n        description: `Generated exam name: ${autoGeneratedName}`,\n        type: \"success\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          justifyContent: \"flex-end\",\n          gap: 12\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: onCancel,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          icon: /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 58\n          }, this),\n          children: \"Create Exam\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 208,\n    columnNumber: 5\n  }, this);\n}\n_s(AutoGenerateExamModal, \"+tiUShACfGgOd7LjkkJb62nPMLc=\", false, function () {\n  return [useDispatch, Form.useForm];\n});\n_c = AutoGenerateExamModal;\nexport default AutoGenerateExamModal;\nvar _c;\n$RefreshReg$(_c, \"AutoGenerateExamModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "Modal", "Form", "Row", "Col", "Input", "Select", "InputNumber", "<PERSON><PERSON>", "message", "<PERSON><PERSON>", "Divider", "FaRobot", "FaMagic", "HideLoading", "ShowLoading", "addExam", "generateExamName", "getSubjectsForLevel", "primarySubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "Option", "AutoGenerateExamModal", "visible", "onCancel", "onSuccess", "prefilledData", "_s", "dispatch", "form", "useForm", "level", "setLevel", "availableSubjects", "setAvailableSubjects", "autoGeneratedName", "setAutoGeneratedName", "isGeneratingName", "setIsGeneratingName", "passRate", "setPassRate", "calculatePassRate", "values", "totalMarks", "passingMarks", "Math", "round", "handleLevelChange", "selectedLevel", "console", "log", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "class", "undefined", "category", "fallbackSubjects", "response", "success", "data", "length", "combinedSubjects", "Set", "warn", "error", "handleFormValuesChange", "changedValues", "allValues", "resetFields", "_prefilledData$subjec", "subjects", "handleAutoGenerateName", "currentV<PERSON>ues", "getFieldsValue", "formLevel", "formClass", "warning", "examName", "name", "onFinish", "examData", "duration", "description", "isPublic", "questions", "_id", "fallbackExam", "Date", "now", "toString", "getClassOptions", "title", "style", "display", "alignItems", "gap", "children", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "open", "footer", "width", "destroyOnClose", "focusTriggerAfterClose", "maskClosable", "type", "showIcon", "marginBottom", "layout", "onValuesChange", "initialValues", "gutter", "xs", "md", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "suffix", "icon", "onClick", "loading", "size", "min", "max", "onChange", "value", "disabled", "map", "cls", "subject", "paddingTop", "TextArea", "rows", "justifyContent", "htmlType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/AIQuestionGeneration/AutoGenerateExamModal.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { \n  Modal, \n  Form, \n  Row, \n  Col, \n  Input, \n  Select, \n  InputNumber, \n  Button, \n  message,\n  Alert,\n  Divider\n} from \"antd\";\nimport { FaRobot, FaMagic } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addExam } from \"../../../apicalls/exams\";\nimport { generateExamName } from \"../../../apicalls/aiQuestions\";\nimport { getSubjectsForLevel } from \"../../../apicalls/aiQuestions\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\n\nconst { Option } = Select;\n\nfunction AutoGenerateExamModal({ \n  visible, \n  onCancel, \n  onSuccess, \n  prefilledData = {} \n}) {\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [level, setLevel] = useState(prefilledData.level || \"\");\n  // Remove unused selectedSubjects state - cleaned up\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [autoGeneratedName, setAutoGeneratedName] = useState(\"\");\n  const [isGeneratingName, setIsGeneratingName] = useState(false);\n  const [passRate, setPassRate] = useState(0);\n\n  const calculatePassRate = useCallback((values) => {\n    const totalMarks = values?.totalMarks || 0;\n    const passingMarks = values?.passingMarks || 0;\n    if (totalMarks > 0 && passingMarks > 0) {\n      return Math.round((passingMarks / totalMarks) * 100);\n    }\n    return 0;\n  }, []);\n\n  const handleLevelChange = useCallback(async (selectedLevel) => {\n    console.log(`🎯 Level changed to: ${selectedLevel}`);\n    setLevel(selectedLevel);\n    form.setFieldsValue({ class: undefined, category: \"\" });\n\n    // First set hardcoded subjects as fallback immediately\n    let fallbackSubjects = [];\n    switch (selectedLevel) {\n      case \"primary\":\n        fallbackSubjects = [...primarySubjects, \"science and technology\"]; // Add known syllabus subject\n        break;\n      case \"secondary\":\n        fallbackSubjects = secondarySubjects;\n        break;\n      case \"advance\":\n        fallbackSubjects = advanceSubjects;\n        break;\n      default:\n        fallbackSubjects = [];\n    }\n    setAvailableSubjects(fallbackSubjects);\n    console.log(`🔄 Set initial subjects for ${selectedLevel}:`, fallbackSubjects);\n\n    // Then try to fetch syllabus-based subjects\n    try {\n      console.log(`🔍 Fetching syllabus subjects for level: ${selectedLevel}`);\n      const response = await getSubjectsForLevel(selectedLevel);\n      console.log(`📊 Syllabus response received:`, response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        // Combine syllabus subjects with hardcoded ones (remove duplicates)\n        const combinedSubjects = [...new Set([...response.data, ...fallbackSubjects])];\n        setAvailableSubjects(combinedSubjects);\n        console.log(`✅ Updated with syllabus subjects for ${selectedLevel}:`, combinedSubjects);\n      } else {\n        console.warn(`⚠️ No syllabus subjects found for ${selectedLevel}, using fallback`);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching syllabus subjects:', error);\n      console.log(`🔄 Keeping fallback subjects for ${selectedLevel}`);\n    }\n  }, [form]);\n\n  const handleFormValuesChange = useCallback((changedValues, allValues) => {\n    if (changedValues.totalMarks !== undefined || changedValues.passingMarks !== undefined) {\n      setPassRate(calculatePassRate(allValues));\n    }\n  }, [calculatePassRate]);\n\n  useEffect(() => {\n    if (visible) {\n      // Reset form when modal opens\n      form.resetFields();\n      setLevel(prefilledData.level || \"\");\n\n      // Set initial values if provided\n      if (prefilledData.level) {\n        form.setFieldsValue({\n          level: prefilledData.level,\n          class: prefilledData.class,\n          category: prefilledData.subjects?.[0] || \"\",\n        });\n        handleLevelChange(prefilledData.level);\n      }\n\n      // Initialize pass rate with default values\n      setPassRate(calculatePassRate({ totalMarks: 100, passingMarks: 50 }));\n    }\n  }, [visible, prefilledData, form, handleLevelChange, calculatePassRate]);\n\n  const handleAutoGenerateName = async () => {\n    const currentValues = form.getFieldsValue();\n    const { level: formLevel, class: formClass, category } = currentValues;\n    \n    if (!formLevel || !formClass || !category) {\n      message.warning(\"Please select level, class, and category first\");\n      return;\n    }\n\n    try {\n      setIsGeneratingName(true);\n      const response = await generateExamName(formLevel, formClass, [category]);\n      \n      if (response.success) {\n        setAutoGeneratedName(response.data.examName);\n        form.setFieldsValue({ name: response.data.examName });\n        message.success(\"Exam name generated successfully!\");\n      } else {\n        message.error(\"Failed to generate exam name\");\n      }\n    } catch (error) {\n      message.error(\"Error generating exam name\");\n    } finally {\n      setIsGeneratingName(false);\n    }\n  };\n\n  const onFinish = async (values) => {\n    try {\n      dispatch(ShowLoading());\n      \n      // Prepare exam data following the same structure as manual creation\n      const examData = {\n        name: values.name,\n        duration: values.duration,\n        level: values.level,\n        category: values.category,\n        class: values.class,\n        totalMarks: values.totalMarks,\n        passingMarks: values.passingMarks,\n        description: values.description || `Auto-generated exam for ${values.category} - ${values.level} Level Class ${values.class}`,\n        isPublic: false, // Default to private\n        questions: [], // Start with empty questions array\n      };\n\n      const response = await addExam(examData);\n\n      if (response.success) {\n        message.success(\"Exam created successfully!\");\n        // Ensure we have valid exam data before passing it back\n        if (response.data && response.data._id) {\n          onSuccess(response.data); // Pass the created exam data back\n        } else {\n          // If no data returned, create a minimal exam object for the UI\n          const fallbackExam = {\n            _id: Date.now().toString(), // Temporary ID\n            name: examData.name,\n            category: examData.category,\n            level: examData.level,\n            class: examData.class,\n          };\n          onSuccess(fallbackExam);\n        }\n        form.resetFields();\n        setAutoGeneratedName(\"\");\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to create exam\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const getClassOptions = () => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"];\n      case \"advance\":\n        return [\"Form-5\", \"Form-6\"];\n      default:\n        return [];\n    }\n  };\n\n  return (\n    <Modal\n      title={\n        <div style={{ display: \"flex\", alignItems: \"center\", gap: 12 }}>\n          <FaRobot style={{ color: \"#1890ff\", fontSize: 20 }} />\n          <span>Auto-Generate Exam</span>\n        </div>\n      }\n      open={visible}\n      onCancel={onCancel}\n      footer={null}\n      width={800}\n      destroyOnClose\n      focusTriggerAfterClose={false}\n      maskClosable={false}\n    >\n      <Alert\n        message=\"Create New Exam\"\n        description=\"This will create a new exam following the same structure as manual exam creation. You can then use this exam for AI question generation.\"\n        type=\"info\"\n        showIcon\n        style={{ marginBottom: 24 }}\n      />\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={onFinish}\n        onValuesChange={handleFormValuesChange}\n        initialValues={{\n          duration: 3600, // Default 1 hour\n          totalMarks: 100,\n          passingMarks: 50,\n        }}\n      >\n        <Row gutter={[16, 16]}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"name\"\n              label=\"Exam Name\"\n              rules={[{ required: true, message: \"Please enter exam name\" }]}\n            >\n              <Input \n                placeholder=\"Enter exam name or auto-generate\"\n                suffix={\n                  <Button\n                    type=\"link\"\n                    icon={<FaMagic />}\n                    onClick={handleAutoGenerateName}\n                    loading={isGeneratingName}\n                    size=\"small\"\n                  >\n                    Auto-Generate\n                  </Button>\n                }\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"duration\"\n              label=\"Exam Duration (Seconds)\"\n              rules={[{ required: true, message: \"Please enter duration\" }]}\n            >\n              <InputNumber\n                min={300} // Minimum 5 minutes\n                max={14400} // Maximum 4 hours\n                style={{ width: \"100%\" }}\n                placeholder=\"Duration in seconds\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"level\"\n              label=\"Level\"\n              rules={[{ required: true, message: \"Please select level\" }]}\n            >\n              <Select\n                placeholder=\"Select Level\"\n                onChange={handleLevelChange}\n              >\n                <Option value=\"Primary\">Primary</Option>\n                <Option value=\"Secondary\">Secondary</Option>\n                <Option value=\"Advance\">Advance</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"class\"\n              label=\"Class\"\n              rules={[{ required: true, message: \"Please select class\" }]}\n            >\n              <Select placeholder=\"Select Class\" disabled={!level}>\n                {getClassOptions().map((cls) => (\n                  <Option key={cls} value={cls}>\n                    {cls}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"category\"\n              label=\"Category (Subject)\"\n              rules={[{ required: true, message: \"Please select category\" }]}\n            >\n              <Select placeholder=\"Select Category\" disabled={!level}>\n                {availableSubjects.map((subject) => (\n                  <Option key={subject} value={subject}>\n                    {subject}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"totalMarks\"\n              label=\"Total Marks\"\n              rules={[{ required: true, message: \"Please enter total marks\" }]}\n            >\n              <InputNumber\n                min={1}\n                max={1000}\n                style={{ width: \"100%\" }}\n                placeholder=\"Total marks\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"passingMarks\"\n              label=\"Passing Marks\"\n              rules={[{ required: true, message: \"Please enter passing marks\" }]}\n            >\n              <InputNumber\n                min={1}\n                max={1000}\n                style={{ width: \"100%\" }}\n                placeholder=\"Passing marks\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <div style={{ paddingTop: 30 }}>\n              <Alert\n                message={`Pass Rate: ${passRate}%`}\n                type=\"info\"\n                size=\"small\"\n              />\n            </div>\n          </Col>\n\n          <Col xs={24}>\n            <Form.Item\n              name=\"description\"\n              label=\"Description (Optional)\"\n            >\n              <Input.TextArea\n                rows={3}\n                placeholder=\"Enter exam description (optional)\"\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        {autoGeneratedName && (\n          <Alert\n            message=\"Auto-Generated Name\"\n            description={`Generated exam name: ${autoGeneratedName}`}\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        )}\n\n        <Divider />\n\n        <div style={{ display: \"flex\", justifyContent: \"flex-end\", gap: 12 }}>\n          <Button onClick={onCancel}>\n            Cancel\n          </Button>\n          <Button type=\"primary\" htmlType=\"submit\" icon={<FaRobot />}>\n            Create Exam\n          </Button>\n        </div>\n      </Form>\n    </Modal>\n  );\n}\n\nexport default AutoGenerateExamModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SAASC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AACjD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7F,MAAM;EAAEC;AAAO,CAAC,GAAGlB,MAAM;AAEzB,SAASmB,qBAAqBA,CAAC;EAC7BC,OAAO;EACPC,QAAQ;EACRC,SAAS;EACTC,aAAa,GAAG,CAAC;AACnB,CAAC,EAAE;EAAAC,EAAA;EACD,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgC,IAAI,CAAC,GAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAACgC,aAAa,CAACK,KAAK,IAAI,EAAE,CAAC;EAC7D;EACA,MAAM,CAACE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EAE3C,MAAM+C,iBAAiB,GAAG7C,WAAW,CAAE8C,MAAM,IAAK;IAChD,MAAMC,UAAU,GAAG,CAAAD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,UAAU,KAAI,CAAC;IAC1C,MAAMC,YAAY,GAAG,CAAAF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,YAAY,KAAI,CAAC;IAC9C,IAAID,UAAU,GAAG,CAAC,IAAIC,YAAY,GAAG,CAAC,EAAE;MACtC,OAAOC,IAAI,CAACC,KAAK,CAAEF,YAAY,GAAGD,UAAU,GAAI,GAAG,CAAC;IACtD;IACA,OAAO,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,iBAAiB,GAAGnD,WAAW,CAAC,MAAOoD,aAAa,IAAK;IAC7DC,OAAO,CAACC,GAAG,CAAE,wBAAuBF,aAAc,EAAC,CAAC;IACpDhB,QAAQ,CAACgB,aAAa,CAAC;IACvBnB,IAAI,CAACsB,cAAc,CAAC;MAAEC,KAAK,EAAEC,SAAS;MAAEC,QAAQ,EAAE;IAAG,CAAC,CAAC;;IAEvD;IACA,IAAIC,gBAAgB,GAAG,EAAE;IACzB,QAAQP,aAAa;MACnB,KAAK,SAAS;QACZO,gBAAgB,GAAG,CAAC,GAAGvC,eAAe,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACnE;MACF,KAAK,WAAW;QACduC,gBAAgB,GAAGtC,iBAAiB;QACpC;MACF,KAAK,SAAS;QACZsC,gBAAgB,GAAGrC,eAAe;QAClC;MACF;QACEqC,gBAAgB,GAAG,EAAE;IACzB;IACArB,oBAAoB,CAACqB,gBAAgB,CAAC;IACtCN,OAAO,CAACC,GAAG,CAAE,+BAA8BF,aAAc,GAAE,EAAEO,gBAAgB,CAAC;;IAE9E;IACA,IAAI;MACFN,OAAO,CAACC,GAAG,CAAE,4CAA2CF,aAAc,EAAC,CAAC;MACxE,MAAMQ,QAAQ,GAAG,MAAMzC,mBAAmB,CAACiC,aAAa,CAAC;MACzDC,OAAO,CAACC,GAAG,CAAE,gCAA+B,EAAEM,QAAQ,CAAC;MAEvD,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjE;QACA,MAAMC,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAGL,QAAQ,CAACE,IAAI,EAAE,GAAGH,gBAAgB,CAAC,CAAC,CAAC;QAC9ErB,oBAAoB,CAAC0B,gBAAgB,CAAC;QACtCX,OAAO,CAACC,GAAG,CAAE,wCAAuCF,aAAc,GAAE,EAAEY,gBAAgB,CAAC;MACzF,CAAC,MAAM;QACLX,OAAO,CAACa,IAAI,CAAE,qCAAoCd,aAAc,kBAAiB,CAAC;MACpF;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3Dd,OAAO,CAACC,GAAG,CAAE,oCAAmCF,aAAc,EAAC,CAAC;IAClE;EACF,CAAC,EAAE,CAACnB,IAAI,CAAC,CAAC;EAEV,MAAMmC,sBAAsB,GAAGpE,WAAW,CAAC,CAACqE,aAAa,EAAEC,SAAS,KAAK;IACvE,IAAID,aAAa,CAACtB,UAAU,KAAKU,SAAS,IAAIY,aAAa,CAACrB,YAAY,KAAKS,SAAS,EAAE;MACtFb,WAAW,CAACC,iBAAiB,CAACyB,SAAS,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAACzB,iBAAiB,CAAC,CAAC;EAEvB9C,SAAS,CAAC,MAAM;IACd,IAAI4B,OAAO,EAAE;MACX;MACAM,IAAI,CAACsC,WAAW,CAAC,CAAC;MAClBnC,QAAQ,CAACN,aAAa,CAACK,KAAK,IAAI,EAAE,CAAC;;MAEnC;MACA,IAAIL,aAAa,CAACK,KAAK,EAAE;QAAA,IAAAqC,qBAAA;QACvBvC,IAAI,CAACsB,cAAc,CAAC;UAClBpB,KAAK,EAAEL,aAAa,CAACK,KAAK;UAC1BqB,KAAK,EAAE1B,aAAa,CAAC0B,KAAK;UAC1BE,QAAQ,EAAE,EAAAc,qBAAA,GAAA1C,aAAa,CAAC2C,QAAQ,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAyB,CAAC,CAAC,KAAI;QAC3C,CAAC,CAAC;QACFrB,iBAAiB,CAACrB,aAAa,CAACK,KAAK,CAAC;MACxC;;MAEA;MACAS,WAAW,CAACC,iBAAiB,CAAC;QAAEE,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAG,CAAC,CAAC,CAAC;IACvE;EACF,CAAC,EAAE,CAACrB,OAAO,EAAEG,aAAa,EAAEG,IAAI,EAAEkB,iBAAiB,EAAEN,iBAAiB,CAAC,CAAC;EAExE,MAAM6B,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,MAAMC,aAAa,GAAG1C,IAAI,CAAC2C,cAAc,CAAC,CAAC;IAC3C,MAAM;MAAEzC,KAAK,EAAE0C,SAAS;MAAErB,KAAK,EAAEsB,SAAS;MAAEpB;IAAS,CAAC,GAAGiB,aAAa;IAEtE,IAAI,CAACE,SAAS,IAAI,CAACC,SAAS,IAAI,CAACpB,QAAQ,EAAE;MACzChD,OAAO,CAACqE,OAAO,CAAC,gDAAgD,CAAC;MACjE;IACF;IAEA,IAAI;MACFrC,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAMkB,QAAQ,GAAG,MAAM1C,gBAAgB,CAAC2D,SAAS,EAAEC,SAAS,EAAE,CAACpB,QAAQ,CAAC,CAAC;MAEzE,IAAIE,QAAQ,CAACC,OAAO,EAAE;QACpBrB,oBAAoB,CAACoB,QAAQ,CAACE,IAAI,CAACkB,QAAQ,CAAC;QAC5C/C,IAAI,CAACsB,cAAc,CAAC;UAAE0B,IAAI,EAAErB,QAAQ,CAACE,IAAI,CAACkB;QAAS,CAAC,CAAC;QACrDtE,OAAO,CAACmD,OAAO,CAAC,mCAAmC,CAAC;MACtD,CAAC,MAAM;QACLnD,OAAO,CAACyD,KAAK,CAAC,8BAA8B,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdzD,OAAO,CAACyD,KAAK,CAAC,4BAA4B,CAAC;IAC7C,CAAC,SAAS;MACRzB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMwC,QAAQ,GAAG,MAAOpC,MAAM,IAAK;IACjC,IAAI;MACFd,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAMmE,QAAQ,GAAG;QACfF,IAAI,EAAEnC,MAAM,CAACmC,IAAI;QACjBG,QAAQ,EAAEtC,MAAM,CAACsC,QAAQ;QACzBjD,KAAK,EAAEW,MAAM,CAACX,KAAK;QACnBuB,QAAQ,EAAEZ,MAAM,CAACY,QAAQ;QACzBF,KAAK,EAAEV,MAAM,CAACU,KAAK;QACnBT,UAAU,EAAED,MAAM,CAACC,UAAU;QAC7BC,YAAY,EAAEF,MAAM,CAACE,YAAY;QACjCqC,WAAW,EAAEvC,MAAM,CAACuC,WAAW,IAAK,2BAA0BvC,MAAM,CAACY,QAAS,MAAKZ,MAAM,CAACX,KAAM,gBAAeW,MAAM,CAACU,KAAM,EAAC;QAC7H8B,QAAQ,EAAE,KAAK;QAAE;QACjBC,SAAS,EAAE,EAAE,CAAE;MACjB,CAAC;;MAED,MAAM3B,QAAQ,GAAG,MAAM3C,OAAO,CAACkE,QAAQ,CAAC;MAExC,IAAIvB,QAAQ,CAACC,OAAO,EAAE;QACpBnD,OAAO,CAACmD,OAAO,CAAC,4BAA4B,CAAC;QAC7C;QACA,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAAC0B,GAAG,EAAE;UACtC3D,SAAS,CAAC+B,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;QAC5B,CAAC,MAAM;UACL;UACA,MAAM2B,YAAY,GAAG;YACnBD,GAAG,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;YAAE;YAC5BX,IAAI,EAAEE,QAAQ,CAACF,IAAI;YACnBvB,QAAQ,EAAEyB,QAAQ,CAACzB,QAAQ;YAC3BvB,KAAK,EAAEgD,QAAQ,CAAChD,KAAK;YACrBqB,KAAK,EAAE2B,QAAQ,CAAC3B;UAClB,CAAC;UACD3B,SAAS,CAAC4D,YAAY,CAAC;QACzB;QACAxD,IAAI,CAACsC,WAAW,CAAC,CAAC;QAClB/B,oBAAoB,CAAC,EAAE,CAAC;MAC1B,CAAC,MAAM;QACL9B,OAAO,CAACyD,KAAK,CAACP,QAAQ,CAAClD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACdzD,OAAO,CAACyD,KAAK,CAAC,uBAAuB,CAAC;IACxC,CAAC,SAAS;MACRnC,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM8E,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQ1D,KAAK;MACX,KAAK,SAAS;QACZ,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC5C,KAAK,WAAW;QACd,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;MACjD,KAAK,SAAS;QACZ,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAC7B;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,oBACEX,OAAA,CAACtB,KAAK;IACJ4F,KAAK,eACHtE,OAAA;MAAKuE,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAG,CAAE;MAAAC,QAAA,gBAC7D3E,OAAA,CAACX,OAAO;QAACkF,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAG;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDjF,OAAA;QAAA2E,QAAA,EAAM;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACN;IACDC,IAAI,EAAE/E,OAAQ;IACdC,QAAQ,EAAEA,QAAS;IACnB+E,MAAM,EAAE,IAAK;IACbC,KAAK,EAAE,GAAI;IACXC,cAAc;IACdC,sBAAsB,EAAE,KAAM;IAC9BC,YAAY,EAAE,KAAM;IAAAZ,QAAA,gBAEpB3E,OAAA,CAACb,KAAK;MACJD,OAAO,EAAC,iBAAiB;MACzB2E,WAAW,EAAC,0IAA0I;MACtJ2B,IAAI,EAAC,MAAM;MACXC,QAAQ;MACRlB,KAAK,EAAE;QAAEmB,YAAY,EAAE;MAAG;IAAE;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAEFjF,OAAA,CAACrB,IAAI;MACH8B,IAAI,EAAEA,IAAK;MACXkF,MAAM,EAAC,UAAU;MACjBjC,QAAQ,EAAEA,QAAS;MACnBkC,cAAc,EAAEhD,sBAAuB;MACvCiD,aAAa,EAAE;QACbjC,QAAQ,EAAE,IAAI;QAAE;QAChBrC,UAAU,EAAE,GAAG;QACfC,YAAY,EAAE;MAChB,CAAE;MAAAmD,QAAA,gBAEF3E,OAAA,CAACpB,GAAG;QAACkH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAnB,QAAA,gBACpB3E,OAAA,CAACnB,GAAG;UAACkH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAClB3E,OAAA,CAACrB,IAAI,CAACsH,IAAI;YACRxC,IAAI,EAAC,MAAM;YACXyC,KAAK,EAAC,WAAW;YACjBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElH,OAAO,EAAE;YAAyB,CAAC,CAAE;YAAAyF,QAAA,eAE/D3E,OAAA,CAAClB,KAAK;cACJuH,WAAW,EAAC,kCAAkC;cAC9CC,MAAM,eACJtG,OAAA,CAACf,MAAM;gBACLuG,IAAI,EAAC,MAAM;gBACXe,IAAI,eAAEvG,OAAA,CAACV,OAAO;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAClBuB,OAAO,EAAEtD,sBAAuB;gBAChCuD,OAAO,EAAExF,gBAAiB;gBAC1ByF,IAAI,EAAC,OAAO;gBAAA/B,QAAA,EACb;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YACT;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENjF,OAAA,CAACnB,GAAG;UAACkH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAClB3E,OAAA,CAACrB,IAAI,CAACsH,IAAI;YACRxC,IAAI,EAAC,UAAU;YACfyC,KAAK,EAAC,yBAAyB;YAC/BC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElH,OAAO,EAAE;YAAwB,CAAC,CAAE;YAAAyF,QAAA,eAE9D3E,OAAA,CAAChB,WAAW;cACV2H,GAAG,EAAE,GAAI,CAAC;cAAA;cACVC,GAAG,EAAE,KAAM,CAAC;cAAA;cACZrC,KAAK,EAAE;gBAAEa,KAAK,EAAE;cAAO,CAAE;cACzBiB,WAAW,EAAC;YAAqB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENjF,OAAA,CAACnB,GAAG;UAACkH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB3E,OAAA,CAACrB,IAAI,CAACsH,IAAI;YACRxC,IAAI,EAAC,OAAO;YACZyC,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElH,OAAO,EAAE;YAAsB,CAAC,CAAE;YAAAyF,QAAA,eAE5D3E,OAAA,CAACjB,MAAM;cACLsH,WAAW,EAAC,cAAc;cAC1BQ,QAAQ,EAAElF,iBAAkB;cAAAgD,QAAA,gBAE5B3E,OAAA,CAACC,MAAM;gBAAC6G,KAAK,EAAC,SAAS;gBAAAnC,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCjF,OAAA,CAACC,MAAM;gBAAC6G,KAAK,EAAC,WAAW;gBAAAnC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CjF,OAAA,CAACC,MAAM;gBAAC6G,KAAK,EAAC,SAAS;gBAAAnC,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENjF,OAAA,CAACnB,GAAG;UAACkH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB3E,OAAA,CAACrB,IAAI,CAACsH,IAAI;YACRxC,IAAI,EAAC,OAAO;YACZyC,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElH,OAAO,EAAE;YAAsB,CAAC,CAAE;YAAAyF,QAAA,eAE5D3E,OAAA,CAACjB,MAAM;cAACsH,WAAW,EAAC,cAAc;cAACU,QAAQ,EAAE,CAACpG,KAAM;cAAAgE,QAAA,EACjDN,eAAe,CAAC,CAAC,CAAC2C,GAAG,CAAEC,GAAG,iBACzBjH,OAAA,CAACC,MAAM;gBAAW6G,KAAK,EAAEG,GAAI;gBAAAtC,QAAA,EAC1BsC;cAAG,GADOA,GAAG;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENjF,OAAA,CAACnB,GAAG;UAACkH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB3E,OAAA,CAACrB,IAAI,CAACsH,IAAI;YACRxC,IAAI,EAAC,UAAU;YACfyC,KAAK,EAAC,oBAAoB;YAC1BC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElH,OAAO,EAAE;YAAyB,CAAC,CAAE;YAAAyF,QAAA,eAE/D3E,OAAA,CAACjB,MAAM;cAACsH,WAAW,EAAC,iBAAiB;cAACU,QAAQ,EAAE,CAACpG,KAAM;cAAAgE,QAAA,EACpD9D,iBAAiB,CAACmG,GAAG,CAAEE,OAAO,iBAC7BlH,OAAA,CAACC,MAAM;gBAAe6G,KAAK,EAAEI,OAAQ;gBAAAvC,QAAA,EAClCuC;cAAO,GADGA,OAAO;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENjF,OAAA,CAACnB,GAAG;UAACkH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB3E,OAAA,CAACrB,IAAI,CAACsH,IAAI;YACRxC,IAAI,EAAC,YAAY;YACjByC,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElH,OAAO,EAAE;YAA2B,CAAC,CAAE;YAAAyF,QAAA,eAEjE3E,OAAA,CAAChB,WAAW;cACV2H,GAAG,EAAE,CAAE;cACPC,GAAG,EAAE,IAAK;cACVrC,KAAK,EAAE;gBAAEa,KAAK,EAAE;cAAO,CAAE;cACzBiB,WAAW,EAAC;YAAa;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENjF,OAAA,CAACnB,GAAG;UAACkH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB3E,OAAA,CAACrB,IAAI,CAACsH,IAAI;YACRxC,IAAI,EAAC,cAAc;YACnByC,KAAK,EAAC,eAAe;YACrBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElH,OAAO,EAAE;YAA6B,CAAC,CAAE;YAAAyF,QAAA,eAEnE3E,OAAA,CAAChB,WAAW;cACV2H,GAAG,EAAE,CAAE;cACPC,GAAG,EAAE,IAAK;cACVrC,KAAK,EAAE;gBAAEa,KAAK,EAAE;cAAO,CAAE;cACzBiB,WAAW,EAAC;YAAe;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENjF,OAAA,CAACnB,GAAG;UAACkH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB3E,OAAA;YAAKuE,KAAK,EAAE;cAAE4C,UAAU,EAAE;YAAG,CAAE;YAAAxC,QAAA,eAC7B3E,OAAA,CAACb,KAAK;cACJD,OAAO,EAAG,cAAaiC,QAAS,GAAG;cACnCqE,IAAI,EAAC,MAAM;cACXkB,IAAI,EAAC;YAAO;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjF,OAAA,CAACnB,GAAG;UAACkH,EAAE,EAAE,EAAG;UAAApB,QAAA,eACV3E,OAAA,CAACrB,IAAI,CAACsH,IAAI;YACRxC,IAAI,EAAC,aAAa;YAClByC,KAAK,EAAC,wBAAwB;YAAAvB,QAAA,eAE9B3E,OAAA,CAAClB,KAAK,CAACsI,QAAQ;cACbC,IAAI,EAAE,CAAE;cACRhB,WAAW,EAAC;YAAmC;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELlE,iBAAiB,iBAChBf,OAAA,CAACb,KAAK;QACJD,OAAO,EAAC,qBAAqB;QAC7B2E,WAAW,EAAG,wBAAuB9C,iBAAkB,EAAE;QACzDyE,IAAI,EAAC,SAAS;QACdC,QAAQ;QACRlB,KAAK,EAAE;UAAEmB,YAAY,EAAE;QAAG;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF,eAEDjF,OAAA,CAACZ,OAAO;QAAA0F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXjF,OAAA;QAAKuE,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAE8C,cAAc,EAAE,UAAU;UAAE5C,GAAG,EAAE;QAAG,CAAE;QAAAC,QAAA,gBACnE3E,OAAA,CAACf,MAAM;UAACuH,OAAO,EAAEpG,QAAS;UAAAuE,QAAA,EAAC;QAE3B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjF,OAAA,CAACf,MAAM;UAACuG,IAAI,EAAC,SAAS;UAAC+B,QAAQ,EAAC,QAAQ;UAAChB,IAAI,eAAEvG,OAAA,CAACX,OAAO;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,EAAC;QAE5D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ;AAAC1E,EAAA,CA7XQL,qBAAqB;EAAA,QAMXzB,WAAW,EACbE,IAAI,CAAC+B,OAAO;AAAA;AAAA8G,EAAA,GAPpBtH,qBAAqB;AA+X9B,eAAeA,qBAAqB;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}