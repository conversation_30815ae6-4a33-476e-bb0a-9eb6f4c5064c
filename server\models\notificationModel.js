const mongoose = require("mongoose");

const notificationSchema = new mongoose.Schema(
  {
    // Recipient of the notification
    recipient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: true,
    },
    
    // Sender of the notification (optional, for user-generated notifications)
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: false,
    },
    
    // Notification type
    type: {
      type: String,
      enum: [
        'new_exam',
        'new_study_material', 
        'forum_question_posted',
        'forum_answer_received',
        'forum_answer_posted',
        'exam_result',
        'achievement_unlocked',
        'level_up',
        'system_announcement'
      ],
      required: true,
    },
    
    // Notification title
    title: {
      type: String,
      required: true,
      maxlength: 200,
    },
    
    // Notification message/content
    message: {
      type: String,
      required: true,
      maxlength: 500,
    },
    
    // Related entity data
    relatedEntity: {
      entityType: {
        type: String,
        enum: ['exam', 'study_material', 'forum_question', 'forum_answer', 'user', 'achievement'],
        required: false,
      },
      entityId: {
        type: mongoose.Schema.Types.ObjectId,
        required: false,
      },
      entityData: {
        type: Object,
        required: false,
      },
    },
    
    // Notification status
    isRead: {
      type: Boolean,
      default: false,
    },
    
    isArchived: {
      type: Boolean,
      default: false,
    },
    
    // Priority level
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium',
    },
    
    // Action URL (where to redirect when clicked)
    actionUrl: {
      type: String,
      required: false,
    },
    
    // Expiry date (for temporary notifications)
    expiresAt: {
      type: Date,
      required: false,
    },
    
    // Delivery channels
    channels: {
      inApp: {
        type: Boolean,
        default: true,
      },
      email: {
        type: Boolean,
        default: false,
      },
      push: {
        type: Boolean,
        default: false,
      },
    },
    
    // Delivery status
    deliveryStatus: {
      inApp: {
        delivered: { type: Boolean, default: false },
        deliveredAt: { type: Date },
      },
      email: {
        delivered: { type: Boolean, default: false },
        deliveredAt: { type: Date },
        error: { type: String },
      },
      push: {
        delivered: { type: Boolean, default: false },
        deliveredAt: { type: Date },
        error: { type: String },
      },
    },
    
    // Metadata
    metadata: {
      type: Object,
      default: {},
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better performance
notificationSchema.index({ recipient: 1, createdAt: -1 });
notificationSchema.index({ recipient: 1, isRead: 1 });
notificationSchema.index({ type: 1, createdAt: -1 });
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Virtual for time ago
notificationSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diff = now - this.createdAt;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  return this.createdAt.toLocaleDateString();
});

// Static method to create notification
notificationSchema.statics.createNotification = async function(data) {
  try {
    const notification = new this(data);
    await notification.save();
    
    // Mark as delivered for in-app
    if (notification.channels.inApp) {
      notification.deliveryStatus.inApp.delivered = true;
      notification.deliveryStatus.inApp.deliveredAt = new Date();
      await notification.save();
    }
    
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

// Instance method to mark as read
notificationSchema.methods.markAsRead = async function() {
  this.isRead = true;
  return await this.save();
};

// Instance method to archive
notificationSchema.methods.archive = async function() {
  this.isArchived = true;
  return await this.save();
};

module.exports = mongoose.model("notifications", notificationSchema);
