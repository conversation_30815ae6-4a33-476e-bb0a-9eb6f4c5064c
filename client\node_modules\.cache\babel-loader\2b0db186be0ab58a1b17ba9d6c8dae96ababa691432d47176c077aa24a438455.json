{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\AIQuestionGeneration\\\\QuestionGenerationForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { Card, Form, Select, InputNumber, Button, Row, Col, Checkbox, message, Divider, Alert, Progress } from \"antd\";\nimport { FaArrowLeft, FaRobot } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllExams } from \"../../../apicalls/exams\";\nimport { generateQuestions, getSubjectsForLevel, getSyllabusTopics } from \"../../../apicalls/aiQuestions\";\nimport { getSyllabusesForAI } from \"../../../apicalls/syllabus\";\nimport AutoGenerateExamModal from \"./AutoGenerateExamModal\";\nimport AILoginModal from \"../../../components/AILoginModal\";\nimport { useAIAuth } from \"../../../hooks/useAIAuth\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction QuestionGenerationForm({\n  onBack,\n  onSuccess\n}) {\n  _s();\n  var _classOptions$selecte;\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n\n  // Enhanced authentication\n  const {\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    loading: authLoading,\n    requiresUpgrade,\n    needsLogin,\n    handleLoginSuccess,\n    requireAIAuth,\n    sessionExpiringSoon,\n    timeUntilExpiry\n  } = useAIAuth();\n  const [exams, setExams] = useState([]);\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [availableTopics, setAvailableTopics] = useState([]);\n  const [availableSyllabuses, setAvailableSyllabuses] = useState([]);\n  const [selectedLevel, setSelectedLevel] = useState(\"\");\n  const [selectedClass, setSelectedClass] = useState(\"\");\n  const [selectedSubjects, setSelectedSubjects] = useState([]);\n  const [selectedSyllabus, setSelectedSyllabus] = useState(null);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationProgress, setGenerationProgress] = useState(0);\n  const [showAutoGenerateModal, setShowAutoGenerateModal] = useState(false);\n  const [showLoginModal, setShowLoginModal] = useState(false);\n  const fetchExams = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      if (response.success && Array.isArray(response.data)) {\n        // Filter out any null or undefined values\n        const validExams = response.data.filter(exam => exam && exam._id);\n        setExams(validExams);\n      } else {\n        message.error(\"Failed to fetch exams\");\n        setExams([]); // Ensure exams is always an array\n      }\n    } catch (error) {\n      message.error(\"Error fetching exams\");\n      setExams([]); // Ensure exams is always an array\n    } finally {\n      dispatch(HideLoading());\n    }\n  }, [dispatch]);\n  useEffect(() => {\n    // Only fetch exams if we have authentication, otherwise let the auth hook handle it\n    if (isAuthenticated && hasAIAccess && !authLoading) {\n      fetchExams();\n    }\n  }, [isAuthenticated, hasAIAccess, authLoading, fetchExams]);\n  const handleLevelChange = async level => {\n    setSelectedLevel(level);\n    setSelectedClass(\"\");\n    setSelectedSubjects([]);\n    setAvailableTopics([]);\n    setSelectedSyllabus(null);\n    form.setFieldsValue({\n      class: undefined,\n      subjects: [],\n      syllabusTopics: [],\n      selectedSyllabus: undefined\n    });\n    try {\n      // Fetch subjects for this level\n      const response = await getSubjectsForLevel(level);\n      if (response.success) {\n        setAvailableSubjects(response.data);\n      } else {\n        message.error(\"Failed to fetch subjects\");\n      }\n\n      // Fetch available syllabuses for this level\n      const syllabusResponse = await getSyllabusesForAI(level);\n      if (syllabusResponse.success) {\n        setAvailableSyllabuses(syllabusResponse.data);\n        console.log(`📚 Found ${syllabusResponse.data.length} syllabuses for ${level}`);\n      } else {\n        setAvailableSyllabuses([]);\n        console.warn(`⚠️ No syllabuses found for ${level}`);\n      }\n    } catch (error) {\n      message.error(\"Error fetching subjects and syllabuses\");\n      setAvailableSyllabuses([]);\n    }\n  };\n  const handleSyllabusChange = syllabusId => {\n    const selectedSyll = availableSyllabuses.find(s => s._id === syllabusId);\n    setSelectedSyllabus(selectedSyll);\n    if (selectedSyll) {\n      console.log(`📚 Selected syllabus: ${selectedSyll.title}`);\n      console.log(`📚 Covers classes: ${selectedSyll.classes.join(', ')}`);\n      console.log(`📚 Subject: ${selectedSyll.subject}`);\n\n      // Auto-fill subject if it matches and is available\n      if (selectedSyll.subject && availableSubjects.includes(selectedSyll.subject)) {\n        form.setFieldsValue({\n          subjects: [selectedSyll.subject]\n        });\n        setSelectedSubjects([selectedSyll.subject]);\n      }\n    }\n  };\n  const handleClassChange = className => {\n    setSelectedClass(className);\n    setAvailableTopics([]);\n    form.setFieldsValue({\n      syllabusTopics: []\n    });\n\n    // If subjects are already selected, fetch topics\n    if (selectedSubjects.length > 0) {\n      fetchTopicsForSubjects(selectedLevel, className, selectedSubjects);\n    }\n  };\n  const handleSubjectsChange = subjects => {\n    setSelectedSubjects(subjects);\n    setAvailableTopics([]);\n    form.setFieldsValue({\n      syllabusTopics: []\n    });\n\n    // If class is selected, fetch topics\n    if (selectedClass) {\n      fetchTopicsForSubjects(selectedLevel, selectedClass, subjects);\n    }\n\n    // Note: Auto-generate exam functionality moved to modal\n  };\n\n  const fetchTopicsForSubjects = async (level, className, subjects) => {\n    if (!level || !className || subjects.length === 0) return;\n    try {\n      const allTopics = [];\n      for (const subject of subjects) {\n        const response = await getSyllabusTopics(level, className, subject);\n        if (response.success) {\n          const subjectTopics = response.data.topics.map(topic => ({\n            ...topic,\n            subject: subject,\n            fullName: `${subject}: ${topic.topicName}`\n          }));\n          allTopics.push(...subjectTopics);\n        }\n      }\n      setAvailableTopics(allTopics);\n    } catch (error) {\n      console.error(\"Error fetching topics:\", error);\n      message.error(\"Failed to fetch syllabus topics\");\n    }\n  };\n  const handleAutoGenerateExamSuccess = newExam => {\n    // Add the new exam to the list and select it\n    if (newExam && newExam._id) {\n      const updatedExams = [...exams, newExam];\n      setExams(updatedExams);\n      form.setFieldsValue({\n        examId: newExam._id\n      });\n      setShowAutoGenerateModal(false);\n      message.success(`Exam created successfully: ${newExam.name}`);\n    } else {\n      message.error(\"Invalid exam data received\");\n      setShowAutoGenerateModal(false);\n    }\n  };\n  const openAutoGenerateModal = () => {\n    setShowAutoGenerateModal(true);\n  };\n  const onFinish = async values => {\n    console.log(\"🚀 Form submission started\");\n    console.log(\"📝 Form values:\", values);\n    try {\n      setIsGenerating(true);\n      setGenerationProgress(10);\n\n      // Validate question distribution\n      const totalDistribution = Object.values(values.questionDistribution || {}).reduce((sum, count) => sum + (count || 0), 0);\n      console.log(\"📊 Total distribution:\", totalDistribution, \"Total questions:\", values.totalQuestions);\n      if (totalDistribution !== values.totalQuestions) {\n        console.error(\"❌ Distribution validation failed\");\n        message.error(\"Question distribution must equal total questions\");\n        setIsGenerating(false);\n        return;\n      }\n      console.log(\"✅ Distribution validation passed\");\n      setGenerationProgress(30);\n\n      // Check authentication and AI access\n      if (!isAuthenticated || !hasAIAccess) {\n        setIsGenerating(false);\n        setShowLoginModal(true);\n        message.warning(\"Please login to access AI question generation features.\");\n        return;\n      }\n\n      // Double-check with server-side validation\n      const authCheck = await requireAIAuth();\n      if (!authCheck.success) {\n        setIsGenerating(false);\n        switch (authCheck.reason) {\n          case 'not_authenticated':\n          case 'refresh_failed':\n            setShowLoginModal(true);\n            message.warning(\"Please login to generate AI questions.\");\n            return;\n          case 'no_ai_access':\n            message.error(\"AI features are not available for your account.\");\n            return;\n          case 'requires_upgrade':\n            message.warning(\"AI question generation requires a premium subscription. Please upgrade your account.\");\n            return;\n          default:\n            setShowLoginModal(true);\n            message.warning(\"Authentication check failed. Please login again.\");\n            return;\n        }\n      }\n      const payload = {\n        examId: values.examId,\n        questionTypes: values.questionTypes,\n        subjects: values.subjects,\n        level: values.level,\n        class: values.class,\n        difficultyLevels: values.difficultyLevels,\n        syllabusTopics: values.syllabusTopics || [],\n        totalQuestions: values.totalQuestions,\n        questionDistribution: values.questionDistribution,\n        selectedSyllabusId: values.selectedSyllabus || null,\n        userId: user._id\n      };\n      console.log(\"📤 Sending payload:\", payload);\n      setGenerationProgress(50);\n      console.log(\"🌐 Making API call to generate questions...\");\n\n      // Show progress message to user\n      message.info(\"AI is generating your questions... This may take a few minutes.\", 5);\n      const response = await generateQuestions(payload);\n      console.log(\"📥 API response received:\", response);\n      setGenerationProgress(90);\n      if (response.success) {\n        setGenerationProgress(100);\n        message.success(\"Questions generated successfully!\");\n        setTimeout(() => {\n          onSuccess();\n        }, 1000);\n      } else {\n        message.error(response.message || \"Failed to generate questions\");\n      }\n    } catch (error) {\n      console.error(\"Question generation error:\", error);\n\n      // More detailed error handling\n      let errorMessage = \"Error generating questions\";\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n        // Handle timeout errors specifically\n        errorMessage = \"Question generation is taking longer than expected. This might be due to high server load. Please try again with fewer questions or check your internet connection.\";\n      } else if (error.response) {\n        // Server responded with error status\n        console.error(\"Server error response:\", error.response.data);\n        console.error(\"Server error status:\", error.response.status);\n        if (error.response.status === 401) {\n          // Handle authentication errors for AI requests\n          const errorData = error.response.data;\n          if (errorData !== null && errorData !== void 0 && errorData.requiresLogin) {\n            // Show the AI login modal instead of redirecting\n            setShowLoginModal(true);\n            errorMessage = errorData.message || \"Authentication required for AI features.\";\n          } else {\n            errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.message) || \"Authentication failed. Please login again.\";\n          }\n        } else if (error.response.status === 403) {\n          // Handle permission/subscription errors\n          const errorData = error.response.data;\n          if (errorData !== null && errorData !== void 0 && errorData.upgradeRequired) {\n            errorMessage = \"AI question generation requires a premium subscription. Please upgrade your account.\";\n          } else {\n            errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.message) || \"Access denied for AI features.\";\n          }\n        } else if (error.response.status === 504 || error.response.status === 502) {\n          errorMessage = \"Server timeout. The AI generation process is taking longer than expected. Please try again with fewer questions.\";\n        } else {\n          var _error$response$data;\n          errorMessage = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || `Server error: ${error.response.status}`;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        console.error(\"Network error:\", error.request);\n        errorMessage = \"Network error - please check your connection. If the problem persists, try generating fewer questions at once.\";\n      } else {\n        // Something else happened\n        console.error(\"Error:\", error.message);\n        errorMessage = error.message || \"Unknown error occurred\";\n      }\n      message.error(errorMessage);\n    } finally {\n      setIsGenerating(false);\n      setGenerationProgress(0);\n    }\n  };\n  const questionTypeOptions = [{\n    label: \"Multiple Choice\",\n    value: \"multiple_choice\"\n  }, {\n    label: \"Fill in the Blank\",\n    value: \"fill_blank\"\n  }, {\n    label: \"Picture-based\",\n    value: \"picture_based\"\n  }];\n  const difficultyOptions = [{\n    label: \"Easy\",\n    value: \"easy\"\n  }, {\n    label: \"Medium\",\n    value: \"medium\"\n  }, {\n    label: \"Hard\",\n    value: \"hard\"\n  }];\n  const levelOptions = [{\n    label: \"Primary Education (Standards I-VI)\",\n    value: \"primary\"\n  }, {\n    label: \"Ordinary Secondary (Forms I-IV)\",\n    value: \"ordinary_secondary\"\n  }, {\n    label: \"Advanced Secondary (Forms V-VI)\",\n    value: \"advanced_secondary\"\n  }];\n  const classOptions = {\n    primary: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\"],\n    // Standards I-VI (TIE Primary)\n    ordinary_secondary: [\"I\", \"II\", \"III\", \"IV\"],\n    // Forms I-IV (TIE Ordinary Secondary)\n    advanced_secondary: [\"V\", \"VI\"] // Forms V-VI (TIE Advanced Secondary)\n  };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"question-generation-form\",\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 21\n          }, this),\n          onClick: onBack,\n          className: \"back-button\",\n          children: \"Back to Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"title-section\",\n          children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Generate AI Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this),\n      children: [authLoading ? /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Checking Authentication...\",\n        description: \"Verifying your access to AI features.\",\n        type: \"info\",\n        showIcon: true,\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 11\n      }, this) : !isAuthenticated ? /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Login Required\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please login to access AI question generation features.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"small\",\n            onClick: () => setShowLoginModal(true),\n            style: {\n              marginTop: 8\n            },\n            children: \"Login Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 15\n        }, this),\n        type: \"warning\",\n        showIcon: true,\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 11\n      }, this) : !hasAIAccess ? /*#__PURE__*/_jsxDEV(Alert, {\n        message: requiresUpgrade ? \"Upgrade Required\" : \"AI Access Restricted\",\n        description: requiresUpgrade ? \"AI question generation requires a premium subscription. Please upgrade your account.\" : \"AI features are not available for your account. Please contact support.\",\n        type: \"error\",\n        showIcon: true,\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 11\n      }, this) : sessionExpiringSoon ? /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Session Expiring Soon\",\n        description: `Your session will expire in ${timeUntilExpiry}. Consider refreshing your login.`,\n        type: \"warning\",\n        showIcon: true,\n        className: \"mb-4\",\n        action: /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          onClick: () => setShowLoginModal(true),\n          children: \"Refresh Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"AI Features Ready\",\n        description: `Welcome ${user === null || user === void 0 ? void 0 : user.name}! You have full access to AI question generation.`,\n        type: \"success\",\n        showIcon: true,\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 11\n      }, this), isGenerating && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Generating Questions\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"AI is generating your questions. This may take a few moments...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: generationProgress,\n            status: \"active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 15\n        }, this),\n        type: \"info\",\n        showIcon: true,\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: onFinish,\n        disabled: isGenerating || !hasAIAccess || authLoading,\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              message: \"Exam Selection\",\n              description: \"You can either select an existing exam or create a new one using the auto-generate feature. Questions can also be generated independently without an exam.\",\n              type: \"info\",\n              showIcon: true,\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 16,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"examId\",\n              label: \"Target Exam (Optional)\",\n              extra: \"Leave empty to generate standalone questions, or select an existing exam\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"Optional: Choose an existing exam\",\n                allowClear: true,\n                children: exams && exams.length > 0 && exams.map(exam => exam && exam._id ? /*#__PURE__*/_jsxDEV(Option, {\n                  value: exam._id,\n                  children: [exam.name, \" - \", exam.category]\n                }, exam._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 23\n                }, this) : null)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Or Create New Exam\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"dashed\",\n                icon: /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 25\n                }, this),\n                onClick: openAutoGenerateModal,\n                style: {\n                  width: \"100%\"\n                },\n                disabled: isGenerating || !hasAIAccess || authLoading,\n                children: \"Auto-Generate New Exam\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"level\",\n              label: \"Education Level\",\n              rules: [{\n                required: true,\n                message: \"Please select a level\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"Choose education level\",\n                onChange: handleLevelChange,\n                children: levelOptions.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"class\",\n              label: \"Class\",\n              rules: [{\n                required: true,\n                message: \"Please select a class\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"Choose class\",\n                disabled: !selectedLevel,\n                onChange: handleClassChange,\n                children: selectedLevel && ((_classOptions$selecte = classOptions[selectedLevel]) === null || _classOptions$selecte === void 0 ? void 0 : _classOptions$selecte.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                  value: cls,\n                  children: [\"Class \", cls]\n                }, cls, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 21\n                }, this)))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"subjects\",\n              label: \"Subjects\",\n              rules: [{\n                required: true,\n                message: \"Please select at least one subject\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                mode: \"multiple\",\n                placeholder: \"Choose subjects\",\n                disabled: !selectedLevel,\n                onChange: handleSubjectsChange,\n                children: availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(Option, {\n                  value: subject,\n                  children: subject\n                }, subject, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"selectedSyllabus\",\n              label: \"Choose Specific Syllabus (Optional)\",\n              extra: \"Select a specific syllabus for AI to reference when generating questions. Leave empty to use default curriculum.\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"Select a specific syllabus (optional)\",\n                disabled: !selectedLevel,\n                allowClear: true,\n                onChange: handleSyllabusChange,\n                children: availableSyllabuses.map(syllabus => /*#__PURE__*/_jsxDEV(Option, {\n                  value: syllabus._id,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: 'bold',\n                        color: '#1890ff'\n                      },\n                      children: [\"\\uD83D\\uDCDA \", syllabus.title]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '12px',\n                        color: '#666',\n                        marginTop: '2px'\n                      },\n                      children: [\"Subject: \", syllabus.subject, \" \\u2022 Classes: \", syllabus.classes.join(', '), \" \\u2022 Quality: \", syllabus.qualityScore || 'N/A', \"% \\u2022 Status: \", syllabus.processingStatus]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 614,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 23\n                  }, this)\n                }, syllabus._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"questionTypes\",\n              label: \"Question Types\",\n              rules: [{\n                required: true,\n                message: \"Please select at least one question type\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n                options: questionTypeOptions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"difficultyLevels\",\n              label: \"Difficulty Levels\",\n              rules: [{\n                required: true,\n                message: \"Please select at least one difficulty level\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n                options: difficultyOptions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"totalQuestions\",\n              label: \"Total Questions\",\n              rules: [{\n                required: true,\n                message: \"Please enter total questions\"\n              }, {\n                type: \"number\",\n                min: 1,\n                max: 50,\n                message: \"Must be between 1 and 50\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 50,\n                placeholder: \"Enter total questions\",\n                style: {\n                  width: \"100%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          children: \"Question Distribution\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this), selectedLevel && selectedClass && selectedSubjects.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"Tanzania Syllabus Information\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Level:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 22\n              }, this), \" \", selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Class:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 22\n              }, this), \" \", selectedClass]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Subjects:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 22\n              }, this), \" \", selectedSubjects.join(\", \")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Available Topics:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 22\n              }, this), \" \", availableTopics.length, \" topics from Tanzania National Curriculum\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Auto-generate:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 22\n              }, this), \" Use the button above to create a new exam with proper structure\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: [\"questionDistribution\", \"multiple_choice\"],\n              label: \"Multiple Choice\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                placeholder: \"0\",\n                style: {\n                  width: \"100%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: [\"questionDistribution\", \"fill_blank\"],\n              label: \"Fill in the Blank\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                placeholder: \"0\",\n                style: {\n                  width: \"100%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: [\"questionDistribution\", \"picture_based\"],\n              label: \"Picture-based\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                placeholder: \"0\",\n                style: {\n                  width: \"100%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"syllabusTopics\",\n          label: `Tanzania Syllabus Topics (${availableTopics.length} available)`,\n          extra: availableTopics.length === 0 ? \"Select level, class, and subjects to see available topics\" : \"Select specific topics from Tanzania National Curriculum\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            mode: \"multiple\",\n            placeholder: availableTopics.length === 0 ? \"No topics available - select level, class, and subjects first\" : \"Choose specific topics from Tanzania syllabus\",\n            style: {\n              width: \"100%\"\n            },\n            disabled: availableTopics.length === 0,\n            optionFilterProp: \"children\",\n            showSearch: true,\n            filterOption: (input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0,\n            children: availableTopics.map((topic, index) => /*#__PURE__*/_jsxDEV(Option, {\n              value: topic.topicName,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: topic.topicName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: \"12px\",\n                    color: \"#666\"\n                  },\n                  children: [topic.subject, \" \\u2022 Difficulty: \", topic.difficulty]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 21\n                }, this), topic.subtopics && topic.subtopics.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: \"11px\",\n                    color: \"#999\"\n                  },\n                  children: [\"Subtopics: \", topic.subtopics.slice(0, 3).join(\", \"), topic.subtopics.length > 3 && ` +${topic.subtopics.length - 3} more`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 19\n              }, this)\n            }, `${topic.subject}-${topic.topicName}-${index}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: onBack,\n            disabled: isGenerating,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 762,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            loading: isGenerating,\n            disabled: !hasAIAccess || authLoading,\n            icon: /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 21\n            }, this),\n            children: isGenerating ? \"Generating...\" : !hasAIAccess ? \"Login Required\" : \"Generate Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AutoGenerateExamModal, {\n      visible: showAutoGenerateModal,\n      onCancel: () => setShowAutoGenerateModal(false),\n      onSuccess: handleAutoGenerateExamSuccess,\n      prefilledData: {\n        level: selectedLevel,\n        class: selectedClass,\n        subjects: selectedSubjects\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 778,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AILoginModal, {\n      visible: showLoginModal,\n      onCancel: () => setShowLoginModal(false),\n      onSuccess: userData => {\n        handleLoginSuccess(userData);\n        setShowLoginModal(false);\n      },\n      title: \"AI Features Login Required\",\n      description: \"Please login to access AI question generation features. Your session may have expired or you need enhanced permissions.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 789,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 383,\n    columnNumber: 5\n  }, this);\n}\n_s(QuestionGenerationForm, \"nMbP+V0Jfat1gF8meRKeCkNhaio=\", false, function () {\n  return [useDispatch, Form.useForm, useAIAuth];\n});\n_c = QuestionGenerationForm;\nexport default QuestionGenerationForm;\nvar _c;\n$RefreshReg$(_c, \"QuestionGenerationForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "Card", "Form", "Select", "InputNumber", "<PERSON><PERSON>", "Row", "Col", "Checkbox", "message", "Divider", "<PERSON><PERSON>", "Progress", "FaArrowLeft", "FaRobot", "HideLoading", "ShowLoading", "getAllExams", "generateQuestions", "getSubjectsForLevel", "getSyllabusTopics", "getSyllabusesForAI", "AutoGenerateExamModal", "AILoginModal", "useAIAuth", "jsxDEV", "_jsxDEV", "Option", "QuestionGenerationForm", "onBack", "onSuccess", "_s", "_classOptions$selecte", "dispatch", "form", "useForm", "isAuthenticated", "hasAIAccess", "user", "loading", "authLoading", "requiresUpgrade", "<PERSON><PERSON><PERSON><PERSON>", "handleLoginSuccess", "requireAIAuth", "sessionExpiringSoon", "timeUntilExpiry", "exams", "setExams", "availableSubjects", "setAvailableSubjects", "availableTopics", "setAvailableTopics", "availableSyllabuses", "setAvailableSyllabuses", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubjects", "setSelectedSubjects", "selectedSyllabus", "setSelectedSyllabus", "isGenerating", "setIsGenerating", "generationProgress", "setGenerationProgress", "showAutoGenerateModal", "setShowAutoGenerateModal", "showLoginModal", "setShowLoginModal", "fetchExams", "response", "success", "Array", "isArray", "data", "validExams", "filter", "exam", "_id", "error", "handleLevelChange", "level", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "class", "undefined", "subjects", "syllabusTopics", "syllabusResponse", "console", "log", "length", "warn", "handleSyllabusChange", "syllabusId", "<PERSON><PERSON><PERSON><PERSON>", "find", "s", "title", "classes", "join", "subject", "includes", "handleClassChange", "className", "fetchTopicsForSubjects", "handleSubjectsChange", "allTopics", "subjectTopics", "topics", "map", "topic", "fullName", "topicName", "push", "handleAutoGenerateExamSuccess", "newExam", "updatedExams", "examId", "name", "openAutoGenerateModal", "onFinish", "values", "totalDistribution", "Object", "questionDistribution", "reduce", "sum", "count", "totalQuestions", "warning", "auth<PERSON><PERSON><PERSON>", "reason", "payload", "questionTypes", "difficultyLevels", "selectedSyllabusId", "userId", "info", "setTimeout", "errorMessage", "code", "status", "errorData", "requiresLogin", "upgradeRequired", "_error$response$data", "request", "questionTypeOptions", "label", "value", "difficultyOptions", "levelOptions", "classOptions", "primary", "ordinary_secondary", "advanced_secondary", "children", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "description", "showIcon", "size", "style", "marginTop", "action", "percent", "layout", "disabled", "gutter", "xs", "marginBottom", "md", "<PERSON><PERSON>", "extra", "placeholder", "allowClear", "category", "width", "rules", "required", "onChange", "option", "cls", "mode", "syllabus", "fontWeight", "color", "fontSize", "qualityScore", "processingStatus", "Group", "options", "min", "max", "char<PERSON>t", "toUpperCase", "slice", "optionFilterProp", "showSearch", "filterOption", "input", "toLowerCase", "indexOf", "index", "difficulty", "subtopics", "htmlType", "visible", "onCancel", "prefilledData", "userData", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/AIQuestionGeneration/QuestionGenerationForm.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport {\n  Card,\n  Form,\n  Select,\n  InputNumber,\n  Button,\n  Row,\n  Col,\n  Checkbox,\n  message,\n  Divider,\n  Alert,\n  Progress\n} from \"antd\";\nimport { FaArrowLeft, FaRobot } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllExams } from \"../../../apicalls/exams\";\nimport {\n  generateQuestions,\n  getSubjectsForLevel,\n  getSyllabusTopics\n} from \"../../../apicalls/aiQuestions\";\nimport { getSyllabusesForAI } from \"../../../apicalls/syllabus\";\nimport AutoGenerateExamModal from \"./AutoGenerateExamModal\";\nimport AILoginModal from \"../../../components/AILoginModal\";\nimport { useAIAuth } from \"../../../hooks/useAIAuth\";\n\nconst { Option } = Select;\n\nfunction QuestionGenerationForm({ onBack, onSuccess }) {\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n\n  // Enhanced authentication\n  const {\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    loading: authLoading,\n    requiresUpgrade,\n    needsLogin,\n    handleLoginSuccess,\n    requireAIAuth,\n    sessionExpiringSoon,\n    timeUntilExpiry\n  } = useAIAuth();\n\n  const [exams, setExams] = useState([]);\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [availableTopics, setAvailableTopics] = useState([]);\n  const [availableSyllabuses, setAvailableSyllabuses] = useState([]);\n  const [selectedLevel, setSelectedLevel] = useState(\"\");\n  const [selectedClass, setSelectedClass] = useState(\"\");\n  const [selectedSubjects, setSelectedSubjects] = useState([]);\n  const [selectedSyllabus, setSelectedSyllabus] = useState(null);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationProgress, setGenerationProgress] = useState(0);\n  const [showAutoGenerateModal, setShowAutoGenerateModal] = useState(false);\n  const [showLoginModal, setShowLoginModal] = useState(false);\n\n  const fetchExams = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      if (response.success && Array.isArray(response.data)) {\n        // Filter out any null or undefined values\n        const validExams = response.data.filter(exam => exam && exam._id);\n        setExams(validExams);\n      } else {\n        message.error(\"Failed to fetch exams\");\n        setExams([]); // Ensure exams is always an array\n      }\n    } catch (error) {\n      message.error(\"Error fetching exams\");\n      setExams([]); // Ensure exams is always an array\n    } finally {\n      dispatch(HideLoading());\n    }\n  }, [dispatch]);\n\n  useEffect(() => {\n    // Only fetch exams if we have authentication, otherwise let the auth hook handle it\n    if (isAuthenticated && hasAIAccess && !authLoading) {\n      fetchExams();\n    }\n  }, [isAuthenticated, hasAIAccess, authLoading, fetchExams]);\n\n  const handleLevelChange = async (level) => {\n    setSelectedLevel(level);\n    setSelectedClass(\"\");\n    setSelectedSubjects([]);\n    setAvailableTopics([]);\n    setSelectedSyllabus(null);\n    form.setFieldsValue({\n      class: undefined,\n      subjects: [],\n      syllabusTopics: [],\n      selectedSyllabus: undefined\n    });\n\n    try {\n      // Fetch subjects for this level\n      const response = await getSubjectsForLevel(level);\n      if (response.success) {\n        setAvailableSubjects(response.data);\n      } else {\n        message.error(\"Failed to fetch subjects\");\n      }\n\n      // Fetch available syllabuses for this level\n      const syllabusResponse = await getSyllabusesForAI(level);\n      if (syllabusResponse.success) {\n        setAvailableSyllabuses(syllabusResponse.data);\n        console.log(`📚 Found ${syllabusResponse.data.length} syllabuses for ${level}`);\n      } else {\n        setAvailableSyllabuses([]);\n        console.warn(`⚠️ No syllabuses found for ${level}`);\n      }\n    } catch (error) {\n      message.error(\"Error fetching subjects and syllabuses\");\n      setAvailableSyllabuses([]);\n    }\n  };\n\n  const handleSyllabusChange = (syllabusId) => {\n    const selectedSyll = availableSyllabuses.find(s => s._id === syllabusId);\n    setSelectedSyllabus(selectedSyll);\n\n    if (selectedSyll) {\n      console.log(`📚 Selected syllabus: ${selectedSyll.title}`);\n      console.log(`📚 Covers classes: ${selectedSyll.classes.join(', ')}`);\n      console.log(`📚 Subject: ${selectedSyll.subject}`);\n\n      // Auto-fill subject if it matches and is available\n      if (selectedSyll.subject && availableSubjects.includes(selectedSyll.subject)) {\n        form.setFieldsValue({ subjects: [selectedSyll.subject] });\n        setSelectedSubjects([selectedSyll.subject]);\n      }\n    }\n  };\n\n  const handleClassChange = (className) => {\n    setSelectedClass(className);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If subjects are already selected, fetch topics\n    if (selectedSubjects.length > 0) {\n      fetchTopicsForSubjects(selectedLevel, className, selectedSubjects);\n    }\n  };\n\n  const handleSubjectsChange = (subjects) => {\n    setSelectedSubjects(subjects);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If class is selected, fetch topics\n    if (selectedClass) {\n      fetchTopicsForSubjects(selectedLevel, selectedClass, subjects);\n    }\n\n    // Note: Auto-generate exam functionality moved to modal\n  };\n\n  const fetchTopicsForSubjects = async (level, className, subjects) => {\n    if (!level || !className || subjects.length === 0) return;\n\n    try {\n      const allTopics = [];\n\n      for (const subject of subjects) {\n        const response = await getSyllabusTopics(level, className, subject);\n        if (response.success) {\n          const subjectTopics = response.data.topics.map(topic => ({\n            ...topic,\n            subject: subject,\n            fullName: `${subject}: ${topic.topicName}`,\n          }));\n          allTopics.push(...subjectTopics);\n        }\n      }\n\n      setAvailableTopics(allTopics);\n    } catch (error) {\n      console.error(\"Error fetching topics:\", error);\n      message.error(\"Failed to fetch syllabus topics\");\n    }\n  };\n\n  const handleAutoGenerateExamSuccess = (newExam) => {\n    // Add the new exam to the list and select it\n    if (newExam && newExam._id) {\n      const updatedExams = [...exams, newExam];\n      setExams(updatedExams);\n      form.setFieldsValue({ examId: newExam._id });\n      setShowAutoGenerateModal(false);\n      message.success(`Exam created successfully: ${newExam.name}`);\n    } else {\n      message.error(\"Invalid exam data received\");\n      setShowAutoGenerateModal(false);\n    }\n  };\n\n  const openAutoGenerateModal = () => {\n    setShowAutoGenerateModal(true);\n  };\n\n  const onFinish = async (values) => {\n    console.log(\"🚀 Form submission started\");\n    console.log(\"📝 Form values:\", values);\n\n    try {\n      setIsGenerating(true);\n      setGenerationProgress(10);\n\n      // Validate question distribution\n      const totalDistribution = Object.values(values.questionDistribution || {}).reduce((sum, count) => sum + (count || 0), 0);\n      console.log(\"📊 Total distribution:\", totalDistribution, \"Total questions:\", values.totalQuestions);\n\n      if (totalDistribution !== values.totalQuestions) {\n        console.error(\"❌ Distribution validation failed\");\n        message.error(\"Question distribution must equal total questions\");\n        setIsGenerating(false);\n        return;\n      }\n\n      console.log(\"✅ Distribution validation passed\");\n\n      setGenerationProgress(30);\n\n      // Check authentication and AI access\n      if (!isAuthenticated || !hasAIAccess) {\n        setIsGenerating(false);\n        setShowLoginModal(true);\n        message.warning(\"Please login to access AI question generation features.\");\n        return;\n      }\n\n      // Double-check with server-side validation\n      const authCheck = await requireAIAuth();\n      if (!authCheck.success) {\n        setIsGenerating(false);\n\n        switch (authCheck.reason) {\n          case 'not_authenticated':\n          case 'refresh_failed':\n            setShowLoginModal(true);\n            message.warning(\"Please login to generate AI questions.\");\n            return;\n          case 'no_ai_access':\n            message.error(\"AI features are not available for your account.\");\n            return;\n          case 'requires_upgrade':\n            message.warning(\"AI question generation requires a premium subscription. Please upgrade your account.\");\n            return;\n          default:\n            setShowLoginModal(true);\n            message.warning(\"Authentication check failed. Please login again.\");\n            return;\n        }\n      }\n\n      const payload = {\n        examId: values.examId,\n        questionTypes: values.questionTypes,\n        subjects: values.subjects,\n        level: values.level,\n        class: values.class,\n        difficultyLevels: values.difficultyLevels,\n        syllabusTopics: values.syllabusTopics || [],\n        totalQuestions: values.totalQuestions,\n        questionDistribution: values.questionDistribution,\n        selectedSyllabusId: values.selectedSyllabus || null,\n        userId: user._id,\n      };\n\n      console.log(\"📤 Sending payload:\", payload);\n\n      setGenerationProgress(50);\n\n      console.log(\"🌐 Making API call to generate questions...\");\n\n      // Show progress message to user\n      message.info(\"AI is generating your questions... This may take a few minutes.\", 5);\n\n      const response = await generateQuestions(payload);\n      console.log(\"📥 API response received:\", response);\n\n      setGenerationProgress(90);\n\n      if (response.success) {\n        setGenerationProgress(100);\n        message.success(\"Questions generated successfully!\");\n        setTimeout(() => {\n          onSuccess();\n        }, 1000);\n      } else {\n        message.error(response.message || \"Failed to generate questions\");\n      }\n    } catch (error) {\n      console.error(\"Question generation error:\", error);\n\n      // More detailed error handling\n      let errorMessage = \"Error generating questions\";\n\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n        // Handle timeout errors specifically\n        errorMessage = \"Question generation is taking longer than expected. This might be due to high server load. Please try again with fewer questions or check your internet connection.\";\n      } else if (error.response) {\n        // Server responded with error status\n        console.error(\"Server error response:\", error.response.data);\n        console.error(\"Server error status:\", error.response.status);\n\n        if (error.response.status === 401) {\n          // Handle authentication errors for AI requests\n          const errorData = error.response.data;\n\n          if (errorData?.requiresLogin) {\n            // Show the AI login modal instead of redirecting\n            setShowLoginModal(true);\n            errorMessage = errorData.message || \"Authentication required for AI features.\";\n          } else {\n            errorMessage = errorData?.message || \"Authentication failed. Please login again.\";\n          }\n        } else if (error.response.status === 403) {\n          // Handle permission/subscription errors\n          const errorData = error.response.data;\n          if (errorData?.upgradeRequired) {\n            errorMessage = \"AI question generation requires a premium subscription. Please upgrade your account.\";\n          } else {\n            errorMessage = errorData?.message || \"Access denied for AI features.\";\n          }\n        } else if (error.response.status === 504 || error.response.status === 502) {\n          errorMessage = \"Server timeout. The AI generation process is taking longer than expected. Please try again with fewer questions.\";\n        } else {\n          errorMessage = error.response.data?.message || `Server error: ${error.response.status}`;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        console.error(\"Network error:\", error.request);\n        errorMessage = \"Network error - please check your connection. If the problem persists, try generating fewer questions at once.\";\n      } else {\n        // Something else happened\n        console.error(\"Error:\", error.message);\n        errorMessage = error.message || \"Unknown error occurred\";\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setIsGenerating(false);\n      setGenerationProgress(0);\n    }\n  };\n\n  const questionTypeOptions = [\n    { label: \"Multiple Choice\", value: \"multiple_choice\" },\n    { label: \"Fill in the Blank\", value: \"fill_blank\" },\n    { label: \"Picture-based\", value: \"picture_based\" },\n  ];\n\n  const difficultyOptions = [\n    { label: \"Easy\", value: \"easy\" },\n    { label: \"Medium\", value: \"medium\" },\n    { label: \"Hard\", value: \"hard\" },\n  ];\n\n  const levelOptions = [\n    { label: \"Primary Education (Standards I-VI)\", value: \"primary\" },\n    { label: \"Ordinary Secondary (Forms I-IV)\", value: \"ordinary_secondary\" },\n    { label: \"Advanced Secondary (Forms V-VI)\", value: \"advanced_secondary\" },\n  ];\n\n  const classOptions = {\n    primary: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\"], // Standards I-VI (TIE Primary)\n    ordinary_secondary: [\"I\", \"II\", \"III\", \"IV\"], // Forms I-IV (TIE Ordinary Secondary)\n    advanced_secondary: [\"V\", \"VI\"], // Forms V-VI (TIE Advanced Secondary)\n  };\n\n  return (\n    <div className=\"question-generation-form\">\n      <Card\n        title={\n          <div className=\"form-header\">\n            <Button\n              type=\"text\"\n              icon={<FaArrowLeft />}\n              onClick={onBack}\n              className=\"back-button\"\n            >\n              Back to Dashboard\n            </Button>\n            <div className=\"title-section\">\n              <FaRobot className=\"title-icon\" />\n              <span>Generate AI Questions</span>\n            </div>\n          </div>\n        }\n      >\n        {/* Authentication Status */}\n        {authLoading ? (\n          <Alert\n            message=\"Checking Authentication...\"\n            description=\"Verifying your access to AI features.\"\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !isAuthenticated ? (\n          <Alert\n            message=\"Login Required\"\n            description={\n              <div>\n                <p>Please login to access AI question generation features.</p>\n                <Button\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => setShowLoginModal(true)}\n                  style={{ marginTop: 8 }}\n                >\n                  Login Now\n                </Button>\n              </div>\n            }\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !hasAIAccess ? (\n          <Alert\n            message={requiresUpgrade ? \"Upgrade Required\" : \"AI Access Restricted\"}\n            description={\n              requiresUpgrade\n                ? \"AI question generation requires a premium subscription. Please upgrade your account.\"\n                : \"AI features are not available for your account. Please contact support.\"\n            }\n            type=\"error\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : sessionExpiringSoon ? (\n          <Alert\n            message=\"Session Expiring Soon\"\n            description={`Your session will expire in ${timeUntilExpiry}. Consider refreshing your login.`}\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n            action={\n              <Button\n                size=\"small\"\n                onClick={() => setShowLoginModal(true)}\n              >\n                Refresh Login\n              </Button>\n            }\n          />\n        ) : (\n          <Alert\n            message=\"AI Features Ready\"\n            description={`Welcome ${user?.name}! You have full access to AI question generation.`}\n            type=\"success\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        {isGenerating && (\n          <Alert\n            message=\"Generating Questions\"\n            description={\n              <div>\n                <p>AI is generating your questions. This may take a few moments...</p>\n                <Progress percent={generationProgress} status=\"active\" />\n              </div>\n            }\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={onFinish}\n          disabled={isGenerating || !hasAIAccess || authLoading}\n        >\n          <Row gutter={[16, 16]}>\n            <Col xs={24}>\n              <Alert\n                message=\"Exam Selection\"\n                description=\"You can either select an existing exam or create a new one using the auto-generate feature. Questions can also be generated independently without an exam.\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 16 }}\n              />\n            </Col>\n\n            <Col xs={24} md={16}>\n              <Form.Item\n                name=\"examId\"\n                label=\"Target Exam (Optional)\"\n                extra=\"Leave empty to generate standalone questions, or select an existing exam\"\n              >\n                <Select\n                  placeholder=\"Optional: Choose an existing exam\"\n                  allowClear\n                >\n                  {exams && exams.length > 0 && exams.map((exam) => (\n                    exam && exam._id ? (\n                      <Option key={exam._id} value={exam._id}>\n                        {exam.name} - {exam.category}\n                      </Option>\n                    ) : null\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item label=\"Or Create New Exam\">\n                <Button\n                  type=\"dashed\"\n                  icon={<FaRobot />}\n                  onClick={openAutoGenerateModal}\n                  style={{ width: \"100%\" }}\n                  disabled={isGenerating || !hasAIAccess || authLoading}\n                >\n                  Auto-Generate New Exam\n                </Button>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"level\"\n                label=\"Education Level\"\n                rules={[{ required: true, message: \"Please select a level\" }]}\n              >\n                <Select \n                  placeholder=\"Choose education level\"\n                  onChange={handleLevelChange}\n                >\n                  {levelOptions.map((option) => (\n                    <Option key={option.value} value={option.value}>\n                      {option.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"class\"\n                label=\"Class\"\n                rules={[{ required: true, message: \"Please select a class\" }]}\n              >\n                <Select\n                  placeholder=\"Choose class\"\n                  disabled={!selectedLevel}\n                  onChange={handleClassChange}\n                >\n                  {selectedLevel && classOptions[selectedLevel]?.map((cls) => (\n                    <Option key={cls} value={cls}>\n                      Class {cls}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"subjects\"\n                label=\"Subjects\"\n                rules={[{ required: true, message: \"Please select at least one subject\" }]}\n              >\n                <Select\n                  mode=\"multiple\"\n                  placeholder=\"Choose subjects\"\n                  disabled={!selectedLevel}\n                  onChange={handleSubjectsChange}\n                >\n                  {availableSubjects.map((subject) => (\n                    <Option key={subject} value={subject}>\n                      {subject}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"selectedSyllabus\"\n                label=\"Choose Specific Syllabus (Optional)\"\n                extra=\"Select a specific syllabus for AI to reference when generating questions. Leave empty to use default curriculum.\"\n              >\n                <Select\n                  placeholder=\"Select a specific syllabus (optional)\"\n                  disabled={!selectedLevel}\n                  allowClear\n                  onChange={handleSyllabusChange}\n                >\n                  {availableSyllabuses.map((syllabus) => (\n                    <Option key={syllabus._id} value={syllabus._id}>\n                      <div>\n                        <div style={{ fontWeight: 'bold', color: '#1890ff' }}>\n                          📚 {syllabus.title}\n                        </div>\n                        <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>\n                          Subject: {syllabus.subject} • Classes: {syllabus.classes.join(', ')} •\n                          Quality: {syllabus.qualityScore || 'N/A'}% •\n                          Status: {syllabus.processingStatus}\n                        </div>\n                      </div>\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"questionTypes\"\n                label=\"Question Types\"\n                rules={[{ required: true, message: \"Please select at least one question type\" }]}\n              >\n                <Checkbox.Group options={questionTypeOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"difficultyLevels\"\n                label=\"Difficulty Levels\"\n                rules={[{ required: true, message: \"Please select at least one difficulty level\" }]}\n              >\n                <Checkbox.Group options={difficultyOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"totalQuestions\"\n                label=\"Total Questions\"\n                rules={[\n                  { required: true, message: \"Please enter total questions\" },\n                  { type: \"number\", min: 1, max: 50, message: \"Must be between 1 and 50\" }\n                ]}\n              >\n                <InputNumber\n                  min={1}\n                  max={50}\n                  placeholder=\"Enter total questions\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Divider>Question Distribution</Divider>\n\n          {selectedLevel && selectedClass && selectedSubjects.length > 0 && (\n            <Alert\n              message=\"Tanzania Syllabus Information\"\n              description={\n                <div>\n                  <p><strong>Level:</strong> {selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</p>\n                  <p><strong>Class:</strong> {selectedClass}</p>\n                  <p><strong>Subjects:</strong> {selectedSubjects.join(\", \")}</p>\n                  <p><strong>Available Topics:</strong> {availableTopics.length} topics from Tanzania National Curriculum</p>\n                  <p><strong>Auto-generate:</strong> Use the button above to create a new exam with proper structure</p>\n                </div>\n              }\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n          )}\n\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"multiple_choice\"]}\n                label=\"Multiple Choice\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"fill_blank\"]}\n                label=\"Fill in the Blank\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"picture_based\"]}\n                label=\"Picture-based\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"syllabusTopics\"\n            label={`Tanzania Syllabus Topics (${availableTopics.length} available)`}\n            extra={availableTopics.length === 0 ? \"Select level, class, and subjects to see available topics\" : \"Select specific topics from Tanzania National Curriculum\"}\n          >\n            <Select\n              mode=\"multiple\"\n              placeholder={availableTopics.length === 0 ? \"No topics available - select level, class, and subjects first\" : \"Choose specific topics from Tanzania syllabus\"}\n              style={{ width: \"100%\" }}\n              disabled={availableTopics.length === 0}\n              optionFilterProp=\"children\"\n              showSearch\n              filterOption={(input, option) =>\n                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0\n              }\n            >\n              {availableTopics.map((topic, index) => (\n                <Option key={`${topic.subject}-${topic.topicName}-${index}`} value={topic.topicName}>\n                  <div>\n                    <strong>{topic.topicName}</strong>\n                    <div style={{ fontSize: \"12px\", color: \"#666\" }}>\n                      {topic.subject} • Difficulty: {topic.difficulty}\n                    </div>\n                    {topic.subtopics && topic.subtopics.length > 0 && (\n                      <div style={{ fontSize: \"11px\", color: \"#999\" }}>\n                        Subtopics: {topic.subtopics.slice(0, 3).join(\", \")}\n                        {topic.subtopics.length > 3 && ` +${topic.subtopics.length - 3} more`}\n                      </div>\n                    )}\n                  </div>\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <div className=\"form-actions\">\n            <Button onClick={onBack} disabled={isGenerating}>\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={isGenerating}\n              disabled={!hasAIAccess || authLoading}\n              icon={<FaRobot />}\n            >\n              {isGenerating ? \"Generating...\" : !hasAIAccess ? \"Login Required\" : \"Generate Questions\"}\n            </Button>\n          </div>\n        </Form>\n      </Card>\n\n      <AutoGenerateExamModal\n        visible={showAutoGenerateModal}\n        onCancel={() => setShowAutoGenerateModal(false)}\n        onSuccess={handleAutoGenerateExamSuccess}\n        prefilledData={{\n          level: selectedLevel,\n          class: selectedClass,\n          subjects: selectedSubjects,\n        }}\n      />\n\n      <AILoginModal\n        visible={showLoginModal}\n        onCancel={() => setShowLoginModal(false)}\n        onSuccess={(userData) => {\n          handleLoginSuccess(userData);\n          setShowLoginModal(false);\n        }}\n        title=\"AI Features Login Required\"\n        description=\"Please login to access AI question generation features. Your session may have expired or you need enhanced permissions.\"\n      />\n    </div>\n  );\n}\n\nexport default QuestionGenerationForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,KAAK,EACLC,QAAQ,QACH,MAAM;AACb,SAASC,WAAW,EAAEC,OAAO,QAAQ,gBAAgB;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SACEC,iBAAiB,EACjBC,mBAAmB,EACnBC,iBAAiB,QACZ,+BAA+B;AACtC,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,SAASC,SAAS,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAM;EAAEC;AAAO,CAAC,GAAGxB,MAAM;AAEzB,SAASyB,sBAAsBA,CAAC;EAAEC,MAAM;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACrD,MAAMC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAM;IACJC,eAAe;IACfC,WAAW;IACXC,IAAI;IACJC,OAAO,EAAEC,WAAW;IACpBC,eAAe;IACfC,UAAU;IACVC,kBAAkB;IAClBC,aAAa;IACbC,mBAAmB;IACnBC;EACF,CAAC,GAAGtB,SAAS,CAAC,CAAC;EAEf,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACsE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM0E,UAAU,GAAGxE,WAAW,CAAC,YAAY;IACzC,IAAI;MACFkC,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMwD,QAAQ,GAAG,MAAMvD,WAAW,CAAC,CAAC;MACpC,IAAIuD,QAAQ,CAACC,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAACI,IAAI,CAAC,EAAE;QACpD;QACA,MAAMC,UAAU,GAAGL,QAAQ,CAACI,IAAI,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC;QACjEhC,QAAQ,CAAC6B,UAAU,CAAC;MACtB,CAAC,MAAM;QACLpE,OAAO,CAACwE,KAAK,CAAC,uBAAuB,CAAC;QACtCjC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdxE,OAAO,CAACwE,KAAK,CAAC,sBAAsB,CAAC;MACrCjC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,SAAS;MACRf,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACkB,QAAQ,CAAC,CAAC;EAEdnC,SAAS,CAAC,MAAM;IACd;IACA,IAAIsC,eAAe,IAAIC,WAAW,IAAI,CAACG,WAAW,EAAE;MAClD+B,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACnC,eAAe,EAAEC,WAAW,EAAEG,WAAW,EAAE+B,UAAU,CAAC,CAAC;EAE3D,MAAMW,iBAAiB,GAAG,MAAOC,KAAK,IAAK;IACzC3B,gBAAgB,CAAC2B,KAAK,CAAC;IACvBzB,gBAAgB,CAAC,EAAE,CAAC;IACpBE,mBAAmB,CAAC,EAAE,CAAC;IACvBR,kBAAkB,CAAC,EAAE,CAAC;IACtBU,mBAAmB,CAAC,IAAI,CAAC;IACzB5B,IAAI,CAACkD,cAAc,CAAC;MAClBC,KAAK,EAAEC,SAAS;MAChBC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,EAAE;MAClB3B,gBAAgB,EAAEyB;IACpB,CAAC,CAAC;IAEF,IAAI;MACF;MACA,MAAMd,QAAQ,GAAG,MAAMrD,mBAAmB,CAACgE,KAAK,CAAC;MACjD,IAAIX,QAAQ,CAACC,OAAO,EAAE;QACpBvB,oBAAoB,CAACsB,QAAQ,CAACI,IAAI,CAAC;MACrC,CAAC,MAAM;QACLnE,OAAO,CAACwE,KAAK,CAAC,0BAA0B,CAAC;MAC3C;;MAEA;MACA,MAAMQ,gBAAgB,GAAG,MAAMpE,kBAAkB,CAAC8D,KAAK,CAAC;MACxD,IAAIM,gBAAgB,CAAChB,OAAO,EAAE;QAC5BnB,sBAAsB,CAACmC,gBAAgB,CAACb,IAAI,CAAC;QAC7Cc,OAAO,CAACC,GAAG,CAAE,YAAWF,gBAAgB,CAACb,IAAI,CAACgB,MAAO,mBAAkBT,KAAM,EAAC,CAAC;MACjF,CAAC,MAAM;QACL7B,sBAAsB,CAAC,EAAE,CAAC;QAC1BoC,OAAO,CAACG,IAAI,CAAE,8BAA6BV,KAAM,EAAC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdxE,OAAO,CAACwE,KAAK,CAAC,wCAAwC,CAAC;MACvD3B,sBAAsB,CAAC,EAAE,CAAC;IAC5B;EACF,CAAC;EAED,MAAMwC,oBAAoB,GAAIC,UAAU,IAAK;IAC3C,MAAMC,YAAY,GAAG3C,mBAAmB,CAAC4C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClB,GAAG,KAAKe,UAAU,CAAC;IACxEjC,mBAAmB,CAACkC,YAAY,CAAC;IAEjC,IAAIA,YAAY,EAAE;MAChBN,OAAO,CAACC,GAAG,CAAE,yBAAwBK,YAAY,CAACG,KAAM,EAAC,CAAC;MAC1DT,OAAO,CAACC,GAAG,CAAE,sBAAqBK,YAAY,CAACI,OAAO,CAACC,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;MACpEX,OAAO,CAACC,GAAG,CAAE,eAAcK,YAAY,CAACM,OAAQ,EAAC,CAAC;;MAElD;MACA,IAAIN,YAAY,CAACM,OAAO,IAAIrD,iBAAiB,CAACsD,QAAQ,CAACP,YAAY,CAACM,OAAO,CAAC,EAAE;QAC5EpE,IAAI,CAACkD,cAAc,CAAC;UAAEG,QAAQ,EAAE,CAACS,YAAY,CAACM,OAAO;QAAE,CAAC,CAAC;QACzD1C,mBAAmB,CAAC,CAACoC,YAAY,CAACM,OAAO,CAAC,CAAC;MAC7C;IACF;EACF,CAAC;EAED,MAAME,iBAAiB,GAAIC,SAAS,IAAK;IACvC/C,gBAAgB,CAAC+C,SAAS,CAAC;IAC3BrD,kBAAkB,CAAC,EAAE,CAAC;IACtBlB,IAAI,CAACkD,cAAc,CAAC;MAAEI,cAAc,EAAE;IAAG,CAAC,CAAC;;IAE3C;IACA,IAAI7B,gBAAgB,CAACiC,MAAM,GAAG,CAAC,EAAE;MAC/Bc,sBAAsB,CAACnD,aAAa,EAAEkD,SAAS,EAAE9C,gBAAgB,CAAC;IACpE;EACF,CAAC;EAED,MAAMgD,oBAAoB,GAAIpB,QAAQ,IAAK;IACzC3B,mBAAmB,CAAC2B,QAAQ,CAAC;IAC7BnC,kBAAkB,CAAC,EAAE,CAAC;IACtBlB,IAAI,CAACkD,cAAc,CAAC;MAAEI,cAAc,EAAE;IAAG,CAAC,CAAC;;IAE3C;IACA,IAAI/B,aAAa,EAAE;MACjBiD,sBAAsB,CAACnD,aAAa,EAAEE,aAAa,EAAE8B,QAAQ,CAAC;IAChE;;IAEA;EACF,CAAC;;EAED,MAAMmB,sBAAsB,GAAG,MAAAA,CAAOvB,KAAK,EAAEsB,SAAS,EAAElB,QAAQ,KAAK;IACnE,IAAI,CAACJ,KAAK,IAAI,CAACsB,SAAS,IAAIlB,QAAQ,CAACK,MAAM,KAAK,CAAC,EAAE;IAEnD,IAAI;MACF,MAAMgB,SAAS,GAAG,EAAE;MAEpB,KAAK,MAAMN,OAAO,IAAIf,QAAQ,EAAE;QAC9B,MAAMf,QAAQ,GAAG,MAAMpD,iBAAiB,CAAC+D,KAAK,EAAEsB,SAAS,EAAEH,OAAO,CAAC;QACnE,IAAI9B,QAAQ,CAACC,OAAO,EAAE;UACpB,MAAMoC,aAAa,GAAGrC,QAAQ,CAACI,IAAI,CAACkC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;YACvD,GAAGA,KAAK;YACRV,OAAO,EAAEA,OAAO;YAChBW,QAAQ,EAAG,GAAEX,OAAQ,KAAIU,KAAK,CAACE,SAAU;UAC3C,CAAC,CAAC,CAAC;UACHN,SAAS,CAACO,IAAI,CAAC,GAAGN,aAAa,CAAC;QAClC;MACF;MAEAzD,kBAAkB,CAACwD,SAAS,CAAC;IAC/B,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CxE,OAAO,CAACwE,KAAK,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC;EAED,MAAMmC,6BAA6B,GAAIC,OAAO,IAAK;IACjD;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACrC,GAAG,EAAE;MAC1B,MAAMsC,YAAY,GAAG,CAAC,GAAGvE,KAAK,EAAEsE,OAAO,CAAC;MACxCrE,QAAQ,CAACsE,YAAY,CAAC;MACtBpF,IAAI,CAACkD,cAAc,CAAC;QAAEmC,MAAM,EAAEF,OAAO,CAACrC;MAAI,CAAC,CAAC;MAC5CZ,wBAAwB,CAAC,KAAK,CAAC;MAC/B3D,OAAO,CAACgE,OAAO,CAAE,8BAA6B4C,OAAO,CAACG,IAAK,EAAC,CAAC;IAC/D,CAAC,MAAM;MACL/G,OAAO,CAACwE,KAAK,CAAC,4BAA4B,CAAC;MAC3Cb,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAMqD,qBAAqB,GAAGA,CAAA,KAAM;IAClCrD,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAMsD,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjCjC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgC,MAAM,CAAC;IAEtC,IAAI;MACF3D,eAAe,CAAC,IAAI,CAAC;MACrBE,qBAAqB,CAAC,EAAE,CAAC;;MAEzB;MACA,MAAM0D,iBAAiB,GAAGC,MAAM,CAACF,MAAM,CAACA,MAAM,CAACG,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAIC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MACxHvC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiC,iBAAiB,EAAE,kBAAkB,EAAED,MAAM,CAACO,cAAc,CAAC;MAEnG,IAAIN,iBAAiB,KAAKD,MAAM,CAACO,cAAc,EAAE;QAC/CxC,OAAO,CAACT,KAAK,CAAC,kCAAkC,CAAC;QACjDxE,OAAO,CAACwE,KAAK,CAAC,kDAAkD,CAAC;QACjEjB,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MAEA0B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAE/CzB,qBAAqB,CAAC,EAAE,CAAC;;MAEzB;MACA,IAAI,CAAC9B,eAAe,IAAI,CAACC,WAAW,EAAE;QACpC2B,eAAe,CAAC,KAAK,CAAC;QACtBM,iBAAiB,CAAC,IAAI,CAAC;QACvB7D,OAAO,CAAC0H,OAAO,CAAC,yDAAyD,CAAC;QAC1E;MACF;;MAEA;MACA,MAAMC,SAAS,GAAG,MAAMxF,aAAa,CAAC,CAAC;MACvC,IAAI,CAACwF,SAAS,CAAC3D,OAAO,EAAE;QACtBT,eAAe,CAAC,KAAK,CAAC;QAEtB,QAAQoE,SAAS,CAACC,MAAM;UACtB,KAAK,mBAAmB;UACxB,KAAK,gBAAgB;YACnB/D,iBAAiB,CAAC,IAAI,CAAC;YACvB7D,OAAO,CAAC0H,OAAO,CAAC,wCAAwC,CAAC;YACzD;UACF,KAAK,cAAc;YACjB1H,OAAO,CAACwE,KAAK,CAAC,iDAAiD,CAAC;YAChE;UACF,KAAK,kBAAkB;YACrBxE,OAAO,CAAC0H,OAAO,CAAC,sFAAsF,CAAC;YACvG;UACF;YACE7D,iBAAiB,CAAC,IAAI,CAAC;YACvB7D,OAAO,CAAC0H,OAAO,CAAC,kDAAkD,CAAC;YACnE;QACJ;MACF;MAEA,MAAMG,OAAO,GAAG;QACdf,MAAM,EAAEI,MAAM,CAACJ,MAAM;QACrBgB,aAAa,EAAEZ,MAAM,CAACY,aAAa;QACnChD,QAAQ,EAAEoC,MAAM,CAACpC,QAAQ;QACzBJ,KAAK,EAAEwC,MAAM,CAACxC,KAAK;QACnBE,KAAK,EAAEsC,MAAM,CAACtC,KAAK;QACnBmD,gBAAgB,EAAEb,MAAM,CAACa,gBAAgB;QACzChD,cAAc,EAAEmC,MAAM,CAACnC,cAAc,IAAI,EAAE;QAC3C0C,cAAc,EAAEP,MAAM,CAACO,cAAc;QACrCJ,oBAAoB,EAAEH,MAAM,CAACG,oBAAoB;QACjDW,kBAAkB,EAAEd,MAAM,CAAC9D,gBAAgB,IAAI,IAAI;QACnD6E,MAAM,EAAEpG,IAAI,CAAC0C;MACf,CAAC;MAEDU,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2C,OAAO,CAAC;MAE3CpE,qBAAqB,CAAC,EAAE,CAAC;MAEzBwB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;MAE1D;MACAlF,OAAO,CAACkI,IAAI,CAAC,iEAAiE,EAAE,CAAC,CAAC;MAElF,MAAMnE,QAAQ,GAAG,MAAMtD,iBAAiB,CAACoH,OAAO,CAAC;MACjD5C,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEnB,QAAQ,CAAC;MAElDN,qBAAqB,CAAC,EAAE,CAAC;MAEzB,IAAIM,QAAQ,CAACC,OAAO,EAAE;QACpBP,qBAAqB,CAAC,GAAG,CAAC;QAC1BzD,OAAO,CAACgE,OAAO,CAAC,mCAAmC,CAAC;QACpDmE,UAAU,CAAC,MAAM;UACf9G,SAAS,CAAC,CAAC;QACb,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLrB,OAAO,CAACwE,KAAK,CAACT,QAAQ,CAAC/D,OAAO,IAAI,8BAA8B,CAAC;MACnE;IACF,CAAC,CAAC,OAAOwE,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAElD;MACA,IAAI4D,YAAY,GAAG,4BAA4B;MAE/C,IAAI5D,KAAK,CAAC6D,IAAI,KAAK,cAAc,IAAI7D,KAAK,CAACxE,OAAO,CAAC8F,QAAQ,CAAC,SAAS,CAAC,EAAE;QACtE;QACAsC,YAAY,GAAG,qKAAqK;MACtL,CAAC,MAAM,IAAI5D,KAAK,CAACT,QAAQ,EAAE;QACzB;QACAkB,OAAO,CAACT,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAACT,QAAQ,CAACI,IAAI,CAAC;QAC5Dc,OAAO,CAACT,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACT,QAAQ,CAACuE,MAAM,CAAC;QAE5D,IAAI9D,KAAK,CAACT,QAAQ,CAACuE,MAAM,KAAK,GAAG,EAAE;UACjC;UACA,MAAMC,SAAS,GAAG/D,KAAK,CAACT,QAAQ,CAACI,IAAI;UAErC,IAAIoE,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,aAAa,EAAE;YAC5B;YACA3E,iBAAiB,CAAC,IAAI,CAAC;YACvBuE,YAAY,GAAGG,SAAS,CAACvI,OAAO,IAAI,0CAA0C;UAChF,CAAC,MAAM;YACLoI,YAAY,GAAG,CAAAG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEvI,OAAO,KAAI,4CAA4C;UACnF;QACF,CAAC,MAAM,IAAIwE,KAAK,CAACT,QAAQ,CAACuE,MAAM,KAAK,GAAG,EAAE;UACxC;UACA,MAAMC,SAAS,GAAG/D,KAAK,CAACT,QAAQ,CAACI,IAAI;UACrC,IAAIoE,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEE,eAAe,EAAE;YAC9BL,YAAY,GAAG,sFAAsF;UACvG,CAAC,MAAM;YACLA,YAAY,GAAG,CAAAG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEvI,OAAO,KAAI,gCAAgC;UACvE;QACF,CAAC,MAAM,IAAIwE,KAAK,CAACT,QAAQ,CAACuE,MAAM,KAAK,GAAG,IAAI9D,KAAK,CAACT,QAAQ,CAACuE,MAAM,KAAK,GAAG,EAAE;UACzEF,YAAY,GAAG,kHAAkH;QACnI,CAAC,MAAM;UAAA,IAAAM,oBAAA;UACLN,YAAY,GAAG,EAAAM,oBAAA,GAAAlE,KAAK,CAACT,QAAQ,CAACI,IAAI,cAAAuE,oBAAA,uBAAnBA,oBAAA,CAAqB1I,OAAO,KAAK,iBAAgBwE,KAAK,CAACT,QAAQ,CAACuE,MAAO,EAAC;QACzF;MACF,CAAC,MAAM,IAAI9D,KAAK,CAACmE,OAAO,EAAE;QACxB;QACA1D,OAAO,CAACT,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACmE,OAAO,CAAC;QAC9CP,YAAY,GAAG,gHAAgH;MACjI,CAAC,MAAM;QACL;QACAnD,OAAO,CAACT,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAACxE,OAAO,CAAC;QACtCoI,YAAY,GAAG5D,KAAK,CAACxE,OAAO,IAAI,wBAAwB;MAC1D;MAEAA,OAAO,CAACwE,KAAK,CAAC4D,YAAY,CAAC;IAC7B,CAAC,SAAS;MACR7E,eAAe,CAAC,KAAK,CAAC;MACtBE,qBAAqB,CAAC,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,MAAMmF,mBAAmB,GAAG,CAC1B;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAED,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAa,CAAC,EACnD;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC,CACnD;EAED,MAAMC,iBAAiB,GAAG,CACxB;IAAEF,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,CACjC;EAED,MAAME,YAAY,GAAG,CACnB;IAAEH,KAAK,EAAE,oCAAoC;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjE;IAAED,KAAK,EAAE,iCAAiC;IAAEC,KAAK,EAAE;EAAqB,CAAC,EACzE;IAAED,KAAK,EAAE,iCAAiC;IAAEC,KAAK,EAAE;EAAqB,CAAC,CAC1E;EAED,MAAMG,YAAY,GAAG;IACnBC,OAAO,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;IAAE;IAC9CC,kBAAkB,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAAE;IAC9CC,kBAAkB,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAE;EACnC,CAAC;;EAED,oBACEnI,OAAA;IAAK+E,SAAS,EAAC,0BAA0B;IAAAqD,QAAA,gBACvCpI,OAAA,CAACzB,IAAI;MACHkG,KAAK,eACHzE,OAAA;QAAK+E,SAAS,EAAC,aAAa;QAAAqD,QAAA,gBAC1BpI,OAAA,CAACrB,MAAM;UACL0J,IAAI,EAAC,MAAM;UACXC,IAAI,eAAEtI,OAAA,CAACb,WAAW;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBC,OAAO,EAAExI,MAAO;UAChB4E,SAAS,EAAC,aAAa;UAAAqD,QAAA,EACxB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1I,OAAA;UAAK+E,SAAS,EAAC,eAAe;UAAAqD,QAAA,gBAC5BpI,OAAA,CAACZ,OAAO;YAAC2F,SAAS,EAAC;UAAY;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClC1I,OAAA;YAAAoI,QAAA,EAAM;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;MAAAN,QAAA,GAGAtH,WAAW,gBACVd,OAAA,CAACf,KAAK;QACJF,OAAO,EAAC,4BAA4B;QACpC6J,WAAW,EAAC,uCAAuC;QACnDP,IAAI,EAAC,MAAM;QACXQ,QAAQ;QACR9D,SAAS,EAAC;MAAM;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACA,CAAChI,eAAe,gBAClBV,OAAA,CAACf,KAAK;QACJF,OAAO,EAAC,gBAAgB;QACxB6J,WAAW,eACT5I,OAAA;UAAAoI,QAAA,gBACEpI,OAAA;YAAAoI,QAAA,EAAG;UAAuD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9D1I,OAAA,CAACrB,MAAM;YACL0J,IAAI,EAAC,SAAS;YACdS,IAAI,EAAC,OAAO;YACZH,OAAO,EAAEA,CAAA,KAAM/F,iBAAiB,CAAC,IAAI,CAAE;YACvCmG,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAE,CAAE;YAAAZ,QAAA,EACzB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;QACDL,IAAI,EAAC,SAAS;QACdQ,QAAQ;QACR9D,SAAS,EAAC;MAAM;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACA,CAAC/H,WAAW,gBACdX,OAAA,CAACf,KAAK;QACJF,OAAO,EAAEgC,eAAe,GAAG,kBAAkB,GAAG,sBAAuB;QACvE6H,WAAW,EACT7H,eAAe,GACX,sFAAsF,GACtF,yEACL;QACDsH,IAAI,EAAC,OAAO;QACZQ,QAAQ;QACR9D,SAAS,EAAC;MAAM;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACAvH,mBAAmB,gBACrBnB,OAAA,CAACf,KAAK;QACJF,OAAO,EAAC,uBAAuB;QAC/B6J,WAAW,EAAG,+BAA8BxH,eAAgB,mCAAmC;QAC/FiH,IAAI,EAAC,SAAS;QACdQ,QAAQ;QACR9D,SAAS,EAAC,MAAM;QAChBkE,MAAM,eACJjJ,OAAA,CAACrB,MAAM;UACLmK,IAAI,EAAC,OAAO;UACZH,OAAO,EAAEA,CAAA,KAAM/F,iBAAiB,CAAC,IAAI,CAAE;UAAAwF,QAAA,EACxC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAEF1I,OAAA,CAACf,KAAK;QACJF,OAAO,EAAC,mBAAmB;QAC3B6J,WAAW,EAAG,WAAUhI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkF,IAAK,mDAAmD;QACtFuC,IAAI,EAAC,SAAS;QACdQ,QAAQ;QACR9D,SAAS,EAAC;MAAM;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF,EAEArG,YAAY,iBACXrC,OAAA,CAACf,KAAK;QACJF,OAAO,EAAC,sBAAsB;QAC9B6J,WAAW,eACT5I,OAAA;UAAAoI,QAAA,gBACEpI,OAAA;YAAAoI,QAAA,EAAG;UAA+D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtE1I,OAAA,CAACd,QAAQ;YAACgK,OAAO,EAAE3G,kBAAmB;YAAC8E,MAAM,EAAC;UAAQ;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACN;QACDL,IAAI,EAAC,MAAM;QACXQ,QAAQ;QACR9D,SAAS,EAAC;MAAM;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF,eAED1I,OAAA,CAACxB,IAAI;QACHgC,IAAI,EAAEA,IAAK;QACX2I,MAAM,EAAC,UAAU;QACjBnD,QAAQ,EAAEA,QAAS;QACnBoD,QAAQ,EAAE/G,YAAY,IAAI,CAAC1B,WAAW,IAAIG,WAAY;QAAAsH,QAAA,gBAEtDpI,OAAA,CAACpB,GAAG;UAACyK,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAjB,QAAA,gBACpBpI,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAAAlB,QAAA,eACVpI,OAAA,CAACf,KAAK;cACJF,OAAO,EAAC,gBAAgB;cACxB6J,WAAW,EAAC,4JAA4J;cACxKP,IAAI,EAAC,MAAM;cACXQ,QAAQ;cACRE,KAAK,EAAE;gBAAEQ,YAAY,EAAE;cAAG;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAApB,QAAA,eAClBpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAC,QAAQ;cACb8B,KAAK,EAAC,wBAAwB;cAC9B8B,KAAK,EAAC,0EAA0E;cAAAtB,QAAA,eAEhFpI,OAAA,CAACvB,MAAM;gBACLkL,WAAW,EAAC,mCAAmC;gBAC/CC,UAAU;gBAAAxB,QAAA,EAET/G,KAAK,IAAIA,KAAK,CAAC6C,MAAM,GAAG,CAAC,IAAI7C,KAAK,CAACgE,GAAG,CAAEhC,IAAI,IAC3CA,IAAI,IAAIA,IAAI,CAACC,GAAG,gBACdtD,OAAA,CAACC,MAAM;kBAAgB4H,KAAK,EAAExE,IAAI,CAACC,GAAI;kBAAA8E,QAAA,GACpC/E,IAAI,CAACyC,IAAI,EAAC,KAAG,EAACzC,IAAI,CAACwG,QAAQ;gBAAA,GADjBxG,IAAI,CAACC,GAAG;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CAAC,GACP,IACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAApB,QAAA,eACjBpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cAAC7B,KAAK,EAAC,oBAAoB;cAAAQ,QAAA,eACnCpI,OAAA,CAACrB,MAAM;gBACL0J,IAAI,EAAC,QAAQ;gBACbC,IAAI,eAAEtI,OAAA,CAACZ,OAAO;kBAAAmJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAClBC,OAAO,EAAE5C,qBAAsB;gBAC/BgD,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO,CAAE;gBACzBV,QAAQ,EAAE/G,YAAY,IAAI,CAAC1B,WAAW,IAAIG,WAAY;gBAAAsH,QAAA,EACvD;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAApB,QAAA,eAClBpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAC,OAAO;cACZ8B,KAAK,EAAC,iBAAiB;cACvBmC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjL,OAAO,EAAE;cAAwB,CAAC,CAAE;cAAAqJ,QAAA,eAE9DpI,OAAA,CAACvB,MAAM;gBACLkL,WAAW,EAAC,wBAAwB;gBACpCM,QAAQ,EAAEzG,iBAAkB;gBAAA4E,QAAA,EAE3BL,YAAY,CAAC1C,GAAG,CAAE6E,MAAM,iBACvBlK,OAAA,CAACC,MAAM;kBAAoB4H,KAAK,EAAEqC,MAAM,CAACrC,KAAM;kBAAAO,QAAA,EAC5C8B,MAAM,CAACtC;gBAAK,GADFsC,MAAM,CAACrC,KAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAApB,QAAA,eAClBpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAC,OAAO;cACZ8B,KAAK,EAAC,OAAO;cACbmC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjL,OAAO,EAAE;cAAwB,CAAC,CAAE;cAAAqJ,QAAA,eAE9DpI,OAAA,CAACvB,MAAM;gBACLkL,WAAW,EAAC,cAAc;gBAC1BP,QAAQ,EAAE,CAACvH,aAAc;gBACzBoI,QAAQ,EAAEnF,iBAAkB;gBAAAsD,QAAA,EAE3BvG,aAAa,MAAAvB,qBAAA,GAAI0H,YAAY,CAACnG,aAAa,CAAC,cAAAvB,qBAAA,uBAA3BA,qBAAA,CAA6B+E,GAAG,CAAE8E,GAAG,iBACrDnK,OAAA,CAACC,MAAM;kBAAW4H,KAAK,EAAEsC,GAAI;kBAAA/B,QAAA,GAAC,QACtB,EAAC+B,GAAG;gBAAA,GADCA,GAAG;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAApB,QAAA,eAClBpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAC,UAAU;cACf8B,KAAK,EAAC,UAAU;cAChBmC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjL,OAAO,EAAE;cAAqC,CAAC,CAAE;cAAAqJ,QAAA,eAE3EpI,OAAA,CAACvB,MAAM;gBACL2L,IAAI,EAAC,UAAU;gBACfT,WAAW,EAAC,iBAAiB;gBAC7BP,QAAQ,EAAE,CAACvH,aAAc;gBACzBoI,QAAQ,EAAEhF,oBAAqB;gBAAAmD,QAAA,EAE9B7G,iBAAiB,CAAC8D,GAAG,CAAET,OAAO,iBAC7B5E,OAAA,CAACC,MAAM;kBAAe4H,KAAK,EAAEjD,OAAQ;kBAAAwD,QAAA,EAClCxD;gBAAO,GADGA,OAAO;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEZ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAAAlB,QAAA,eACVpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAC,kBAAkB;cACvB8B,KAAK,EAAC,qCAAqC;cAC3C8B,KAAK,EAAC,kHAAkH;cAAAtB,QAAA,eAExHpI,OAAA,CAACvB,MAAM;gBACLkL,WAAW,EAAC,uCAAuC;gBACnDP,QAAQ,EAAE,CAACvH,aAAc;gBACzB+H,UAAU;gBACVK,QAAQ,EAAE7F,oBAAqB;gBAAAgE,QAAA,EAE9BzG,mBAAmB,CAAC0D,GAAG,CAAEgF,QAAQ,iBAChCrK,OAAA,CAACC,MAAM;kBAAoB4H,KAAK,EAAEwC,QAAQ,CAAC/G,GAAI;kBAAA8E,QAAA,eAC7CpI,OAAA;oBAAAoI,QAAA,gBACEpI,OAAA;sBAAK+I,KAAK,EAAE;wBAAEuB,UAAU,EAAE,MAAM;wBAAEC,KAAK,EAAE;sBAAU,CAAE;sBAAAnC,QAAA,GAAC,eACjD,EAACiC,QAAQ,CAAC5F,KAAK;oBAAA;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACN1I,OAAA;sBAAK+I,KAAK,EAAE;wBAAEyB,QAAQ,EAAE,MAAM;wBAAED,KAAK,EAAE,MAAM;wBAAEvB,SAAS,EAAE;sBAAM,CAAE;sBAAAZ,QAAA,GAAC,WACxD,EAACiC,QAAQ,CAACzF,OAAO,EAAC,mBAAY,EAACyF,QAAQ,CAAC3F,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,EAAC,mBAC3D,EAAC0F,QAAQ,CAACI,YAAY,IAAI,KAAK,EAAC,mBACjC,EAACJ,QAAQ,CAACK,gBAAgB;oBAAA;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAVK2B,QAAQ,CAAC/G,GAAG;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAAAlB,QAAA,eACVpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAC,eAAe;cACpB8B,KAAK,EAAC,gBAAgB;cACtBmC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjL,OAAO,EAAE;cAA2C,CAAC,CAAE;cAAAqJ,QAAA,eAEjFpI,OAAA,CAAClB,QAAQ,CAAC6L,KAAK;gBAACC,OAAO,EAAEjD;cAAoB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAAAlB,QAAA,eACVpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAC,kBAAkB;cACvB8B,KAAK,EAAC,mBAAmB;cACzBmC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjL,OAAO,EAAE;cAA8C,CAAC,CAAE;cAAAqJ,QAAA,eAEpFpI,OAAA,CAAClB,QAAQ,CAAC6L,KAAK;gBAACC,OAAO,EAAE9C;cAAkB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAApB,QAAA,eACjBpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAC,gBAAgB;cACrB8B,KAAK,EAAC,iBAAiB;cACvBmC,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjL,OAAO,EAAE;cAA+B,CAAC,EAC3D;gBAAEsJ,IAAI,EAAE,QAAQ;gBAAEwC,GAAG,EAAE,CAAC;gBAAEC,GAAG,EAAE,EAAE;gBAAE/L,OAAO,EAAE;cAA2B,CAAC,CACxE;cAAAqJ,QAAA,eAEFpI,OAAA,CAACtB,WAAW;gBACVmM,GAAG,EAAE,CAAE;gBACPC,GAAG,EAAE,EAAG;gBACRnB,WAAW,EAAC,uBAAuB;gBACnCZ,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1I,OAAA,CAAChB,OAAO;UAAAoJ,QAAA,EAAC;QAAqB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,EAEvC7G,aAAa,IAAIE,aAAa,IAAIE,gBAAgB,CAACiC,MAAM,GAAG,CAAC,iBAC5DlE,OAAA,CAACf,KAAK;UACJF,OAAO,EAAC,+BAA+B;UACvC6J,WAAW,eACT5I,OAAA;YAAAoI,QAAA,gBACEpI,OAAA;cAAAoI,QAAA,gBAAGpI,OAAA;gBAAAoI,QAAA,EAAQ;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7G,aAAa,CAACkJ,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnJ,aAAa,CAACoJ,KAAK,CAAC,CAAC,CAAC;YAAA;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/F1I,OAAA;cAAAoI,QAAA,gBAAGpI,OAAA;gBAAAoI,QAAA,EAAQ;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3G,aAAa;YAAA;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C1I,OAAA;cAAAoI,QAAA,gBAAGpI,OAAA;gBAAAoI,QAAA,EAAQ;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzG,gBAAgB,CAAC0C,IAAI,CAAC,IAAI,CAAC;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/D1I,OAAA;cAAAoI,QAAA,gBAAGpI,OAAA;gBAAAoI,QAAA,EAAQ;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjH,eAAe,CAACyC,MAAM,EAAC,2CAAyC;YAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3G1I,OAAA;cAAAoI,QAAA,gBAAGpI,OAAA;gBAAAoI,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,oEAAgE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnG,CACN;UACDL,IAAI,EAAC,MAAM;UACXQ,QAAQ;UACRE,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAG;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACF,eAED1I,OAAA,CAACpB,GAAG;UAACyK,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAjB,QAAA,gBACpBpI,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAApB,QAAA,eACjBpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAE,CAAC,sBAAsB,EAAE,iBAAiB,CAAE;cAClD8B,KAAK,EAAC,iBAAiB;cAAAQ,QAAA,eAEvBpI,OAAA,CAACtB,WAAW;gBACVmM,GAAG,EAAE,CAAE;gBACPlB,WAAW,EAAC,GAAG;gBACfZ,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAApB,QAAA,eACjBpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAE,CAAC,sBAAsB,EAAE,YAAY,CAAE;cAC7C8B,KAAK,EAAC,mBAAmB;cAAAQ,QAAA,eAEzBpI,OAAA,CAACtB,WAAW;gBACVmM,GAAG,EAAE,CAAE;gBACPlB,WAAW,EAAC,GAAG;gBACfZ,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN1I,OAAA,CAACnB,GAAG;YAACyK,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAApB,QAAA,eACjBpI,OAAA,CAACxB,IAAI,CAACiL,IAAI;cACR3D,IAAI,EAAE,CAAC,sBAAsB,EAAE,eAAe,CAAE;cAChD8B,KAAK,EAAC,eAAe;cAAAQ,QAAA,eAErBpI,OAAA,CAACtB,WAAW;gBACVmM,GAAG,EAAE,CAAE;gBACPlB,WAAW,EAAC,GAAG;gBACfZ,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1I,OAAA,CAACxB,IAAI,CAACiL,IAAI;UACR3D,IAAI,EAAC,gBAAgB;UACrB8B,KAAK,EAAG,6BAA4BnG,eAAe,CAACyC,MAAO,aAAa;UACxEwF,KAAK,EAAEjI,eAAe,CAACyC,MAAM,KAAK,CAAC,GAAG,2DAA2D,GAAG,0DAA2D;UAAAkE,QAAA,eAE/JpI,OAAA,CAACvB,MAAM;YACL2L,IAAI,EAAC,UAAU;YACfT,WAAW,EAAElI,eAAe,CAACyC,MAAM,KAAK,CAAC,GAAG,+DAA+D,GAAG,+CAAgD;YAC9J6E,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO,CAAE;YACzBV,QAAQ,EAAE3H,eAAe,CAACyC,MAAM,KAAK,CAAE;YACvCgH,gBAAgB,EAAC,UAAU;YAC3BC,UAAU;YACVC,YAAY,EAAEA,CAACC,KAAK,EAAEnB,MAAM,KAC1BA,MAAM,CAAC9B,QAAQ,CAACkD,WAAW,CAAC,CAAC,CAACC,OAAO,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IAAI,CAC/D;YAAAlD,QAAA,EAEA3G,eAAe,CAAC4D,GAAG,CAAC,CAACC,KAAK,EAAEkG,KAAK,kBAChCxL,OAAA,CAACC,MAAM;cAAsD4H,KAAK,EAAEvC,KAAK,CAACE,SAAU;cAAA4C,QAAA,eAClFpI,OAAA;gBAAAoI,QAAA,gBACEpI,OAAA;kBAAAoI,QAAA,EAAS9C,KAAK,CAACE;gBAAS;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAClC1I,OAAA;kBAAK+I,KAAK,EAAE;oBAAEyB,QAAQ,EAAE,MAAM;oBAAED,KAAK,EAAE;kBAAO,CAAE;kBAAAnC,QAAA,GAC7C9C,KAAK,CAACV,OAAO,EAAC,sBAAe,EAACU,KAAK,CAACmG,UAAU;gBAAA;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,EACLpD,KAAK,CAACoG,SAAS,IAAIpG,KAAK,CAACoG,SAAS,CAACxH,MAAM,GAAG,CAAC,iBAC5ClE,OAAA;kBAAK+I,KAAK,EAAE;oBAAEyB,QAAQ,EAAE,MAAM;oBAAED,KAAK,EAAE;kBAAO,CAAE;kBAAAnC,QAAA,GAAC,aACpC,EAAC9C,KAAK,CAACoG,SAAS,CAACT,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACtG,IAAI,CAAC,IAAI,CAAC,EACjDW,KAAK,CAACoG,SAAS,CAACxH,MAAM,GAAG,CAAC,IAAK,KAAIoB,KAAK,CAACoG,SAAS,CAACxH,MAAM,GAAG,CAAE,OAAM;gBAAA;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAZM,GAAEpD,KAAK,CAACV,OAAQ,IAAGU,KAAK,CAACE,SAAU,IAAGgG,KAAM,EAAC;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAanD,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ1I,OAAA;UAAK+E,SAAS,EAAC,cAAc;UAAAqD,QAAA,gBAC3BpI,OAAA,CAACrB,MAAM;YAACgK,OAAO,EAAExI,MAAO;YAACiJ,QAAQ,EAAE/G,YAAa;YAAA+F,QAAA,EAAC;UAEjD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1I,OAAA,CAACrB,MAAM;YACL0J,IAAI,EAAC,SAAS;YACdsD,QAAQ,EAAC,QAAQ;YACjB9K,OAAO,EAAEwB,YAAa;YACtB+G,QAAQ,EAAE,CAACzI,WAAW,IAAIG,WAAY;YACtCwH,IAAI,eAAEtI,OAAA,CAACZ,OAAO;cAAAmJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAN,QAAA,EAEjB/F,YAAY,GAAG,eAAe,GAAG,CAAC1B,WAAW,GAAG,gBAAgB,GAAG;UAAoB;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEP1I,OAAA,CAACJ,qBAAqB;MACpBgM,OAAO,EAAEnJ,qBAAsB;MAC/BoJ,QAAQ,EAAEA,CAAA,KAAMnJ,wBAAwB,CAAC,KAAK,CAAE;MAChDtC,SAAS,EAAEsF,6BAA8B;MACzCoG,aAAa,EAAE;QACbrI,KAAK,EAAE5B,aAAa;QACpB8B,KAAK,EAAE5B,aAAa;QACpB8B,QAAQ,EAAE5B;MACZ;IAAE;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEF1I,OAAA,CAACH,YAAY;MACX+L,OAAO,EAAEjJ,cAAe;MACxBkJ,QAAQ,EAAEA,CAAA,KAAMjJ,iBAAiB,CAAC,KAAK,CAAE;MACzCxC,SAAS,EAAG2L,QAAQ,IAAK;QACvB9K,kBAAkB,CAAC8K,QAAQ,CAAC;QAC5BnJ,iBAAiB,CAAC,KAAK,CAAC;MAC1B,CAAE;MACF6B,KAAK,EAAC,4BAA4B;MAClCmE,WAAW,EAAC;IAAyH;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACrI,EAAA,CAjwBQH,sBAAsB;EAAA,QACZ5B,WAAW,EACbE,IAAI,CAACiC,OAAO,EAcvBX,SAAS;AAAA;AAAAkM,EAAA,GAhBN9L,sBAAsB;AAmwB/B,eAAeA,sBAAsB;AAAC,IAAA8L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}