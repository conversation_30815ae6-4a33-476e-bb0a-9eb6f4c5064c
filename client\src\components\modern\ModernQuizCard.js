import React from 'react';
import { motion } from 'framer-motion';
import {
  Tb<PERSON>lock,
  TbQuestionMark,
  TbPlayerPlay,
  TbStar,
  TbCheck,
  TbX,
  TbBrain,
} from 'react-icons/tb';
import './modern-quiz.css';

const ModernQuizCard = ({
  quiz,
  onStart,
  userResult = null,
  className = '',
  ...props
}) => {
  // Get quiz status and styling based on user result
  const getQuizStatus = () => {
    if (!userResult) {
      return {
        status: 'not-attempted',
        statusBg: 'bg-blue-500',
        cardBg: 'bg-white',
        borderColor: 'border-blue-200',
        statusText: 'Not Attempted',
        hoverBg: 'hover:bg-blue-50',
        shadowColor: ''
      };
    }

    const passingMarks = quiz.passingMarks || 60;
    const passed = userResult.percentage >= passingMarks;

    if (passed) {
      return {
        status: 'passed',
        statusBg: 'bg-green-500',
        cardBg: 'bg-white',
        borderColor: 'border-green-200',
        statusText: 'Passed',
        hoverBg: 'hover:bg-green-50',
        shadowColor: 'shadow-emerald-200/50'
      };
    } else {
      return {
        status: 'failed',
        statusBg: 'bg-red-500',
        cardBg: 'bg-white',
        borderColor: 'border-red-200',
        statusText: 'Failed',
        hoverBg: 'hover:bg-red-50',
        shadowColor: 'shadow-rose-200/50'
      };
    }
  };

  const status = getQuizStatus();

  // Get header colors based on status
  const getHeaderColors = () => {
    if (!userResult) {
      return 'bg-gradient-to-r from-blue-600 to-blue-700';
    } else if (status.status === 'passed') {
      return 'bg-gradient-to-r from-emerald-600 to-emerald-700';
    } else {
      return 'bg-gradient-to-r from-rose-600 to-rose-700';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={`h-full ${className}`}
    >
      <div
        className={`
          h-full rounded-xl border-2 shadow-lg hover:shadow-xl
          transition-all duration-300 cursor-pointer
          ${status.cardBg} ${status.borderColor} ${status.hoverBg} ${status.shadowColor}
          overflow-hidden flex flex-col
        `}
        onClick={() => onStart && onStart(quiz)}
        {...props}
      >
        {/* Status Badge */}
        <div className="absolute top-3 right-3 z-10">
          <div className={`px-3 py-1 rounded-full text-xs font-bold text-white ${status.statusBg}`}>
            {userResult ? (
              <>
                {status.status === 'passed' ? (
                  <TbCheck className="w-3 h-3 inline mr-1" />
                ) : (
                  <TbX className="w-3 h-3 inline mr-1" />
                )}
                {status.statusText}
              </>
            ) : (
              'Not Attempted'
            )}
          </div>
        </div>

        {/* Header */}
        <div className={`${getHeaderColors()} p-4 text-white relative`}>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
              <TbBrain className="w-5 h-5" />
            </div>
            <div className="flex-1">
              <h3 className="font-bold text-lg leading-tight line-clamp-2">
                {quiz.name || 'Quiz Title'}
              </h3>
              <p className="text-blue-100 text-sm mt-1">
                {quiz.subject || 'Subject'}
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 flex-1 flex flex-col">
          {/* Quiz Stats */}
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="flex items-center gap-2 text-gray-600">
              <TbClock className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium">
                {Math.round((quiz.duration || 1800) / 60)} min
              </span>
            </div>
            <div className="flex items-center gap-2 text-gray-600">
              <TbQuestionMark className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium">
                {quiz.questions?.length || 0} questions
              </span>
            </div>
            <div className="flex items-center gap-2 text-gray-600">
              <TbStar className="w-4 h-4 text-yellow-500" />
              <span className="text-sm font-medium">
                {quiz.xpPoints || 100} XP
              </span>
            </div>
            <div className="flex items-center gap-2 text-gray-600">
              <span className="text-sm font-medium text-gray-500">
                Pass: {quiz.passingMarks || 60}%
              </span>
            </div>
          </div>

          {/* User Result */}
          {userResult && (
            <div className={`
              border rounded-lg p-3 mb-4
              ${status.status === 'passed' 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
              }
            `}>
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium text-gray-700">Last Score:</span>
                <span className={`font-bold ${
                  status.status === 'passed' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {userResult.percentage}%
                </span>
              </div>
              <div className="flex items-center justify-between text-xs text-gray-600 mt-1">
                <span>{Array.isArray(userResult.correctAnswers) ? userResult.correctAnswers.length : (userResult.correctAnswers || 0)} correct</span>
                <span>+{userResult.xpEarned || userResult.points || userResult.xpGained || 0} XP</span>
              </div>
            </div>
          )}

          {/* Start Button */}
          <div className="mt-auto">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onStart && onStart(quiz);
              }}
              className="
                w-full bg-blue-600 hover:bg-blue-700 text-white 
                py-3 px-4 rounded-lg font-semibold text-sm
                transition-colors duration-200 
                flex items-center justify-center gap-2
                shadow-md hover:shadow-lg
              "
            >
              <TbPlayerPlay className="w-4 h-4" />
              {userResult ? 'Retake Quiz' : 'Start Quiz'}
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ModernQuizCard;
