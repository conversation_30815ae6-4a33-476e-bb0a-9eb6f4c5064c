const mongoose = require("mongoose");

// Schema for storing syllabus PDFs and their extracted content
const syllabusSchema = new mongoose.Schema(
  {
    // Basic Information
    title: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    
    // Educational Classification
    level: {
      type: String,
      enum: ["primary", "secondary", "advance"],
      required: true,
    },
    classes: [{
      type: String,
      required: true,
    }], // Changed from single class to array of classes
    subject: {
      type: String,
      required: true,
    },
    
    // File Information
    fileName: {
      type: String,
      required: true,
    },
    originalFileName: {
      type: String,
      required: true,
    },
    filePath: {
      type: String,
      required: true,
    },
    fileSize: {
      type: Number,
      required: true,
    },
    mimeType: {
      type: String,
      required: true,
      default: "application/pdf",
    },
    
    // S3 Storage Information (if using AWS S3)
    s3Key: {
      type: String,
    },
    s3Bucket: {
      type: String,
    },
    s3Url: {
      type: String,
    },
    
    // Extracted Content
    extractedText: {
      type: String,
      required: false, // Will be filled during processing
      default: "",
    },
    extractedTopics: [{
      topicName: {
        type: String,
        required: true,
      },
      subtopics: [{
        name: String,
        description: String,
        pageNumber: Number,
      }],
      pageNumbers: [Number],
      keyTerms: [String],
      difficulty: {
        type: String,
        enum: ["easy", "medium", "hard"],
        default: "medium",
      },
    }],
    
    // Learning Objectives extracted from PDF
    learningObjectives: [{
      objective: String,
      topic: String,
      pageNumber: Number,
    }],
    
    // Competencies and Skills
    competencies: [{
      name: String,
      description: String,
      assessmentCriteria: [String],
      relatedTopics: [String],
    }],
    
    // Processing Information
    processingStatus: {
      type: String,
      enum: ["pending", "processing", "completed", "failed"],
      default: "pending",
    },
    processingError: {
      type: String,
    },
    extractionMethod: {
      type: String,
      enum: ["pdf-parse", "tesseract", "manual"],
      default: "pdf-parse",
    },
    
    // AI Enhancement
    aiEnhanced: {
      type: Boolean,
      default: false,
    },
    aiEnhancementDate: {
      type: Date,
    },
    aiExtractedTopics: [{
      topicName: String,
      subtopics: [String],
      keyTerms: [String],
      difficulty: String,
      confidence: Number, // 0-1 confidence score
    }],
    
    // Version Control
    version: {
      type: String,
      default: "1.0",
    },
    previousVersions: [{
      version: String,
      filePath: String,
      uploadDate: Date,
      changes: String,
    }],
    
    // Usage Statistics
    usageStats: {
      questionsGenerated: {
        type: Number,
        default: 0,
      },
      lastUsed: {
        type: Date,
      },
      popularTopics: [{
        topic: String,
        usageCount: Number,
      }],
    },
    
    // Metadata
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: false, // Will be set during upload
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
    },
    approvalDate: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isPublic: {
      type: Boolean,
      default: false,
    },
    
    // Quality Control
    qualityScore: {
      type: Number,
      min: 0,
      max: 100,
    },
    reviewNotes: {
      type: String,
    },
    
    // Tags for better organization
    tags: [String],
    
    // Academic Year
    academicYear: {
      type: String,
    },
    
    // Language
    language: {
      type: String,
      default: "English",
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
syllabusSchema.index({ level: 1, classes: 1, subject: 1 });
syllabusSchema.index({ isActive: 1, processingStatus: 1 });
syllabusSchema.index({ uploadedBy: 1 });
syllabusSchema.index({ tags: 1 });
syllabusSchema.index({ "extractedTopics.topicName": "text", "extractedText": "text" });

// Virtual for file URL
syllabusSchema.virtual('fileUrl').get(function() {
  if (this.s3Url) {
    return this.s3Url;
  }
  return `/uploads/syllabus/${this.fileName}`;
});

// Method to get topics for AI generation
syllabusSchema.methods.getTopicsForAI = function() {
  const topics = {};
  
  this.extractedTopics.forEach(topic => {
    topics[topic.topicName] = {
      subtopics: topic.subtopics.map(sub => sub.name),
      keyTerms: topic.keyTerms,
      difficulty: topic.difficulty,
    };
  });
  
  // Include AI enhanced topics if available
  if (this.aiEnhanced && this.aiExtractedTopics.length > 0) {
    this.aiExtractedTopics.forEach(topic => {
      if (topic.confidence > 0.7) { // Only include high-confidence AI topics
        topics[topic.topicName] = {
          subtopics: topic.subtopics,
          keyTerms: topic.keyTerms,
          difficulty: topic.difficulty,
          aiGenerated: true,
        };
      }
    });
  }
  
  return topics;
};

// Method to update usage statistics
syllabusSchema.methods.updateUsageStats = function(topicUsed = null) {
  this.usageStats.questionsGenerated += 1;
  this.usageStats.lastUsed = new Date();
  
  if (topicUsed) {
    const existingTopic = this.usageStats.popularTopics.find(t => t.topic === topicUsed);
    if (existingTopic) {
      existingTopic.usageCount += 1;
    } else {
      this.usageStats.popularTopics.push({
        topic: topicUsed,
        usageCount: 1,
      });
    }
  }
  
  return this.save();
};

// Static method to find syllabus for AI generation
syllabusSchema.statics.findForAIGeneration = function(level, className, subject) {
  return this.findOne({
    level: level,
    classes: { $in: [className] }, // Check if className is in the classes array
    subject: subject,
    isActive: true,
    processingStatus: "completed",
  }).sort({ createdAt: -1 }); // Get the most recent version
};

// Static method to get all available subjects for a level
syllabusSchema.statics.getAvailableSubjects = function(level, className = null) {
  const query = {
    level: level,
    isActive: true,
    processingStatus: "completed",
  };

  if (className) {
    query.classes = { $in: [className] }; // Check if className is in the classes array
  }

  return this.distinct("subject", query);
};

const Syllabus = mongoose.model("syllabus", syllabusSchema);

module.exports = Syllabus;
