import React, { useState, useEffect } from 'react';
import { getUserOnlineStatus } from '../../apicalls/notifications';

const ProfilePicture = ({
  user,
  size = 'md',
  showOnlineStatus = true,
  className = '',
  onClick = null,
  style = {},
  refreshInterval = 30000, // 30 seconds
  ...props
}) => {
  const [isOnline, setIsOnline] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check actual online status from server
  useEffect(() => {
    if (!showOnlineStatus || !user?._id) {
      setLoading(false);
      setIsOnline(false);
      return;
    }

    const checkOnlineStatus = async () => {
      try {
        const response = await getUserOnlineStatus(user._id);
        if (response.success) {
          setIsOnline(response.data.isOnline);
        } else {
          setIsOnline(false);
        }
      } catch (error) {
        console.error('Error checking online status:', error);
        setIsOnline(false);
      } finally {
        setLoading(false);
      }
    };

    // Initial check
    checkOnlineStatus();

    // Set up interval for periodic checks
    const interval = setInterval(checkOnlineStatus, refreshInterval);

    return () => clearInterval(interval);
  }, [user?._id, showOnlineStatus, refreshInterval]);

  const getSizeClasses = () => {
    switch (size) {
      case 'xs':
        return { container: 'w-6 h-6', text: 'text-xs' };
      case 'sm':
        return { container: 'w-8 h-8', text: 'text-xs' };
      case 'md':
        return { container: 'w-10 h-10', text: 'text-sm' };
      case 'lg':
        return { container: 'w-12 h-12', text: 'text-base' };
      case 'xl':
        return { container: 'w-16 h-16', text: 'text-lg' };
      case '2xl':
        return { container: 'w-20 h-20', text: 'text-xl' };
      case '3xl':
        return { container: 'w-24 h-24', text: 'text-2xl' };
      default:
        return { container: 'w-10 h-10', text: 'text-sm' };
    }
  };

  const sizeClasses = getSizeClasses();
  const isClickable = onClick !== null;

  return (
    <div 
      className={`relative inline-block ${className}`} 
      style={{ padding: showOnlineStatus ? '2px' : '0' }}
      {...props}
    >
      <div
        className={`
          ${sizeClasses.container}
          rounded-full overflow-hidden border-2 border-white/20 relative
          ${isClickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}
        `}
        style={{
          background: '#f0f0f0',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          ...style
        }}
        onClick={onClick}
      >
        {user?.profileImage || user?.profilePicture ? (
          <img
            src={user.profileImage || user.profilePicture}
            alt={user.name || 'User'}
            className="object-cover rounded-full w-full h-full"
            style={{ objectFit: 'cover' }}
            onError={(e) => {
              // Fallback to initials if image fails to load
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
        ) : null}
        
        {/* Fallback initials */}
        <div
          className={`
            rounded-full flex items-center justify-center font-semibold w-full h-full
            ${sizeClasses.text}
            ${user?.profileImage || user?.profilePicture ? 'hidden' : 'flex'}
          `}
          style={{
            background: user?.profileImage || user?.profilePicture ? 'transparent' : '#25D366',
            color: '#FFFFFF'
          }}
        >
          {(user?.name || user?.username || 'U').charAt(0).toUpperCase()}
        </div>
      </div>

      {/* Online Status Indicator - Only show if actually online */}
      {showOnlineStatus && user?._id && !loading && isOnline && (
        <div
          style={{
            position: 'absolute',
            width: size === 'xs' ? '8px' :
                   size === 'sm' ? '10px' :
                   size === 'md' ? '12px' :
                   size === 'lg' ? '14px' :
                   size === 'xl' ? '16px' :
                   size === '2xl' ? '18px' :
                   size === '3xl' ? '20px' : '12px',
            height: size === 'xs' ? '8px' :
                    size === 'sm' ? '10px' :
                    size === 'md' ? '12px' :
                    size === 'lg' ? '14px' :
                    size === 'xl' ? '16px' :
                    size === '2xl' ? '18px' :
                    size === '3xl' ? '20px' : '12px',
            bottom: '-3px',
            right: '-3px',
            zIndex: 999,
            borderRadius: '50%',
            backgroundColor: '#22c55e',
            background: '#22c55e',
            border: '2px solid #1f2937',
            outline: 'none',
            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.8)',
            animation: 'pulse 2s infinite'
          }}
          title="Online"
        />
      )}
    </div>
  );
};

export default ProfilePicture;
