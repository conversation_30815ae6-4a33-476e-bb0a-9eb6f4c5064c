const fs = require("fs");
const path = require("path");
const pdf = require("pdf-parse");
const axios = require("axios");
const Syllabus = require("../models/syllabusModel");

class SyllabusService {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
  }

  // Main method to process uploaded syllabus
  async processUploadedSyllabus(syllabusId) {
    try {
      console.log(`📚 Starting processing for syllabus ${syllabusId}`);
      
      const syllabus = await Syllabus.findById(syllabusId);
      if (!syllabus) {
        throw new Error("Syllabus not found");
      }

      // Update status to processing
      syllabus.processingStatus = "processing";
      await syllabus.save();

      // Step 1: Extract text from PDF
      console.log("📄 Extracting text from PDF...");
      const extractedText = await this.extractTextFromPDF(syllabus.filePath);
      
      // Step 2: Parse topics and structure using AI
      console.log("🤖 Analyzing content with AI...");
      const aiAnalysis = await this.analyzeContentWithAI(extractedText, syllabus);
      
      // Step 3: Update syllabus with extracted data
      syllabus.extractedText = extractedText;
      syllabus.extractedTopics = aiAnalysis.topics || [];
      syllabus.learningObjectives = aiAnalysis.learningObjectives || [];
      syllabus.competencies = aiAnalysis.competencies || [];
      syllabus.aiEnhanced = true;
      syllabus.aiEnhancementDate = new Date();
      syllabus.aiExtractedTopics = aiAnalysis.aiTopics || [];
      syllabus.processingStatus = "completed";
      syllabus.qualityScore = this.calculateQualityScore(syllabus);

      await syllabus.save();

      console.log(`✅ Successfully processed syllabus ${syllabusId}`);
      return { success: true, syllabusId };

    } catch (error) {
      console.error(`❌ Error processing syllabus ${syllabusId}:`, error);
      
      // Update status to failed
      const syllabus = await Syllabus.findById(syllabusId);
      if (syllabus) {
        syllabus.processingStatus = "failed";
        syllabus.processingError = error.message;
        await syllabus.save();
      }
      
      throw error;
    }
  }

  // Extract text from PDF file
  async extractTextFromPDF(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error("PDF file not found");
      }

      const dataBuffer = fs.readFileSync(filePath);
      const data = await pdf(dataBuffer);
      
      if (!data.text || data.text.trim().length === 0) {
        throw new Error("No text could be extracted from PDF");
      }

      // Clean up the extracted text
      const cleanedText = this.cleanExtractedText(data.text);
      
      console.log(`📄 Extracted ${cleanedText.length} characters from PDF`);
      return cleanedText;

    } catch (error) {
      console.error("Error extracting text from PDF:", error);
      throw new Error(`PDF text extraction failed: ${error.message}`);
    }
  }

  // Clean and normalize extracted text
  cleanExtractedText(text) {
    return text
      // Remove excessive whitespace
      .replace(/\s+/g, " ")
      // Remove page numbers and headers/footers patterns
      .replace(/Page \d+/gi, "")
      .replace(/\d+\s*$/gm, "")
      // Fix common OCR issues
      .replace(/([a-z])([A-Z])/g, "$1 $2")
      // Remove extra line breaks
      .replace(/\n\s*\n/g, "\n")
      // Trim
      .trim();
  }

  // Analyze content with AI to extract structured information
  async analyzeContentWithAI(extractedText, syllabus) {
    try {
      if (!this.openaiApiKey) {
        console.warn("OpenAI API key not found, skipping AI analysis");
        return this.fallbackAnalysis(extractedText);
      }

      const prompt = this.buildAnalysisPrompt(extractedText, syllabus);
      
      const response = await axios.post(
        "https://api.openai.com/v1/chat/completions",
        {
          model: "gpt-4o",
          messages: [
            {
              role: "system",
              content: "You are an expert educational content analyzer specializing in Tanzania curriculum. Analyze syllabus content and extract structured information."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: 4000,
          temperature: 0.3,
        },
        {
          headers: {
            "Authorization": `Bearer ${this.openaiApiKey}`,
            "Content-Type": "application/json",
          },
          timeout: 60000,
        }
      );

      const aiResponse = response.data.choices[0].message.content;
      return this.parseAIResponse(aiResponse);

    } catch (error) {
      console.error("Error in AI analysis:", error);
      return this.fallbackAnalysis(extractedText);
    }
  }

  // Build prompt for AI analysis
  buildAnalysisPrompt(extractedText, syllabus) {
    return `Analyze this Tanzania ${syllabus.level} education syllabus for ${syllabus.subject} Class ${syllabus.class}.

SYLLABUS CONTENT:
${extractedText.substring(0, 8000)} ${extractedText.length > 8000 ? '...' : ''}

Extract and structure the following information in JSON format:

{
  "topics": [
    {
      "topicName": "Topic Name",
      "subtopics": [
        {
          "name": "Subtopic Name",
          "description": "Brief description",
          "pageNumber": 1
        }
      ],
      "keyTerms": ["term1", "term2"],
      "difficulty": "easy|medium|hard"
    }
  ],
  "learningObjectives": [
    {
      "objective": "Learning objective text",
      "topic": "Related topic",
      "pageNumber": 1
    }
  ],
  "competencies": [
    {
      "name": "Competency name",
      "description": "Description",
      "assessmentCriteria": ["criteria1", "criteria2"],
      "relatedTopics": ["topic1", "topic2"]
    }
  ],
  "aiTopics": [
    {
      "topicName": "AI-identified topic",
      "subtopics": ["subtopic1", "subtopic2"],
      "keyTerms": ["term1", "term2"],
      "difficulty": "medium",
      "confidence": 0.85
    }
  ]
}

Focus on:
1. Main curriculum topics and subtopics
2. Learning objectives and outcomes
3. Key competencies and skills
4. Assessment criteria
5. Important terminology
6. Difficulty progression

Return ONLY valid JSON.`;
  }

  // Parse AI response
  parseAIResponse(aiResponse) {
    try {
      // Try to extract JSON from the response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // If no JSON found, try parsing the entire response
      return JSON.parse(aiResponse);
      
    } catch (error) {
      console.error("Error parsing AI response:", error);
      return this.fallbackAnalysis("");
    }
  }

  // Fallback analysis when AI is not available
  fallbackAnalysis(extractedText) {
    const topics = this.extractTopicsFromText(extractedText);
    const objectives = this.extractObjectivesFromText(extractedText);
    
    return {
      topics: topics,
      learningObjectives: objectives,
      competencies: [],
      aiTopics: [],
    };
  }

  // Extract topics using text patterns
  extractTopicsFromText(text) {
    const topics = [];
    const lines = text.split('\n');
    
    // Look for common topic patterns
    const topicPatterns = [
      /^(\d+\.?\d*)\s+([A-Z][^.!?]*)/,  // Numbered topics
      /^([A-Z][A-Z\s]+)$/,              // ALL CAPS topics
      /^Topic\s*\d*:?\s*(.+)/i,         // "Topic:" patterns
      /^Unit\s*\d*:?\s*(.+)/i,          // "Unit:" patterns
    ];

    lines.forEach((line, index) => {
      line = line.trim();
      if (line.length < 5 || line.length > 100) return;

      for (const pattern of topicPatterns) {
        const match = line.match(pattern);
        if (match) {
          const topicName = match[1] || match[2] || match[0];
          if (topicName && topicName.length > 3) {
            topics.push({
              topicName: topicName.trim(),
              subtopics: [],
              keyTerms: this.extractKeyTermsFromContext(text, index),
              difficulty: "medium",
            });
          }
          break;
        }
      }
    });

    return topics.slice(0, 20); // Limit to 20 topics
  }

  // Extract learning objectives from text
  extractObjectivesFromText(text) {
    const objectives = [];
    const objectivePatterns = [
      /(?:students?\s+(?:will|should|can|are able to))\s+([^.!?]+)/gi,
      /(?:by the end of|after completing)\s+[^,]*,\s*students?\s+([^.!?]+)/gi,
      /(?:learning objectives?|aims?|goals?):\s*([^.!?]+)/gi,
    ];

    objectivePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        if (match[1] && match[1].length > 10) {
          objectives.push({
            objective: match[1].trim(),
            topic: "General",
            pageNumber: 1,
          });
        }
      }
    });

    return objectives.slice(0, 15); // Limit to 15 objectives
  }

  // Extract key terms from surrounding context
  extractKeyTermsFromContext(text, lineIndex) {
    const lines = text.split('\n');
    const contextLines = lines.slice(Math.max(0, lineIndex - 2), lineIndex + 3);
    const contextText = contextLines.join(' ');
    
    // Simple key term extraction
    const terms = contextText
      .match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || [];
    
    return terms
      .filter(term => term.length > 3 && term.length < 30)
      .slice(0, 5);
  }

  // Calculate quality score based on extracted content
  calculateQualityScore(syllabus) {
    let score = 0;
    
    // Text length score (0-30 points)
    const textLength = syllabus.extractedText.length;
    if (textLength > 5000) score += 30;
    else if (textLength > 2000) score += 20;
    else if (textLength > 500) score += 10;
    
    // Topics score (0-25 points)
    const topicCount = syllabus.extractedTopics.length;
    if (topicCount > 10) score += 25;
    else if (topicCount > 5) score += 15;
    else if (topicCount > 0) score += 5;
    
    // Learning objectives score (0-20 points)
    const objectiveCount = syllabus.learningObjectives.length;
    if (objectiveCount > 10) score += 20;
    else if (objectiveCount > 5) score += 15;
    else if (objectiveCount > 0) score += 5;
    
    // AI enhancement score (0-15 points)
    if (syllabus.aiEnhanced) score += 15;
    
    // Competencies score (0-10 points)
    if (syllabus.competencies.length > 0) score += 10;
    
    return Math.min(score, 100);
  }

  // Get syllabus content for AI question generation
  async getSyllabusForAI(level, className, subject) {
    try {
      const syllabus = await Syllabus.findForAIGeneration(level, className, subject);
      
      if (!syllabus) {
        console.log(`No syllabus found for ${level} ${subject} Class ${className}`);
        return null;
      }

      return {
        extractedText: syllabus.extractedText,
        topics: syllabus.getTopicsForAI(),
        learningObjectives: syllabus.learningObjectives,
        competencies: syllabus.competencies,
        syllabusId: syllabus._id,
      };
      
    } catch (error) {
      console.error("Error getting syllabus for AI:", error);
      return null;
    }
  }
}

module.exports = new SyllabusService();
