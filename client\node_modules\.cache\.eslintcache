[{"C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js": "5", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js": "6", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js": "7", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js": "8", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js": "9", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js": "10", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js": "11", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js": "12", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\Plans.jsx": "13", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js": "14", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js": "15", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js": "16", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js": "17", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js": "18", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js": "19", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js": "20", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js": "21", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js": "22", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js": "23", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js": "24", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js": "25", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js": "26", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Chat\\index.jsx": "28", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js": "29", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js": "30", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js": "31", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js": "32", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js": "33", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js": "34", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js": "35", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js": "36", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx": "37", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js": "38", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js": "39", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js": "40", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js": "41", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js": "42", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js": "43", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\WaitingModal.jsx": "44", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\ConfirmModal.jsx": "45", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js": "46", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js": "47", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js": "48", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js": "49", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js": "50", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js": "51", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js": "52", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\index.js": "53", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js": "54", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionGenerationForm.js": "55", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionPreview.js": "56", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\AutoGenerateExamModal.js": "57", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\authUtils.js": "58", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AILoginModal.js": "59", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\hooks\\useAIAuth.js": "60", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\auth.js": "61", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js": "62", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizStart.js": "63", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js": "64", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizRenderer.js": "65", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js": "66", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js": "67", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js": "68", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js": "69", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js": "70", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js": "71", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js": "72", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js": "73", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js": "74", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js": "75", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js": "76", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js": "77", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js": "78", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js": "79", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js": "80", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizErrorBoundary.js": "81", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js": "82", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js": "83", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js": "84", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js": "85", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js": "86", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js": "87", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js": "88", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js": "89", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js": "90", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js": "91", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js": "92", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js": "93", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js": "94", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ErrorBoundary.js": "95", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\ModernQuizPage.js": "96", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizDashboard.js": "97", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizInterface.js": "98", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ModernQuizCard.js": "99", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\quiz.js": "100", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js": "101", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js": "102", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js": "103", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js": "104", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js": "105", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx": "106", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\SyllabusManagement\\SyllabusManagement.jsx": "107", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js": "108"}, {"size": 395, "mtime": 1696247250000, "results": "109", "hashOfConfig": "110"}, {"size": 10149, "mtime": 1751491744668, "results": "111", "hashOfConfig": "110"}, {"size": 362, "mtime": 1696247250000, "results": "112", "hashOfConfig": "110"}, {"size": 430, "mtime": 1736735017645, "results": "113", "hashOfConfig": "110"}, {"size": 180, "mtime": 1696247250000, "results": "114", "hashOfConfig": "110"}, {"size": 19115, "mtime": 1751482317520, "results": "115", "hashOfConfig": "110"}, {"size": 334, "mtime": 1696247250000, "results": "116", "hashOfConfig": "110"}, {"size": 416, "mtime": 1696247250000, "results": "117", "hashOfConfig": "110"}, {"size": 404, "mtime": 1736731932223, "results": "118", "hashOfConfig": "110"}, {"size": 449, "mtime": 1736732007232, "results": "119", "hashOfConfig": "110"}, {"size": 13176, "mtime": 1751228551397, "results": "120", "hashOfConfig": "110"}, {"size": 2949, "mtime": 1751478398897, "results": "121", "hashOfConfig": "110"}, {"size": 7653, "mtime": 1750329453221, "results": "122", "hashOfConfig": "110"}, {"size": 48075, "mtime": 1751478351350, "results": "123", "hashOfConfig": "110"}, {"size": 44652, "mtime": 1751260519262, "results": "124", "hashOfConfig": "110"}, {"size": 2243, "mtime": 1735567977494, "results": "125", "hashOfConfig": "110"}, {"size": 12017, "mtime": 1751480609809, "results": "126", "hashOfConfig": "110"}, {"size": 128872, "mtime": 1751485183468, "results": "127", "hashOfConfig": "110"}, {"size": 1327, "mtime": 1709427669270, "results": "128", "hashOfConfig": "110"}, {"size": 8089, "mtime": 1740446459586, "results": "129", "hashOfConfig": "110"}, {"size": 4498, "mtime": 1751474540149, "results": "130", "hashOfConfig": "110"}, {"size": 19148, "mtime": 1751409742999, "results": "131", "hashOfConfig": "110"}, {"size": 7528, "mtime": 1751289392869, "results": "132", "hashOfConfig": "110"}, {"size": 3037, "mtime": 1751407422967, "results": "133", "hashOfConfig": "110"}, {"size": 22538, "mtime": 1751485757742, "results": "134", "hashOfConfig": "110"}, {"size": 18152, "mtime": 1751482578847, "results": "135", "hashOfConfig": "110"}, {"size": 16989, "mtime": 1751415543478, "results": "136", "hashOfConfig": "110"}, {"size": 4579, "mtime": 1749938582942, "results": "137", "hashOfConfig": "110"}, {"size": 522, "mtime": 1736735708590, "results": "138", "hashOfConfig": "110"}, {"size": 2578, "mtime": 1740446459580, "results": "139", "hashOfConfig": "110"}, {"size": 2455, "mtime": 1751479784424, "results": "140", "hashOfConfig": "110"}, {"size": 388, "mtime": 1703845955779, "results": "141", "hashOfConfig": "110"}, {"size": 279, "mtime": 1736719733927, "results": "142", "hashOfConfig": "110"}, {"size": 1104, "mtime": 1749936905424, "results": "143", "hashOfConfig": "110"}, {"size": 3391, "mtime": 1751304153158, "results": "144", "hashOfConfig": "110"}, {"size": 5595, "mtime": 1751164672302, "results": "145", "hashOfConfig": "110"}, {"size": 944, "mtime": 1750970590507, "results": "146", "hashOfConfig": "110"}, {"size": 6669, "mtime": 1750999504134, "results": "147", "hashOfConfig": "110"}, {"size": 12864, "mtime": 1751134045332, "results": "148", "hashOfConfig": "110"}, {"size": 3835, "mtime": 1751478376207, "results": "149", "hashOfConfig": "110"}, {"size": 8101, "mtime": 1750963515173, "results": "150", "hashOfConfig": "110"}, {"size": 578, "mtime": 1705434185826, "results": "151", "hashOfConfig": "110"}, {"size": 1787, "mtime": 1734985908268, "results": "152", "hashOfConfig": "110"}, {"size": 2748, "mtime": 1736737718411, "results": "153", "hashOfConfig": "110"}, {"size": 2421, "mtime": 1737107445778, "results": "154", "hashOfConfig": "110"}, {"size": 3692, "mtime": 1751088963669, "results": "155", "hashOfConfig": "110"}, {"size": 8145, "mtime": 1751000372079, "results": "156", "hashOfConfig": "110"}, {"size": 29072, "mtime": 1750992761364, "results": "157", "hashOfConfig": "110"}, {"size": 9494, "mtime": 1750995979612, "results": "158", "hashOfConfig": "110"}, {"size": 1524, "mtime": 1750994293078, "results": "159", "hashOfConfig": "110"}, {"size": 17375, "mtime": 1751000106093, "results": "160", "hashOfConfig": "110"}, {"size": 11161, "mtime": 1750999560542, "results": "161", "hashOfConfig": "110"}, {"size": 8252, "mtime": 1751004143541, "results": "162", "hashOfConfig": "110"}, {"size": 3047, "mtime": 1751086581664, "results": "163", "hashOfConfig": "110"}, {"size": 25462, "mtime": 1751089065189, "results": "164", "hashOfConfig": "110"}, {"size": 10774, "mtime": 1751085763434, "results": "165", "hashOfConfig": "110"}, {"size": 11689, "mtime": 1751100954560, "results": "166", "hashOfConfig": "110"}, {"size": 5089, "mtime": 1751261831682, "results": "167", "hashOfConfig": "110"}, {"size": 5991, "mtime": 1751088070022, "results": "168", "hashOfConfig": "110"}, {"size": 5741, "mtime": 1751088101803, "results": "169", "hashOfConfig": "110"}, {"size": 3690, "mtime": 1751088038266, "results": "170", "hashOfConfig": "110"}, {"size": 39932, "mtime": 1751479156528, "results": "171", "hashOfConfig": "110"}, {"size": 9731, "mtime": 1751478261471, "results": "172", "hashOfConfig": "110"}, {"size": 8920, "mtime": 1751372481600, "results": "173", "hashOfConfig": "110"}, {"size": 12631, "mtime": 1751471556045, "results": "174", "hashOfConfig": "110"}, {"size": 15402, "mtime": 1751422122218, "results": "175", "hashOfConfig": "110"}, {"size": 1410, "mtime": 1751140352157, "results": "176", "hashOfConfig": "110"}, {"size": 1140, "mtime": 1751426583568, "results": "177", "hashOfConfig": "110"}, {"size": 2324, "mtime": 1751140401815, "results": "178", "hashOfConfig": "110"}, {"size": 2913, "mtime": 1751140370241, "results": "179", "hashOfConfig": "110"}, {"size": 1857, "mtime": 1751140385464, "results": "180", "hashOfConfig": "110"}, {"size": 3119, "mtime": 1751164996340, "results": "181", "hashOfConfig": "110"}, {"size": 16104, "mtime": 1751480428624, "results": "182", "hashOfConfig": "110"}, {"size": 13299, "mtime": 1751249005755, "results": "183", "hashOfConfig": "110"}, {"size": 7171, "mtime": 1751229742199, "results": "184", "hashOfConfig": "110"}, {"size": 2576, "mtime": 1751143230244, "results": "185", "hashOfConfig": "110"}, {"size": 3904, "mtime": 1751143777976, "results": "186", "hashOfConfig": "110"}, {"size": 5088, "mtime": 1751143254906, "results": "187", "hashOfConfig": "110"}, {"size": 4989, "mtime": 1751143312418, "results": "188", "hashOfConfig": "110"}, {"size": 6304, "mtime": 1751188593099, "results": "189", "hashOfConfig": "110"}, {"size": 3061, "mtime": 1751208387152, "results": "190", "hashOfConfig": "110"}, {"size": 12711, "mtime": 1751260271529, "results": "191", "hashOfConfig": "110"}, {"size": 18256, "mtime": 1751482855935, "results": "192", "hashOfConfig": "110"}, {"size": 29607, "mtime": 1751260312633, "results": "193", "hashOfConfig": "110"}, {"size": 11901, "mtime": 1751236424130, "results": "194", "hashOfConfig": "110"}, {"size": 8429, "mtime": 1751244672688, "results": "195", "hashOfConfig": "110"}, {"size": 7685, "mtime": 1751244700154, "results": "196", "hashOfConfig": "110"}, {"size": 10081, "mtime": 1751244608756, "results": "197", "hashOfConfig": "110"}, {"size": 16372, "mtime": 1751479340474, "results": "198", "hashOfConfig": "110"}, {"size": 3109, "mtime": 1751260973778, "results": "199", "hashOfConfig": "110"}, {"size": 6762, "mtime": 1751491693502, "results": "200", "hashOfConfig": "110"}, {"size": 9778, "mtime": 1751407462268, "results": "201", "hashOfConfig": "110"}, {"size": 846, "mtime": 1751407484742, "results": "202", "hashOfConfig": "110"}, {"size": 8103, "mtime": 1751424497355, "results": "203", "hashOfConfig": "110"}, {"size": 3103, "mtime": 1751464185780, "results": "204", "hashOfConfig": "110"}, {"size": 16479, "mtime": 1751468023515, "results": "205", "hashOfConfig": "110"}, {"size": 9366, "mtime": 1751467242668, "results": "206", "hashOfConfig": "110"}, {"size": 20588, "mtime": 1751468051806, "results": "207", "hashOfConfig": "110"}, {"size": 6704, "mtime": 1751478229862, "results": "208", "hashOfConfig": "110"}, {"size": 3908, "mtime": 1751475102949, "results": "209", "hashOfConfig": "110"}, {"size": 8029, "mtime": 1751473633905, "results": "210", "hashOfConfig": "110"}, {"size": 3632, "mtime": 1751487806125, "results": "211", "hashOfConfig": "110"}, {"size": 3190, "mtime": 1751481948070, "results": "212", "hashOfConfig": "110"}, {"size": 10720, "mtime": 1751489615015, "results": "213", "hashOfConfig": "110"}, {"size": 5031, "mtime": 1751488922521, "results": "214", "hashOfConfig": "110"}, {"size": 10242, "mtime": 1751488422450, "results": "215", "hashOfConfig": "110"}, {"size": 17670, "mtime": 1751491677848, "results": "216", "hashOfConfig": "110"}, {"size": 6724, "mtime": 1751491604685, "results": "217", "hashOfConfig": "110"}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, "1ymk59w", {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "252", "usedDeprecatedRules": "221"}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "259", "usedDeprecatedRules": "221"}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "266", "usedDeprecatedRules": "221"}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "270", "usedDeprecatedRules": "221"}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "280", "usedDeprecatedRules": "221"}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "284", "usedDeprecatedRules": "221"}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "291", "usedDeprecatedRules": "221"}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "307", "usedDeprecatedRules": "221"}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "311", "usedDeprecatedRules": "221"}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "336", "usedDeprecatedRules": "221"}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "361", "usedDeprecatedRules": "221"}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "386", "usedDeprecatedRules": "221"}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "390", "usedDeprecatedRules": "221"}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "397", "usedDeprecatedRules": "221"}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "410", "usedDeprecatedRules": "221"}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "414", "usedDeprecatedRules": "221"}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "427", "usedDeprecatedRules": "221"}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "434", "usedDeprecatedRules": "221"}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "459", "usedDeprecatedRules": "221"}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "463", "usedDeprecatedRules": "221"}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "470", "usedDeprecatedRules": "221"}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "477", "usedDeprecatedRules": "221"}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "481", "usedDeprecatedRules": "221"}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "497", "usedDeprecatedRules": "221"}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "501", "usedDeprecatedRules": "221"}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "221"}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "520", "usedDeprecatedRules": "221"}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "524", "usedDeprecatedRules": "221"}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "528", "usedDeprecatedRules": "221"}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "535", "usedDeprecatedRules": "221"}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "539", "usedDeprecatedRules": "221"}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "543", "usedDeprecatedRules": "221"}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js", [], [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js", ["574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js", ["591", "592"], [], "import { Col, Form, message, Row, Select, Table } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport {\r\n  addExam,\r\n  deleteQuestionById,\r\n  editExamById,\r\n  getExamById,\r\n} from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Tabs } from \"antd\";\r\nimport AddEditQuestion from \"./AddEditQuestion\";\r\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\r\nconst { TabPane } = Tabs;\r\n\r\nfunction AddEditExam() {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [examData, setExamData] = useState(null);\r\n  const [level, setLevel] = useState('');\r\n  const [showAddEditQuestionModal, setShowAddEditQuestionModal] = useState(false);\r\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n  const [classValue, setClassValue] = useState('');\r\n  const params = useParams();\r\n\r\n  console.log(examData?.questions, \"examData?.questions\")\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      let response;\r\n\r\n      if (params.id) {\r\n        response = await editExamById({\r\n          ...values,\r\n          examId: params.id,\r\n        });\r\n      } else {\r\n        response = await addExam(values);\r\n      }\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        navigate(\"/admin/exams\");\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const getExamData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getExamById({\r\n        examId: params.id,\r\n      });\r\n      setClassValue(response?.data?.class);\r\n      setLevel(response?.data?.level);\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExamData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (params.id) {\r\n      getExamData();\r\n    }\r\n  }, []);\r\n\r\n  const deleteQuestion = async (questionId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteQuestionById({\r\n        questionId,\r\n        examId: params.id\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const questionsColumns = [\r\n    {\r\n      title: \"Question\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Options\",\r\n      dataIndex: \"options\",\r\n      render: (text, record) => {\r\n        if (record?.options && typeof record.options === 'object' && Object.keys(record.options).length > 0) {\r\n          return Object.keys(record.options).map((key) => (\r\n            <div key={key}>\r\n              {key}: {record.options[key]}\r\n            </div>\r\n          ));\r\n        } else {\r\n          return <div>No options available for this question.</div>;\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Correct Answer\",\r\n      dataIndex: \"correctOption\",\r\n      render: (text, record) => {\r\n        if (record.answerType === \"Free Text\") {\r\n          return <div>{record.correctOption}</div>;\r\n        } else {\r\n          return (\r\n            <div>\r\n              {record.correctOption}: {record.options && record.options[record.correctOption] ? record.options[record.correctOption] : record.correctOption}\r\n            </div>\r\n          );\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Source\",\r\n      dataIndex: \"source\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center gap-1\">\r\n          {record?.isAIGenerated ? (\r\n            <span className=\"flex items-center gap-1 text-blue-600 text-sm\">\r\n              🤖 AI\r\n            </span>\r\n          ) : (\r\n            <span className=\"text-gray-600 text-sm\">Manual</span>\r\n          )}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span title=\"Has Image\">🖼️</span>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2 items-center\">\r\n          {/* Edit Button */}\r\n          <i\r\n            className=\"ri-pencil-line cursor-pointer text-blue-600 hover:text-blue-800\"\r\n            title=\"Edit Question\"\r\n            onClick={() => {\r\n              setSelectedQuestion(record);\r\n              setShowAddEditQuestionModal(true);\r\n            }}\r\n          ></i>\r\n\r\n          {/* Add Image Button for AI-generated questions without images */}\r\n          {record?.isAIGenerated && !record?.image && !record?.imageUrl && (\r\n            <i\r\n              className=\"ri-image-add-line cursor-pointer text-green-600 hover:text-green-800\"\r\n              title=\"Add Image to AI Question\"\r\n              onClick={() => {\r\n                setSelectedQuestion(record);\r\n                setShowAddEditQuestionModal(true);\r\n              }}\r\n            ></i>\r\n          )}\r\n\r\n          {/* AI Generated Indicator */}\r\n          {record?.isAIGenerated && (\r\n            <span\r\n              className=\"text-blue-500 text-sm\"\r\n              title=\"AI Generated Question\"\r\n            >\r\n              🤖\r\n            </span>\r\n          )}\r\n\r\n          {/* Image Indicator */}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span\r\n              className=\"text-green-500 text-sm\"\r\n              title=\"Has Image\"\r\n            >\r\n              🖼️\r\n            </span>\r\n          )}\r\n\r\n          {/* Delete Button */}\r\n          <i\r\n            className=\"ri-delete-bin-line cursor-pointer text-red-600 hover:text-red-800\"\r\n            title=\"Delete Question\"\r\n            onClick={() => {\r\n              deleteQuestion(record._id);\r\n            }}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const handleLevelChange = (e) => {\r\n    setLevel(e.target.value);\r\n    setClassValue(\"\"); // Reset class\r\n  };\r\n\r\n  console.log(classValue, \"classValue\")\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <PageTitle title={params.id ? \"Edit Exam\" : \"Add Exam\"} />\r\n      <div className=\"divider\"></div>\r\n\r\n      {(examData || !params.id) && (\r\n        <Form layout=\"vertical\" onFinish={onFinish} initialValues={examData}>\r\n          <Tabs defaultActiveKey=\"1\">\r\n            <TabPane tab=\"Exam Details\" key=\"1\">\r\n              <Row gutter={[10, 10]}>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Name\" name=\"name\">\r\n                    <input type=\"text\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Topic\" name=\"topic\">\r\n                    <input type=\"text\" placeholder=\"Enter quiz topic (e.g., Algebra, Cell Biology)\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Duration (Seconds)\" name=\"duration\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n\r\n\r\n                <Col span={8}>\r\n                  <Form.Item name=\"level\" label=\"Level\" initialValue=\"\">\r\n                    <select value={level} onChange={handleLevelChange}   >\r\n                      <option value=\"\" disabled >\r\n                        Select Level\r\n                      </option>\r\n                      <option value=\"Primary\">Primary</option>\r\n                      <option value=\"Secondary\">Secondary</option>\r\n                      <option value=\"Advance\">Advance</option>\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Category\" name=\"category\">\r\n                    <select name=\"\" id=\"\">\r\n                      <option value=\"\">Select Category</option>\r\n                      {level === \"primary\" && (\r\n                        <>\r\n                          {primarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level === \"secondary\" && (\r\n                        <>\r\n                          {secondarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level === \"advance\" && (\r\n                        <>\r\n                          {advanceSubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n\r\n                  <Form.Item name=\"class\" label=\"Class\" initialValue=\"\" required>\r\n                    <select value={classValue} onChange={(e) => setClassValue(e.target.value)}>\r\n                      <option value=\"\"  >\r\n                        Select Class\r\n                      </option>\r\n                      {level === \"primary\" && (\r\n                        <>\r\n                          <option value=\"1\">1</option>\r\n                          <option value=\"2\">2</option>\r\n                          <option value=\"3\">3</option>\r\n                          <option value=\"4\">4</option>\r\n                          <option value=\"5\">5</option>\r\n                          <option value=\"6\">6</option>\r\n                          <option value=\"7\">7</option>\r\n                        </>\r\n                      )}\r\n                      {level === \"secondary\" && (\r\n                        <>\r\n                          <option value=\"Form-1\">Form-1</option>\r\n                          <option value=\"Form-2\">Form-2</option>\r\n                          <option value=\"Form-3\">Form-3</option>\r\n                          <option value=\"Form-4\">Form-4</option>\r\n                        </>\r\n                      )}\r\n                      {level === \"advance\" && (\r\n                        <>\r\n                          <option value=\"Form-5\">Form-5</option>\r\n                          <option value=\"Form-6\">Form-6</option>\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Total Marks\" name=\"totalMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Passing Marks\" name=\"passingMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n              </Row>\r\n              <div className=\"flex justify-end gap-2\">\r\n                <button\r\n                  className=\"primary-outlined-btn\"\r\n                  type=\"button\"\r\n                  onClick={() => navigate(\"/admin/exams\")}\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button className=\"primary-contained-btn\" type=\"submit\">\r\n                  Save\r\n                </button>\r\n              </div>\r\n            </TabPane>\r\n            {params.id && (\r\n              <TabPane tab=\"Questions\" key=\"2\">\r\n                <div className=\"flex justify-end\">\r\n                  <button\r\n                    className=\"primary-outlined-btn\"\r\n                    type=\"button\"\r\n                    onClick={() => setShowAddEditQuestionModal(true)}\r\n                  >\r\n                    Add Question\r\n                  </button>\r\n                </div>\r\n\r\n                <Table\r\n                  columns={questionsColumns}\r\n                  dataSource={examData?.questions || []}\r\n                />\r\n              </TabPane>\r\n            )}\r\n          </Tabs>\r\n        </Form>\r\n      )}\r\n\r\n      {showAddEditQuestionModal && (\r\n        <AddEditQuestion\r\n          setShowAddEditQuestionModal={setShowAddEditQuestionModal}\r\n          showAddEditQuestionModal={showAddEditQuestionModal}\r\n          examId={params.id}\r\n          refreshData={getExamData}\r\n          selectedQuestion={selectedQuestion}\r\n          setSelectedQuestion={setSelectedQuestion}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AddEditExam;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js", ["593"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\Plans.jsx", ["594"], [], "import React, { useEffect, useState } from \"react\";\r\nimport { getPlans } from \"../../../apicalls/plans\";\r\nimport \"./Plans.css\";\r\nimport ConfirmModal from \"./components/ConfirmModal\";\r\nimport WaitingModal from \"./components/WaitingModal\";\r\nimport { addPayment } from \"../../../apicalls/payment\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { setPaymentVerificationNeeded } from \"../../../redux/paymentSlice\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nconst Plans = () => {\r\n    const [plans, setPlans] = useState([]);\r\n    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);\r\n    const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);\r\n    const [paymentInProgress, setPaymentInProgress] = useState(false);\r\n    const [selectedPlan, setSelectedPlan] = useState(null);\r\n    const { user } = useSelector((state) => state.user);\r\n    const { subscriptionData } = useSelector((state) => state.subscription);\r\n    const dispatch = useDispatch()\r\n\r\n    useEffect(() => {\r\n        const fetchPlans = async () => {\r\n            try {\r\n                const response = await getPlans();\r\n                setPlans(response);\r\n            } catch (error) {\r\n                console.error(\"Error fetching plans:\", error);\r\n            }\r\n        };\r\n\r\n        fetchPlans();\r\n    }, []);\r\n\r\n    const transactionDetails = {\r\n        amount: selectedPlan?.discountedPrice || 'N/A',\r\n        currency: \"TZS\",\r\n        destination: \"brainwave.zone\",\r\n    };\r\n\r\n\r\n    const handlePaymentStart = async (plan) => {\r\n        setSelectedPlan(plan);\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const response = await addPayment({ plan });\r\n            localStorage.setItem(\"order_id\", response.order_id);\r\n            setWaitingModalOpen(true);\r\n            setPaymentInProgress(true);\r\n            dispatch(setPaymentVerificationNeeded(true));\r\n        } catch (error) {\r\n            console.error(\"Error processing payment:\", error);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n\r\n    useEffect(() => {\r\n        console.log(\"subscription Data in Plans\", subscriptionData)\r\n        if (user?.paymentRequired === true && subscriptionData?.paymentStatus === \"paid\" && paymentInProgress) {\r\n            setWaitingModalOpen(false);\r\n            setConfirmModalOpen(true);\r\n            setPaymentInProgress(false);\r\n        }\r\n    }, [user, subscriptionData]);\r\n\r\n    return (\r\n        <div>\r\n            {!user ?\r\n                <>\r\n                </>\r\n                :\r\n                !user.paymentRequired ?\r\n                    <div className=\"no-plan-required\">\r\n                        <div className=\"no-plan-content\">\r\n                            <h2>No Plan Required</h2>\r\n                            <p>You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!</p>\r\n                        </div>\r\n                    </div>\r\n                    :\r\n                    subscriptionData?.paymentStatus !== \"paid\" ?\r\n                        <div className=\"plans-container\">\r\n                            {plans.map((plan) => (\r\n                                <div\r\n                                    key={plan._id}\r\n                                    className={`plan-card ${plan.title === \"Standard Membership\" ? \"basic\" : \"\"}`}\r\n                                >\r\n                                    {plan.title === \"Standard Membership\" && (\r\n                                        <div className=\"most-popular-label\">MOST POPULAR</div>\r\n                                    )}\r\n\r\n                                    <h2 className=\"plan-title\">{plan.title}</h2>\r\n                                    <p className=\"plan-actual-price\">\r\n                                        {plan.actualPrice.toLocaleString()} TZS\r\n                                    </p>\r\n                                    <p className=\"plan-discounted-price\">\r\n                                        {plan.discountedPrice.toLocaleString()} TZS\r\n                                    </p>\r\n                                    <span className=\"plan-discount-tag\">\r\n                                        {plan.discountPercentage}% OFF\r\n                                    </span>\r\n                                    <p className=\"plan-renewal-info\">\r\n                                        For {plan?.features[0]}\r\n                                    </p>\r\n                                    <button className=\"plan-button\"\r\n                                        // onClick={() => setConfirmModalOpen(true)}\r\n                                        onClick={() => handlePaymentStart(plan)}\r\n                                    >Choose Plan</button>\r\n                                    <ul className=\"plan-features\">\r\n                                        {plan.features.map((feature, index) => (\r\n                                            <li key={index} className=\"plan-feature\">\r\n                                                <span className=\"plan-feature-icon\">✔</span>\r\n                                                {feature}\r\n                                            </li>\r\n                                        ))}\r\n                                    </ul>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        :\r\n                        <div className=\"subscription-details\">\r\n                            <h1 className=\"plan-title\">{subscriptionData.plan.title}</h1>\r\n\r\n                            <svg\r\n                                width=\"64px\"\r\n                                height=\"64px\"\r\n                                viewBox=\"-3.2 -3.2 38.40 38.40\"\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"#10B981\"\r\n                                stroke=\"#253864\"\r\n                                transform=\"matrix(1, 0, 0, 1, 0, 0)\"\r\n                            >\r\n                                <g id=\"SVGRepo_bgCarrier\" strokeWidth=\"0\"></g>\r\n                                <g id=\"SVGRepo_tracerCarrier\" strokeLinecap=\"round\" strokeLinejoin=\"round\" stroke=\"#CCCCCC\" strokeWidth=\"0.064\"></g>\r\n                                <g id=\"SVGRepo_iconCarrier\">\r\n                                    <path\r\n                                        d=\"m16 0c8.836556 0 16 7.163444 16 16s-7.163444 16-16 16-16-7.163444-16-16 7.163444-16 16-16zm5.7279221 11-7.0710679 7.0710678-4.2426406-4.2426407-1.4142136 1.4142136 5.6568542 5.6568542 8.4852814-8.4852813z\"\r\n                                        fill=\"#202327\"\r\n                                        fillRule=\"evenodd\"\r\n                                    ></path>\r\n                                </g>\r\n                            </svg>\r\n\r\n                            <p className=\"plan-description\">{subscriptionData?.plan?.subscriptionData}</p>\r\n                            <p className=\"plan-dates\">Start Date: {subscriptionData.startDate}</p>\r\n                            <p className=\"plan-dates\">End Date: {subscriptionData.endDate}</p>\r\n                        </div>\r\n            }\r\n\r\n            <WaitingModal\r\n                isOpen={isWaitingModalOpen}\r\n                onClose={() => setWaitingModalOpen(false)}\r\n            />\r\n\r\n            <ConfirmModal\r\n                isOpen={isConfirmModalOpen}\r\n                onClose={() => setConfirmModalOpen(false)}\r\n                transaction={transactionDetails}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Plans;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js", ["595", "596", "597"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js", ["598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614"], [], "import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n// import { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport {\n  FaPlayCircle,\n  FaBook,\n  FaVideo,\n  FaFileAlt,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n} from \"react-icons/fa\";\nimport {\n  TbVideo,\n  TbFileText,\n  TbBook as TbBookIcon,\n  TbScho<PERSON>,\n  Tb<PERSON><PERSON><PERSON>,\n  <PERSON>b<PERSON><PERSON><PERSON>,\n  Tb<PERSON>ortAscending,\n  Tb<PERSON><PERSON>,\n  TbD<PERSON>load,\n  Tb<PERSON><PERSON>,\n  TbCalendar,\n  Tb<PERSON>ser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  TbAlertTriangle,\n  TbInfoCircle,\n  TbCheck,\n  TbSubtitles,\n  TbBooks,\n  TbCertificate\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction StudyMaterial() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = user?.level || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary'\n    ? primarySubjects\n    : userLevelLower === 'secondary'\n      ? secondarySubjects\n      : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary'\n    ? ['1', '2', '3', '4', '5', '6', '7']\n    : userLevelLower === 'secondary'\n      ? ['Form-1', 'Form-2', 'Form-3', 'Form-4']\n      : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"videos\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || user?.className || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = user?.class || user?.className;\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n  const [selectedSubtitle, setSelectedSubtitle] = useState('off');\n  const [videoRef, setVideoRef] = useState(null);\n\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = user?.class || user?.className;\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user?.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user?.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" :\n        selectedClass.toString().replace(\"Form-\", \"\");\n\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject, // This can be \"all\" or a specific subject\n      };\n      if (userLevel) {\n        data.level = userLevel;\n      }\n\n      const res = await getStudyMaterial(data);\n\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = (tab) => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n\n  const handleSubjectChange = (subject) => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n\n  const handleClassChange = (className) => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material =>\n        material.title.toLowerCase().includes(searchLower) ||\n        material.subject.toLowerCase().includes(searchLower) ||\n        (material.year && material.year.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n        // For videos (no year field), sort by creation date or reverse order\n        else if (activeTab === \"videos\") {\n          // Since videos are fetched in reverse order from server, maintain that for \"newest\"\n          return 0; // Keep original order (newest first from server)\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n        // For videos, reverse the order\n        else if (activeTab === \"videos\") {\n          return 0; // Will be reversed after sort\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n    // For videos with \"oldest\" sort, reverse the array\n    if (activeTab === \"videos\" && sortBy === \"oldest\") {\n      filtered.reverse();\n    }\n\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = (documentUrl) => {\n    // Use proxy endpoint to handle CORS issues\n    const proxyUrl = `${process.env.REACT_APP_SERVER_DOMAIN}/api/study/document-proxy?url=${encodeURIComponent(documentUrl)}`;\n\n    fetch(proxyUrl, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem('token')}`,\n      }\n    })\n      .then((response) => {\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        return response.blob();\n      })\n      .then((blob) => {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = documentUrl.split(\"/\").pop();\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      })\n      .catch((error) => {\n        console.error(\"Error downloading the file:\", error);\n        // Fallback to direct download if proxy fails\n        window.open(documentUrl, '_blank');\n      });\n  };\n\n  const handleDocumentPreview = (documentUrl) => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedMaterials[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    setSelectedSubtitle('off');\n    setVideoRef(null);\n  };\n\n  // Handle subtitle selection\n  const handleSubtitleChange = (language) => {\n    setSelectedSubtitle(language);\n\n    if (videoRef) {\n      const tracks = videoRef.textTracks;\n\n      // Disable all tracks first\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'disabled';\n      }\n\n      // Enable selected track\n      if (language !== 'off') {\n        for (let i = 0; i < tracks.length; i++) {\n          if (tracks[i].language === language) {\n            tracks[i].mode = 'showing';\n            break;\n          }\n        }\n      }\n    }\n  };\n\n  const handleExpandVideo = () => {\n    setIsVideoExpanded(true);\n  };\n\n  const handleCollapseVideo = () => {\n    setIsVideoExpanded(false);\n  };\n\n\n\n\n\n\n\n\n\n  // Note: Auto-refresh removed since videos are now uploaded synchronously\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          credentials: 'include'\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.warn('⚠️ Failed to get signed URL, using original:', error.message);\n        return videoUrl;\n      }\n    }\n\n    // For other URLs or if signed URL fails, return as-is\n    return videoUrl;\n  };\n\n\n\n\n\n  // Get appropriate thumbnail URL for video\n  const getThumbnailUrl = (material) => {\n    // If we have a custom thumbnail, use it\n    if (material.thumbnail && material.thumbnail !== \"\" && material.thumbnail !== \"processing\") {\n      return material.thumbnail;\n    }\n\n    // For YouTube videos, extract video ID and use YouTube thumbnail\n    if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n      // Extract YouTube video ID if it's a full URL\n      let videoId = material.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n\n    // For uploaded videos without thumbnails, use a default placeholder\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n  };\n\n  // Keyboard support for video modal\n  useEffect(() => {\n    const handleKeyPress = (event) => {\n      if (showVideoIndices.length > 0) {\n        switch (event.key) {\n          case 'Escape':\n            handleHideVideo();\n            break;\n          case 'f':\n          case 'F':\n            if (!isVideoExpanded) {\n              handleExpandVideo();\n            } else {\n              handleCollapseVideo();\n            }\n            break;\n          default:\n            break;\n        }\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyPress);\n    return () => {\n      document.removeEventListener('keydown', handleKeyPress);\n    };\n  }, [showVideoIndices, isVideoExpanded]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\n      {/* Modern Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-gradient-to-r from-primary-600 to-blue-600 text-white\"\n      >\n        <div className=\"container-modern py-12\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\">\n                <TbBooks className=\"w-8 h-8 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-4xl font-bold mb-2\">Study Materials</h1>\n                <p className=\"text-xl text-blue-100\">\n                  Access comprehensive learning resources for {userLevel} education\n                </p>\n              </div>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3\">\n                <div className=\"text-sm text-blue-100 mb-1\">Current Level</div>\n                <div className=\"text-lg font-bold\">{userLevel?.toUpperCase()}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      <div className=\"container-modern py-8\">\n        {/* Study Material Tabs */}\n        <div className=\"mb-6\">\n          <div className=\"study-tabs\">\n            {[\n              { key: 'videos', label: 'Videos', icon: TbVideo },\n              { key: 'study-notes', label: 'Notes', icon: TbFileText },\n              { key: 'past-papers', label: 'Past Papers', icon: TbCertificate },\n              { key: 'books', label: 'Books', icon: TbBookIcon }\n            ].map((tab) => (\n              <button\n                key={tab.key}\n                className={`study-tab ${activeTab === tab.key ? 'active' : ''}`}\n                onClick={() => handleTabChange(tab.key)}\n              >\n                <tab.icon />\n                <span>{tab.label}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Modern Filters Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"mb-8\"\n        >\n          <div className=\"card p-6\">\n            <div className=\"flex flex-col lg:flex-row gap-6 items-end\">\n              {/* Search */}\n              <div className=\"flex-1\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Search Materials\n                </label>\n                <input\n                  placeholder={`Search ${activeTab.replace('-', ' ')}...`}\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"form-input\"\n                />\n              </div>\n\n              {/* Class Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Filter by Class\n                  {userCurrentClass && (\n                    <span className=\"ml-2 text-xs text-primary-600 font-medium\">\n                      (Your class: {userLevelLower === 'primary' ? `Class ${userCurrentClass}` : `Form ${userCurrentClass}`})\n                    </span>\n                  )}\n                </label>\n                <div className=\"relative\">\n                  <button\n                    onClick={toggleClassSelector}\n                    className=\"w-full input-modern flex items-center justify-between\"\n                  >\n                    <span className=\"flex items-center space-x-2\">\n                      <TbSchool className=\"w-4 h-4 text-gray-400\" />\n                      <span>\n                        {selectedClass === 'all' ? 'All Classes' :\n                          userLevelLower === 'primary'\n                            ? `Class ${selectedClass}`\n                            : `Form ${selectedClass}`\n                        }\n                      </span>\n                      {selectedClass === userCurrentClass && (\n                        <span className=\"badge-primary text-xs\">Current</span>\n                      )}\n                    </span>\n                    <TbChevronDownIcon className={`w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  <AnimatePresence>\n                    {showClassSelector && (\n                      <motion.div\n                        initial={{ opacity: 0, y: -10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        exit={{ opacity: 0, y: -10 }}\n                        className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\"\n                      >\n                        <button\n                          className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${\n                            selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                          }`}\n                          onClick={() => handleClassChange('all')}\n                        >\n                          All Classes\n                        </button>\n                        {availableClasses.map((className, index) => (\n                          <button\n                            key={index}\n                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${\n                              selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                            }`}\n                            onClick={() => handleClassChange(className)}\n                          >\n                            <span>\n                              {userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`}\n                            </span>\n                            {className === userCurrentClass && (\n                              <span className=\"badge-success text-xs\">Your Class</span>\n                            )}\n                          </button>\n                        ))}\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </div>\n              </div>\n\n              {/* Subject Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Filter by Subject\n                </label>\n                <select\n                  value={selectedSubject}\n                  onChange={(e) => handleSubjectChange(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"all\">All Subjects</option>\n                  {subjectsList.map((subject, index) => (\n                    <option key={index} value={subject}>\n                      {subject}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Sort */}\n              <div className=\"w-full lg:w-48\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Sort by\n                </label>\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"oldest\">Oldest First</option>\n                  <option value=\"title\">By Title</option>\n                </select>\n              </div>\n\n              {/* Clear Filters */}\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => {\n                  setSearchTerm(\"\");\n                  setSelectedClass(\"all\");\n                  setSelectedSubject(\"all\");\n                  setSortBy(\"newest\");\n                }}\n              >\n                Clear Filters\n              </button>\n            </div>\n\n            {/* Results Count */}\n            {(searchTerm || selectedClass !== \"all\" || selectedSubject !== \"all\") && (\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\n                <span className=\"text-sm text-gray-600\">\n                  Showing {filteredAndSortedMaterials.length} of {materials.length} {activeTab.replace('-', ' ')}\n                </span>\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n      {/* Materials Display */}\n      <div className=\"materials-section\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading materials...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <FaTimes className=\"error-icon\" />\n            <h3>Error Loading Materials</h3>\n            <p>{error}</p>\n            <button\n              className=\"retry-btn\"\n              onClick={() => {\n                setError(null);\n                fetchMaterials();\n              }}\n            >\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedMaterials.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {filteredAndSortedMaterials.map((material, index) => (\n              <div key={index} className=\"study-card\">\n                <div className=\"study-card-header\">\n                  <div className=\"study-card-meta\">\n                    {activeTab === 'videos' && <FaVideo />}\n                    {activeTab === 'study-notes' && <FaFileAlt />}\n                    {activeTab === 'past-papers' && <FaFileAlt />}\n                    {activeTab === 'books' && <FaBook />}\n                    <span>\n                      {activeTab === 'study-notes' ? 'Note' :\n                       activeTab === 'past-papers' ? 'Past Paper' :\n                       activeTab === 'videos' ? 'Video' : 'Book'}\n                    </span>\n                  </div>\n\n                  <div className=\"study-card-title\">\n                    {material.title}\n                  </div>\n\n                  {/* Class tags for videos */}\n                  {activeTab === 'videos' && material.coreClass && (\n                    <div className=\"d-flex gap-2 mt-2\">\n                      {material.isCore ? (\n                        <span className=\"badge badge-primary\">\n                          Core Class {userLevelLower === 'primary' ? material.coreClass : `Form ${material.coreClass}`}\n                        </span>\n                      ) : material.sharedFromClass && (\n                        <span className=\"badge badge-secondary\">\n                          Shared from {userLevelLower === 'primary' ? `Class ${material.sharedFromClass}` : `Form ${material.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  )}\n                  {material.year && (\n                    <span className=\"badge badge-secondary mt-2\">{material.year}</span>\n                  )}\n                </div>\n\n                {/* Video Thumbnail for videos */}\n                {activeTab === 'videos' && (material.videoUrl || material.videoID) && (\n                  <div className=\"video-thumbnail-container\" onClick={() => handleShowVideo(index)}>\n                    <img\n                      src={getThumbnailUrl(material)}\n                      alt={material.title}\n                      className=\"video-thumbnail\"\n                      onError={(e) => {\n                        // Fallback logic for failed thumbnails\n                        if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n                          // For YouTube videos, try different quality thumbnails\n                          let videoId = material.videoID;\n                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                          }\n\n                          if (!e.target.src.includes('youtube.com')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n                          } else if (e.target.src.includes('maxresdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;\n                          } else if (e.target.src.includes('mqdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n                          } else {\n                            // Final fallback to default placeholder\n                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                          }\n                        } else {\n                          // For uploaded videos without thumbnails, use default placeholder\n                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                      <span className=\"play-text\">Watch Now</span>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"card-content\">\n                  <h3 className=\"material-title\">{material.title}</h3>\n                  <div className=\"material-meta\">\n                    <span className=\"material-subject\">{material.subject}</span>\n                    {material.className && (\n                      <span className=\"material-class\">\n                        {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"card-actions\">\n                  {activeTab === 'videos' && (material.videoUrl || material.videoID) ? (\n                    <div className=\"video-info-text\">\n                      <span className=\"video-duration\">Click thumbnail to play</span>\n                    </div>\n                  ) : material.documentUrl ? (\n                    <>\n                      <button\n                        className=\"action-btn secondary\"\n                        onClick={() => handleDocumentPreview(material.documentUrl)}\n                      >\n                        <FaEye /> View\n                      </button>\n                      <button\n                        className=\"action-btn primary\"\n                        onClick={() => handleDocumentDownload(material.documentUrl)}\n                      >\n                        <FaDownload /> Download\n                      </button>\n                    </>\n                  ) : (\n                    <span className=\"unavailable\">Not available</span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Materials Found</h3>\n            <p>No study materials are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedMaterials[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <>\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3>{video.title || 'Untitled Video'}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject || 'Unknown Subject'}</span>\n                        <span className=\"video-class\">Class {video.className || 'N/A'}</span>\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      {!isVideoExpanded ? (\n                        <button\n                          className=\"control-btn expand-btn\"\n                          onClick={handleExpandVideo}\n                          title=\"Expand to fullscreen\"\n                        >\n                          <FaExpand />\n                        </button>\n                      ) : (\n                        <button\n                          className=\"control-btn collapse-btn\"\n                          onClick={handleCollapseVideo}\n                          title=\"Exit fullscreen\"\n                        >\n                          <FaCompress />\n                        </button>\n                      )}\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadedMetadata={() => {\n                              // Auto-enable first subtitle if available and none selected\n                              if (video.subtitles && video.subtitles.length > 0 && selectedSubtitle === 'off') {\n                                const defaultSubtitle = video.subtitles.find(sub => sub.isDefault) || video.subtitles[0];\n                                handleSubtitleChange(defaultSubtitle.language);\n                              }\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            <p style={{color: 'white', textAlign: 'center', padding: '20px'}}>\n                              Your browser does not support the video tag.\n                              <br />\n                              <a href={video.signedVideoUrl || video.videoUrl} target=\"_blank\" rel=\"noopener noreferrer\" style={{color: '#4fc3f7'}}>\n                                Click here to open video in new tab\n                              </a>\n                            </p>\n                          </video>\n\n                          {/* Custom Subtitle Controls */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div style={{\n                              padding: '12px 15px',\n                              background: 'rgba(0,0,0,0.8)',\n                              borderRadius: '0 0 8px 8px',\n                              borderTop: '1px solid #333'\n                            }}>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '12px',\n                                flexWrap: 'wrap'\n                              }}>\n                                <span style={{\n                                  color: '#fff',\n                                  fontSize: '14px',\n                                  fontWeight: '500',\n                                  minWidth: 'fit-content'\n                                }}>\n                                  📝 Choose Language:\n                                </span>\n\n                                <div style={{\n                                  display: 'flex',\n                                  gap: '8px',\n                                  flexWrap: 'wrap',\n                                  flex: 1\n                                }}>\n                                  {/* Off Button */}\n                                  <button\n                                    onClick={() => handleSubtitleChange('off')}\n                                    style={{\n                                      padding: '6px 12px',\n                                      borderRadius: '20px',\n                                      border: 'none',\n                                      fontSize: '12px',\n                                      fontWeight: '500',\n                                      cursor: 'pointer',\n                                      transition: 'all 0.2s ease',\n                                      backgroundColor: selectedSubtitle === 'off' ? '#ff4757' : '#2f3542',\n                                      color: '#fff'\n                                    }}\n                                  >\n                                    OFF\n                                  </button>\n\n                                  {/* Language Buttons */}\n                                  {video.subtitles.map((subtitle) => (\n                                    <button\n                                      key={subtitle.language}\n                                      onClick={() => handleSubtitleChange(subtitle.language)}\n                                      style={{\n                                        padding: '6px 12px',\n                                        borderRadius: '20px',\n                                        border: 'none',\n                                        fontSize: '12px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s ease',\n                                        backgroundColor: selectedSubtitle === subtitle.language ? '#2ed573' : '#57606f',\n                                        color: '#fff',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '4px'\n                                      }}\n                                    >\n                                      <span>{subtitle.languageName}</span>\n                                      {subtitle.isAutoGenerated && (\n                                        <span style={{\n                                          fontSize: '10px',\n                                          opacity: 0.8,\n                                          backgroundColor: 'rgba(255,255,255,0.2)',\n                                          padding: '1px 4px',\n                                          borderRadius: '8px'\n                                        }}>\n                                          AI\n                                        </span>\n                                      )}\n                                    </button>\n                                  ))}\n                                </div>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  {!isVideoExpanded && (\n                    <div className=\"video-footer\">\n                      <div className=\"video-description\">\n                        <p>Watch this educational video to learn more about {video.subject}.</p>\n                        {video.subtitleGenerationStatus === 'processing' && (\n                          <div className=\"subtitle-status\" style={{\n                            marginTop: '10px',\n                            fontSize: '0.9em',\n                            color: '#2196F3',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '8px'\n                          }}>\n                            <div style={{\n                              width: '16px',\n                              height: '16px',\n                              border: '2px solid #2196F3',\n                              borderTop: '2px solid transparent',\n                              borderRadius: '50%',\n                              animation: 'spin 1s linear infinite'\n                            }}></div>\n                            🤖 Generating subtitles...\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n\n      {/* PDF Modal */}\n      <PDFModal\n        modalIsOpen={modalIsOpen}\n        closeModal={() => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        }}\n        documentUrl={documentUrl}\n      />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterial;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js", ["615", "616"], [], "import React from \"react\";\r\nimport './index.css';\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Modal, Table } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { useEffect } from \"react\";\r\nimport moment from \"moment\";\r\n\r\nfunction UserReports() {\r\n  const [reportsData, setReportsData] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => <>{record.exam?.name}</>,\r\n    },\r\n    {\r\n      title: \"Date\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <>{moment(record.createdAt).format(\"DD-MM-YYYY hh:mm:ss\")}</>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalQuestions\",\r\n      render: (text, record) => <>{record.exam?.totalMarks}</>,\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.exam?.passingMarks}</>,\r\n    },\r\n    {\r\n      title: \"Obtained Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.result?.correctAnswers.length}</>,\r\n    },\r\n    {\r\n      title: \"Verdict\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => <>{record.result?.verdict}</>,\r\n    },\r\n  ];\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"reports-container\">\r\n      <PageTitle title=\"Reports\" />\r\n      <div className=\"divider\"></div>\r\n      <Table \r\n      columns={columns} \r\n      dataSource={reportsData} \r\n      rowKey={(record) => record._id} \r\n      scroll={{ x: true }} \r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserReports;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js", ["617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js", ["628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js", ["644", "645"], [], "import React, { useEffect, useState, Suspense } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\n\r\nimport { message } from \"antd\";\r\nconst Test = () => {\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n\r\n\r\n    useEffect(() => {\r\n        const getUserData = async () => {\r\n            try {\r\n                const response = await getUserInfo();\r\n                if (response.success) {\r\n                    if (response.data.isAdmin) {\r\n                        setIsAdmin(true);\r\n                    } else {\r\n                        setIsAdmin(false);\r\n                        setUserData(response.data);\r\n                    }\r\n                } else {\r\n                    message.error(response.message);\r\n                }\r\n            } catch (error) {\r\n                message.error(error.message);\r\n            }\r\n        };\r\n        if (localStorage.getItem(\"token\")) {\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        // <Suspense fallback={<div>Loading...</div>}>\r\n        <div className=\"\">\r\n            <div>{userData.name}</div>\r\n            <div>{userData.school}</div>\r\n            <div>{userData.class}</div>\r\n        </div>\r\n        // </Suspense>\r\n    );\r\n}\r\n\r\nexport default Test;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js", ["646"], [], "import React, { useEffect, useState } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Rate } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addReview, getAllReviews } from \"../../../apicalls/reviews\";\r\nimport image from '../../../assets/person.png';\r\n\r\nconst AboutUs = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [userRating, setUserRating] = useState('');\r\n    const [userText, setUserText] = useState('');\r\n    const [reviews, setReviews] = useState('');\r\n    const [userOldReview, setUserOldReview] = useState(null);\r\n    const dispatch = useDispatch();\r\n\r\n    const getReviews = async () => {\r\n        try {\r\n            const response = await getAllReviews();\r\n            if (response.success) {\r\n                setReviews(response.data.reverse());\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    }\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await getReviews();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const handleRatingChange = (value) => {\r\n        setUserRating(value);\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        if (userRating === '' || userRating === 0 || userText === '') {\r\n            return;\r\n        }\r\n        try {\r\n            const data = {\r\n                rating: userRating,\r\n                text: userText\r\n            }\r\n            const response = await addReview(data);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                getReviews();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n            dispatch(HideLoading());\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (reviews) {\r\n            const userInReview = reviews.find(review => review.user._id === userData._id);\r\n            setUserOldReview(userInReview);\r\n        }\r\n    }, [reviews, userData]);\r\n\r\n    return (\r\n        <div className=\"AboutUs\">\r\n            {!isAdmin &&\r\n                <>\r\n                    <PageTitle title=\"About Us\" />\r\n                    <div className=\"divider\"></div>\r\n                    <p className=\"info-para\">\r\n                        Welcome to our web application! Lorem ipsum dolor sit amet, consectetur adipiscing elit.\r\n                        Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,\r\n                        quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\r\n                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\r\n                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\r\n                        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\r\n                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\r\n                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n                    </p>\r\n                    {!userOldReview ?\r\n                        <>\r\n                            <h1>Feedback</h1>\r\n                            <p>\r\n                                We strive to provide an exceptional user experience and value your feedback.<br />\r\n                                Please take a moment to rate our web app:\r\n                            </p>\r\n                            <div><b>Rate Your Experience:</b></div>\r\n                            <div className=\"rating\">\r\n                                <div>\r\n                                    <Rate defaultValue={0} onChange={handleRatingChange} />\r\n                                    <br />\r\n                                    <textarea\r\n                                        className=\"rating-text\"\r\n                                        placeholder=\"Share your thoughts...\"\r\n                                        rows={4}\r\n                                        value={userText}\r\n                                        onChange={(e) => setUserText(e.target.value)}\r\n                                    />\r\n                                </div>\r\n                                <button onClick={handleSubmit}>Submit</button>\r\n                            </div>\r\n                        </>\r\n                        :\r\n                        <>\r\n                            <h2>Your Feedback</h2>\r\n                            <div className=\"p-rating-div\">\r\n                                <div className=\"profile-row\">\r\n                                    <img className=\"profile\" src={userOldReview.user.profileImage ? userOldReview.user.profileImage : image} alt=\"profile\" onError={(e) => { e.target.src = image }} />\r\n                                    <p>{userOldReview.user.name}</p>\r\n                                </div>\r\n                                <Rate defaultValue={userOldReview.rating} className=\"rate\" disabled={true} />\r\n                                <br />\r\n                                <div className=\"text\">{userOldReview.text}</div>\r\n                            </div>\r\n                        </>\r\n                    }\r\n                    <h2>Previous Reviews</h2>\r\n                    {reviews ?\r\n                        <div className=\"p-ratings\">\r\n                            {reviews.map((review, index) => (\r\n                                <div key={index}>\r\n                                    {userOldReview?.user._id !== review.user?._id && review.user?._id &&\r\n                                        <div className=\"p-rating-div\">\r\n                                            <div className=\"profile-row\">\r\n                                                <img className=\"profile\" src={review.user.profileImage ? review.user.profileImage : image} alt=\"profile\" onError={(e) => { e.target.src = image }} />\r\n                                                <p>{review.user.name}</p>\r\n                                            </div>\r\n                                            <Rate defaultValue={review.rating} className=\"rate\" disabled={true} />\r\n                                            <br />\r\n                                            <div className=\"text\">{review.text}</div>\r\n                                        </div>\r\n                                    }\r\n                                </div>\r\n                            ))\r\n                            }\r\n                        </div>\r\n                        :\r\n                        <div>\r\n                            No reviews yet.    \r\n                        </div>\r\n                    }\r\n                </>\r\n            }\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default AboutUs;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js", ["647", "648"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js", ["649", "650", "651", "652", "653"], [], "import { message } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport \"./index.css\";\r\nimport {\r\n  getAllUsers,\r\n  blockUserById,\r\n  deleteUserById,\r\n} from \"../../../apicalls/users\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\r\nimport {\r\n  TbUsers,\r\n  TbSearch,\r\n  TbFilter,\r\n  TbUserCheck,\r\n  TbUserX,\r\n  TbTrash,\r\n  TbEye,\r\n  TbSchool,\r\n  TbMail,\r\n  TbUser,\r\n  TbCrown,\r\n  TbClock,\r\n  TbX\r\n} from \"react-icons/tb\";\r\n\r\nfunction Users() {\r\n  const navigate = useNavigate();\r\n  const [users, setUsers] = useState([]);\r\n  const [filteredUsers, setFilteredUsers] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"all\");\r\n  const [filterSubscription, setFilterSubscription] = useState(\"all\");\r\n  const [loading, setLoading] = useState(false);\r\n  const dispatch = useDispatch();\r\n\r\n  // Function to determine subscription status for filtering based on subscription dates\r\n  const getSubscriptionFilterStatus = (user) => {\r\n    const now = new Date();\r\n    const paymentRequired = user.paymentRequired;\r\n    const subscriptionEndDate = user.subscriptionEndDate;\r\n    const subscriptionStartDate = user.subscriptionStartDate;\r\n\r\n    // Debug logging (can be removed in production)\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log(`User ${user.name}:`, {\r\n        paymentRequired,\r\n        subscriptionStartDate,\r\n        subscriptionEndDate,\r\n        isExpired: subscriptionEndDate ? new Date(subscriptionEndDate) < now : 'no end date'\r\n      });\r\n    }\r\n\r\n    // NO-PLAN: Users who never required payment or never had subscription\r\n    if (!paymentRequired) {\r\n      return 'no-plan';\r\n    }\r\n\r\n    // Users with paymentRequired = true (have or had a subscription)\r\n    if (paymentRequired) {\r\n      // Check if subscription has expired by date\r\n      if (subscriptionEndDate) {\r\n        const endDate = new Date(subscriptionEndDate);\r\n\r\n        if (endDate < now) {\r\n          // Subscription end date has passed - EXPIRED PLAN\r\n          return 'expired-plan';\r\n        } else {\r\n          // Subscription is still valid by date - ON PLAN\r\n          return 'on-plan';\r\n        }\r\n      } else {\r\n        // Has paymentRequired = true but no end date specified\r\n        // This could be a lifetime subscription or missing data\r\n        // Assume they are on plan if they have paymentRequired = true\r\n        return 'on-plan';\r\n      }\r\n    }\r\n\r\n    // Default fallback for edge cases\r\n    return 'no-plan';\r\n  };\r\n\r\n  const getUsersData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllUsers();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setUsers(response.users);\r\n        console.log(\"users loaded:\", response.users.length);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const blockUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await blockUserById({\r\n        studentId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteUserById({ studentId });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(\"User deleted successfully\");\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n\r\n  // Filter users based on search, status, and subscription\r\n  useEffect(() => {\r\n    let filtered = users;\r\n\r\n    // Filter by search query\r\n    if (searchQuery) {\r\n      filtered = filtered.filter(user =>\r\n        user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.school?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.class?.toLowerCase().includes(searchQuery.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Filter by status\r\n    if (filterStatus !== \"all\") {\r\n      filtered = filtered.filter(user => {\r\n        if (filterStatus === \"blocked\") return user.isBlocked;\r\n        if (filterStatus === \"active\") return !user.isBlocked;\r\n        return true;\r\n      });\r\n    }\r\n\r\n    // Filter by subscription plan\r\n    if (filterSubscription !== \"all\") {\r\n      filtered = filtered.filter(user => {\r\n        const subscriptionStatus = getSubscriptionFilterStatus(user);\r\n        return subscriptionStatus === filterSubscription;\r\n      });\r\n    }\r\n\r\n    setFilteredUsers(filtered);\r\n  }, [users, searchQuery, filterStatus, filterSubscription]);\r\n\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n\r\n  const UserCard = ({ user }) => {\r\n    const subscriptionStatus = getSubscriptionFilterStatus(user);\r\n\r\n    const getSubscriptionBadge = () => {\r\n      switch (subscriptionStatus) {\r\n        case 'on-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-green-100 text-green-800 flex items-center space-x-1\">\r\n              <TbCrown className=\"w-3 h-3\" />\r\n              <span>On Plan</span>\r\n            </span>\r\n          );\r\n        case 'expired-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-orange-100 text-orange-800 flex items-center space-x-1\">\r\n              <TbClock className=\"w-3 h-3\" />\r\n              <span>Expired</span>\r\n            </span>\r\n          );\r\n        case 'no-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-gray-100 text-gray-800 flex items-center space-x-1\">\r\n              <TbX className=\"w-3 h-3\" />\r\n              <span>No Plan</span>\r\n            </span>\r\n          );\r\n        default:\r\n          return null;\r\n      }\r\n    };\r\n\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return 'N/A';\r\n      return new Date(dateString).toLocaleDateString();\r\n    };\r\n\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        whileHover={{ y: -2 }}\r\n        transition={{ duration: 0.2 }}\r\n      >\r\n        <Card className=\"p-6 hover:shadow-large\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex items-start space-x-4\">\r\n              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${\r\n                user.isBlocked ? 'bg-error-100' : 'bg-primary-100'\r\n              }`}>\r\n                <TbUser className={`w-6 h-6 ${user.isBlocked ? 'text-error-600' : 'text-primary-600'}`} />\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"flex items-center space-x-2 mb-2\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">{user.name}</h3>\r\n                  <span className={`badge-modern ${\r\n                    user.isBlocked ? 'bg-error-100 text-error-800' : 'bg-success-100 text-success-800'\r\n                  }`}>\r\n                    {user.isBlocked ? 'Blocked' : 'Active'}\r\n                  </span>\r\n                  {getSubscriptionBadge()}\r\n                </div>\r\n\r\n                <div className=\"space-y-1 text-sm text-gray-600\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbMail className=\"w-4 h-4\" />\r\n                    <span>{user.email}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbSchool className=\"w-4 h-4\" />\r\n                    <span>{user.school || 'No school specified'}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbUsers className=\"w-4 h-4\" />\r\n                    <span>Class: {user.class || 'Not assigned'}</span>\r\n                  </div>\r\n\r\n                  {/* Subscription Details */}\r\n                  {user.subscriptionPlan && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbCrown className=\"w-4 h-4\" />\r\n                      <span>Plan: {user.subscriptionPlan}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Subscription Period */}\r\n                  {user.subscriptionStartDate && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span>Started: {formatDate(user.subscriptionStartDate)}</span>\r\n                    </div>\r\n                  )}\r\n                  {user.subscriptionEndDate && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span className={new Date(user.subscriptionEndDate) < new Date() ? 'text-red-600 font-medium' : 'text-green-600'}>\r\n                        {new Date(user.subscriptionEndDate) < new Date() ? 'Expired: ' : 'Expires: '}\r\n                        {formatDate(user.subscriptionEndDate)}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Payment Status */}\r\n                  {user.paymentRequired !== undefined && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbCrown className=\"w-4 h-4\" />\r\n                      <span className={user.paymentRequired ? 'text-blue-600' : 'text-gray-600'}>\r\n                        {user.paymentRequired ? 'Paid Subscription' : 'Free Account'}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Activity Information */}\r\n                  {user.totalQuizzesTaken > 0 && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbUser className=\"w-4 h-4\" />\r\n                      <span>Quizzes: {user.totalQuizzesTaken}</span>\r\n                    </div>\r\n                  )}\r\n                  {user.lastActivity && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span>Last Active: {formatDate(user.lastActivity)}</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Button\r\n                variant={user.isBlocked ? \"success\" : \"warning\"}\r\n                size=\"sm\"\r\n                onClick={() => blockUser(user.studentId)}\r\n                icon={user.isBlocked ? <TbUserCheck /> : <TbUserX />}\r\n              >\r\n                {user.isBlocked ? \"Unblock\" : \"Block\"}\r\n              </Button>\r\n\r\n              <Button\r\n                variant=\"error\"\r\n                size=\"sm\"\r\n                onClick={() => {\r\n                  if (window.confirm(\"Are you sure you want to delete this user?\")) {\r\n                    deleteUser(user.studentId);\r\n                  }\r\n                }}\r\n                icon={<TbTrash />}\r\n              >\r\n                Delete\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n      </motion.div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6\">\r\n      <div className=\"container-modern\">\r\n        {/* Modern Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"mb-8\"\r\n        >\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <div>\r\n              <h1 className=\"heading-2 text-gradient flex items-center\">\r\n                <TbUsers className=\"w-8 h-8 mr-3\" />\r\n                User Management\r\n              </h1>\r\n              <p className=\"text-gray-600 mt-2\">\r\n                Manage student accounts, permissions, and access controls\r\n              </p>\r\n            </div>\r\n\r\n            {/* Stats Cards */}\r\n            <div className=\"hidden lg:flex space-x-4\">\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-primary-600\">{users.length}</div>\r\n                <div className=\"text-sm text-gray-500\">Total Users</div>\r\n              </Card>\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-success-600\">\r\n                  {users.filter(u => !u.isBlocked).length}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">Active</div>\r\n              </Card>\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-green-600\">\r\n                  {users.filter(u => getSubscriptionFilterStatus(u) === 'on-plan').length}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">On Plan</div>\r\n              </Card>\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-orange-600\">\r\n                  {users.filter(u => getSubscriptionFilterStatus(u) === 'expired-plan').length}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">Expired</div>\r\n              </Card>\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-gray-600\">\r\n                  {users.filter(u => getSubscriptionFilterStatus(u) === 'no-plan').length}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">No Plan</div>\r\n              </Card>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Modern Filters */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"mb-8\"\r\n        >\r\n          <Card className=\"p-6\">\r\n            <div className=\"flex flex-col lg:flex-row gap-4 items-end\">\r\n              <div className=\"flex-1\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Search Users\r\n                </label>\r\n                <Input\r\n                  placeholder=\"Search by name, email, school, or class...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  icon={<TbSearch />}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"w-full lg:w-48\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Filter by Status\r\n                </label>\r\n                <select\r\n                  value={filterStatus}\r\n                  onChange={(e) => setFilterStatus(e.target.value)}\r\n                  className=\"input-modern\"\r\n                >\r\n                  <option value=\"all\">All Users</option>\r\n                  <option value=\"active\">Active Only</option>\r\n                  <option value=\"blocked\">Blocked Only</option>\r\n                </select>\r\n              </div>\r\n\r\n              <div className=\"w-full lg:w-48\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Filter by Plan\r\n                </label>\r\n                <select\r\n                  value={filterSubscription}\r\n                  onChange={(e) => setFilterSubscription(e.target.value)}\r\n                  className=\"input-modern\"\r\n                >\r\n                  <option value=\"all\">All Plans</option>\r\n                  <option value=\"on-plan\">On Plan</option>\r\n                  <option value=\"expired-plan\">Expired Plan</option>\r\n                  <option value=\"no-plan\">No Plan</option>\r\n                </select>\r\n              </div>\r\n\r\n              <Button\r\n                variant=\"secondary\"\r\n                icon={<TbFilter />}\r\n                onClick={() => {\r\n                  setSearchQuery(\"\");\r\n                  setFilterStatus(\"all\");\r\n                  setFilterSubscription(\"all\");\r\n                }}\r\n              >\r\n                Clear Filters\r\n              </Button>\r\n            </div>\r\n\r\n            {(searchQuery || filterStatus !== \"all\" || filterSubscription !== \"all\") && (\r\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\r\n                <span className=\"text-sm text-gray-600\">\r\n                  Showing {filteredUsers.length} of {users.length} users\r\n                  {filterSubscription !== \"all\" && (\r\n                    <span className=\"ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs\">\r\n                      {filterSubscription === 'on-plan' && 'On Plan'}\r\n                      {filterSubscription === 'expired-plan' && 'Expired Plan'}\r\n                      {filterSubscription === 'no-plan' && 'No Plan'}\r\n                    </span>\r\n                  )}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </Card>\r\n        </motion.div>\r\n\r\n        {/* Users Grid */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.4 }}\r\n        >\r\n          {loading ? (\r\n            <div className=\"flex justify-center py-12\">\r\n              <Loading text=\"Loading users...\" />\r\n            </div>\r\n          ) : filteredUsers.length > 0 ? (\r\n            <div className=\"space-y-4\">\r\n              {filteredUsers.map((user, index) => (\r\n                <motion.div\r\n                  key={user.studentId}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: index * 0.1 }}\r\n                >\r\n                  <UserCard user={user} />\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <Card className=\"p-12 text-center\">\r\n              <TbUsers className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No Users Found</h3>\r\n              <p className=\"text-gray-600\">\r\n                {searchQuery || filterStatus !== \"all\" || filterSubscription !== \"all\"\r\n                  ? \"Try adjusting your search or filter criteria\"\r\n                  : \"No users have been registered yet\"}\r\n              </p>\r\n            </Card>\r\n          )}\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Users;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js", ["654", "655", "656", "657", "658", "659", "660", "661"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js", ["662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js", ["680", "681", "682", "683", "684"], [], "import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowBigRightLinesFilled,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbStar,\r\n  TbSchool,\r\n  TbMenu2,\r\n  TbX,\r\n  TbMoon,\r\n  TbSun\r\n} from \"react-icons/tb\";\r\nimport { Rate, message } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReviews } from \"../../../apicalls/reviews\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport Image2 from \"../../../assets/collage-2.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport { useTheme } from \"../../../contexts/ThemeContext\";\r\nimport { Button } from \"../../../components/modern\";\r\n\r\nconst Home = () => {\r\n  const homeSectionRef = useRef(null);\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const [reviews, setReviews] = useState([]);\r\n  const dispatch = useDispatch();\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n  const { isDarkMode, toggleTheme } = useTheme();\r\n\r\n  useEffect(() => { getReviews(); }, []);\r\n\r\n  const getReviews = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getAllReviews();\r\n      if (response.success) {\r\n        setReviews(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    dispatch(HideLoading());\r\n  };\r\n\r\n  const scrollToSection = (ref, offset = 80) => {\r\n    if (ref?.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({ top: sectionTop - offset, behavior: \"smooth\" });\r\n      // Close mobile menu after clicking\r\n      setMenuOpen(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home\">\r\n      {/* Navigation */}\r\n      <motion.nav\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-header fixed w-full top-0 z-50\"\r\n      >\r\n        <div className=\"container\">\r\n          <div className=\"flex items-center justify-between h-16\">\r\n            {/* Left Section - Navigation */}\r\n            <div className=\"hidden md:flex items-center space-x-6\">\r\n              <button onClick={() => scrollToSection(homeSectionRef)} className=\"nav-item\">Home</button>\r\n              <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item\">Reviews</button>\r\n              <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item\">Contact Us</button>\r\n            </div>\r\n\r\n            {/* Center Section - Logo */}\r\n            <div className=\"flex-1 flex justify-center\">\r\n              <Link to=\"/\" className=\"flex items-center space-x-2 sm:space-x-3\">\r\n                {/* Tanzania Flag */}\r\n                <div className=\"w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full overflow-hidden shadow-lg border-2 border-white\">\r\n                  <div className=\"w-full h-full bg-gradient-to-b from-green-500 via-yellow-400 to-blue-500 relative\">\r\n                    <div className=\"absolute inset-0 bg-black/20\"></div>\r\n                    <div className=\"absolute top-0 left-0 w-full h-1/3 bg-green-500\"></div>\r\n                    <div className=\"absolute top-1/3 left-0 w-full h-1/3 bg-yellow-400\"></div>\r\n                    <div className=\"absolute bottom-0 left-0 w-full h-1/3 bg-blue-500\"></div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Brainwave Text - Centered */}\r\n                <h1 className=\"text-2xl sm:text-3xl md:text-4xl font-black tracking-tight\">\r\n                  <span className=\"text-black\">Brain</span>\r\n                  <span className=\"text-green-600\">wave</span>\r\n                </h1>\r\n\r\n                {/* Official Logo */}\r\n                <div className=\"w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full overflow-hidden shadow-lg border-2 border-white\">\r\n                  <img\r\n                    src=\"/favicon.png\"\r\n                    alt=\"Brainwave Logo\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    onError={(e) => {\r\n                      e.target.style.display = 'none';\r\n                      e.target.nextSibling.style.display = 'flex';\r\n                    }}\r\n                  />\r\n                  <div className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white text-sm sm:text-base md:text-lg\" style={{display: 'none'}}>\r\n                    🧠\r\n                  </div>\r\n                </div>\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Right Section */}\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* Theme Toggle */}\r\n              <button\r\n                onClick={toggleTheme}\r\n                className=\"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n              >\r\n                {isDarkMode ? <TbSun className=\"w-5 h-5\" /> : <TbMoon className=\"w-5 h-5\" />}\r\n              </button>\r\n\r\n              {/* Mobile Menu Button */}\r\n              <button\r\n                onClick={() => setMenuOpen(!menuOpen)}\r\n                className=\"md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n              >\r\n                {menuOpen ? <TbX className=\"w-6 h-6\" /> : <TbMenu2 className=\"w-6 h-6\" />}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          {menuOpen && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              className=\"md:hidden mobile-nav\"\r\n            >\r\n              <div className=\"flex flex-col\">\r\n                <button onClick={() => scrollToSection(homeSectionRef)} className=\"nav-item\">Home</button>\r\n                <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item\">Reviews</button>\r\n                <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item\">Contact Us</button>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </div>\r\n      </motion.nav>\r\n\r\n      {/* Hero Section */}\r\n      <section ref={homeSectionRef} className=\"hero-section\">\r\n        <div className=\"container\">\r\n          <div className=\"hero-grid\">\r\n            {/* Hero Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"hero-content\"\r\n            >\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"hero-badge\"\r\n              >\r\n                <TbSchool className=\"w-5 h-5 mr-2\" />\r\n                #1 Educational Platform in Tanzania\r\n              </motion.div>\r\n\r\n              <h1 className=\"hero-title\">\r\n                Fueling Bright Futures with{\" \"}\r\n                <span className=\"text-gradient\">\r\n                  Education\r\n                  <TbArrowBigRightLinesFilled className=\"inline w-8 h-8 ml-2\" />\r\n                </span>\r\n              </h1>\r\n\r\n              <p className=\"hero-subtitle\">\r\n                Discover limitless learning opportunities with our comprehensive\r\n                online study platform. Study anywhere, anytime, and achieve your\r\n                academic goals with confidence.\r\n              </p>\r\n\r\n              {/* Explore Platform Button */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"cta-section\"\r\n              >\r\n                <Link to=\"/login\">\r\n                  <button className=\"btn btn-primary btn-large\">\r\n                    <TbBrain className=\"w-5 h-5 mr-2\" />\r\n                    Explore Platform\r\n                  </button>\r\n                </Link>\r\n              </motion.div>\r\n\r\n              {/* Trust Indicators */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"trust-indicators\"\r\n              >\r\n                <div className=\"trust-indicator\">\r\n                  <TbUsers style={{color: '#007BFF'}} />\r\n                  <span>15K+ Students</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbStar style={{color: '#f59e0b'}} />\r\n                  <span>4.9/5 Rating</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbTrophy style={{color: '#007BFF'}} />\r\n                  <span>Award Winning</span>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              className=\"hero-image\"\r\n            >\r\n              <div className=\"relative\">\r\n                <img\r\n                  src={Image1}\r\n                  alt=\"Students Learning\"\r\n                />\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{top: '-1rem', left: '-1rem'}}\r\n                >\r\n                  <TbBook style={{color: '#007BFF'}} />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{bottom: '-1rem', right: '-1rem'}}\r\n                >\r\n                  <TbTrophy style={{color: '#f59e0b'}} />\r\n                </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"stats-section\">\r\n        <div className=\"container\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"stats-grid\"\r\n          >\r\n            {[\r\n              { number: \"15K+\", text: \"Active Students\" },\r\n              { number: \"500+\", text: \"Expert Teachers\" },\r\n              { number: \"1000+\", text: \"Video Lessons\" },\r\n              { number: \"98%\", text: \"Success Rate\" }\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"stat-item\"\r\n              >\r\n                <div className=\"stat-number\">{stat.number}</div>\r\n                <p className=\"stat-text\">{stat.text}</p>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Reviews Section */}\r\n      <section ref={reviewsSectionRef} className=\"reviews-section\">\r\n        <div className=\"reviews-container\">\r\n          <h2 className=\"reviews-title\">\r\n            Reviews from our students\r\n          </h2>\r\n          <div className=\"reviews-grid\">\r\n            {[\r\n              {\r\n                rating: 5,\r\n                text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\r\n                user: { name: \"Sarah Johnson\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\r\n                user: { name: \"Michael Chen\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\r\n                user: { name: \"Amina Hassan\" }\r\n              }\r\n            ].map((review, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"review-card\"\r\n              >\r\n                <div className=\"review-rating\">\r\n                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>\r\n                    {'★'.repeat(review.rating)}\r\n                  </div>\r\n                </div>\r\n                <div className=\"review-text\">\"{review.text}\"</div>\r\n                <div className=\"review-divider\"></div>\r\n                <div className=\"review-author\">{review.user?.name}</div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section ref={contactUsRef} className=\"contact-section\">\r\n        <div className=\"contact-container\">\r\n          <h2 className=\"contact-title\">Contact Us</h2>\r\n          <p className=\"contact-subtitle\">Get in touch with us for any questions or support</p>\r\n          <form className=\"contact-form\" onSubmit={handleSubmit}>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Name</label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Your Name\"\r\n                className=\"form-input\"\r\n                value={formData.name}\r\n                onChange={handleChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Email</label>\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Your Email\"\r\n                className=\"form-input\"\r\n                value={formData.email}\r\n                onChange={handleChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Message</label>\r\n              <textarea\r\n                name=\"message\"\r\n                placeholder=\"Your Message\"\r\n                className=\"form-input form-textarea\"\r\n                value={formData.message}\r\n                onChange={handleChange}\r\n                required\r\n              ></textarea>\r\n            </div>\r\n            <button\r\n              type=\"submit\"\r\n              className=\"form-submit\"\r\n              disabled={loading}\r\n            >\r\n              {loading ? \"Sending...\" : \"Send Message\"}\r\n            </button>\r\n            {responseMessage && (\r\n              <p className=\"response-message\" style={{ marginTop: '1rem', textAlign: 'center', color: '#10b981' }}>\r\n                {responseMessage}\r\n              </p>\r\n            )}\r\n          </form>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"footer\">\r\n        <div className=\"footer-content\">\r\n          <p className=\"footer-text\">\r\n            © 2024 BrainWave Educational Platform. All rights reserved.\r\n          </p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Chat\\index.jsx", ["685", "686"], [], "import React, { useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport \"./index.css\"; // Import the custom CSS\r\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nfunction ChatGPTIntegration() {\r\n  const [messages, setMessages] = useState([]);\r\n  const [prompt, setPrompt] = useState(\"\");\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const handleChat = async () => {\r\n    if (!prompt.trim() && !imageFile) return;\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      // Step 1: Upload the image to the server (if an image is selected)\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n\r\n        const data = await uploadImg(formData);\r\n\r\n        if (data?.success) {\r\n          imageUrl = data.url; // Extract the S3 URL\r\n          console.log(\"Image URL: \", imageUrl);\r\n        } else {\r\n          throw new Error(\"Image upload failed\");\r\n        }\r\n      }\r\n\r\n      // Step 2: Construct the ChatGPT message payload\r\n      const userMessage = imageUrl\r\n        ? {\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: prompt },\r\n            { type: \"image_url\", image_url: { url: imageUrl } },\r\n          ],\r\n        }\r\n        : { role: \"user\", content: prompt };\r\n\r\n      const updatedMessages = [...messages, userMessage];\r\n      setMessages(updatedMessages);\r\n      setPrompt(\"\");\r\n\r\n      // Step 3: Send the payload to ChatGPT\r\n      const chatPayload = { messages: updatedMessages };\r\n\r\n      const chatRes = await chatWithChatGPT(chatPayload);\r\n\r\n      const apiResponse = chatRes?.data;\r\n      console.log(\"API Response: \", apiResponse);\r\n\r\n      // Step 4: Append the assistant's response to the conversation\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: apiResponse },\r\n      ]);\r\n\r\n      setImageFile(null);\r\n    } catch (error) {\r\n      console.error(\"Error during chat:\", error);\r\n      alert(\"An error occurred while processing your request. Please try again.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (event) => {\r\n    if (event.key === \"Enter\") {\r\n      handleChat(); // Trigger the handleChat function on Enter key\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"chat-container\">\r\n      {/* Chat messages */}\r\n      <div className=\"chat-messages\">\r\n        {messages.map((msg, index) => (\r\n          <div\r\n            key={index}\r\n            className={`message ${msg.role === \"user\" ? \"user-message\" : \"assistant-message\"\r\n              }`}\r\n          >\r\n            <>\r\n              {msg.role === \"assistant\" ? (\r\n                <>\r\n                  {msg?.content ? (\r\n                    <ContentRenderer text={msg.content} />\r\n                  ) : (\r\n                    <p>Unable to get a response from AI</p>\r\n                  )}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {typeof msg.content === \"string\"\r\n                    ? msg.content\r\n                    : msg.content.map((item, idx) =>\r\n                      item.type === \"text\" ? (\r\n                        <p key={idx}>{item.text}</p>\r\n                      ) : (\r\n                        <img\r\n                          key={idx}\r\n                          src={item.image_url.url}\r\n                          alt=\"User content\"\r\n                          style={{ height: \"100px\" }}\r\n                        />\r\n                      )\r\n                    )}\r\n                </>\r\n              )}\r\n            </>\r\n          </div>\r\n        ))}\r\n        {isLoading && <div className=\"loading-indicator\">Loading...</div>}\r\n      </div>\r\n\r\n      {/* Input and upload */}\r\n      <div className=\"chat-input-container\">\r\n        <textarea\r\n          className=\"chat-input\"\r\n          placeholder=\"Type your message here...\"\r\n          value={prompt}\r\n          onChange={(e) => setPrompt(e.target.value)}\r\n        ></textarea>\r\n        <input\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={(e) => setImageFile(e.target.files[0])}\r\n          style={{ width: \"200px\", borderRadius: \"5px\", marginRight: \"10px\" }}\r\n        />\r\n        <button\r\n          disabled={isLoading}\r\n          className=\"send-button\"\r\n          onClick={handleChat}\r\n        >\r\n          Send\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatGPTIntegration;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js", ["687", "688", "689"], [], "import React from 'react';\r\nimport { InlineMath, BlockMath } from 'react-katex';\r\nimport 'katex/dist/katex.min.css';\r\n\r\nconst ContentRenderer = ({ text }) => {\r\n    // Handle undefined, null, or empty text\r\n    if (!text || typeof text !== 'string') {\r\n        return <div></div>;\r\n    }\r\n\r\n    const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\r\n    const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\r\n    // const boldTextRegex = /(?:\\*\\*.*?\\*\\*)/g;\r\n    const boldTextRegex = /\\*\\*.*?\\*\\*/g;\r\n    // console.log('Text: ', text);\r\n    let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\r\n    const lines = modifiedText.split('\\n');\r\n    // console.log('Lines with symbol: ', lines);\r\n    const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n    // console.log('Lines: ', restoredLines);\r\n\r\n\r\n\r\n\r\n    const inlineMathSymbol = \"~~INLINEMATH~~\";\r\n    const blockMathSymbol = \"~~BLOCKMATH~~\";\r\n    const boldSymbol = \"~~BOLD~~\";\r\n\r\n    let newModifiedText = text.replace(blockMathRegex, match => {\r\n        return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\r\n        return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(boldTextRegex, match => {\r\n        // console.log('Bold Part: ', match);\r\n        return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\r\n    });\r\n\r\n    const newLines = newModifiedText.split('\\n');\r\n\r\n    const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n\r\n    // console.log('New Modified Text: ', newModifiedText);\r\n\r\n    const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\r\n\r\n    // Debug logging removed to prevent React rendering issues\r\n\r\n    return (\r\n        <div>\r\n            {newRestoredLines.map((line, lineIndex) => (\r\n                <div key={lineIndex}>\r\n                    {line.trim() === '' ?\r\n                        <br key={`br-${lineIndex}`} />\r\n                        :\r\n                        line.split(newRegex).map((part, index) => {\r\n                            if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\r\n                                return (\r\n                                    <React.Fragment key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\r\n                                            if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\r\n                                                return (\r\n                                                    <InlineMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                                    </InlineMath>\r\n                                                );\r\n                                            } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\r\n                                                return (\r\n                                                    <BlockMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                                    </BlockMath>\r\n                                                );\r\n                                            } else {\r\n                                                return (\r\n                                                    <span key={`${lineIndex}-${index}-${n_index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                                        <strong>{nestedPart}</strong>\r\n                                                    </span>\r\n                                                );\r\n                                            }\r\n                                        })}\r\n                                    </React.Fragment>\r\n                                );\r\n                            } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\r\n                                return (\r\n                                    <InlineMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                    </InlineMath>\r\n                                );\r\n                            } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\r\n                                return (\r\n                                    <BlockMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                    </BlockMath>\r\n                                );\r\n                            } else {\r\n                                return (\r\n                                    <span key={`${lineIndex}-${index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                        {part}\r\n                                    </span>\r\n                                );\r\n                            }\r\n                        })}\r\n                </div>\r\n            ))}\r\n        </div>\r\n\r\n    )\r\n};\r\n\r\nexport default ContentRenderer;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js", ["690"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js", [], ["691"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\WaitingModal.jsx", ["692", "693"], [], "import React, { useEffect, useState } from \"react\";\r\nimport Mo<PERSON> from \"react-modal\";\r\nimport \"./WaitingModal.css\";\r\n\r\nModal.setAppElement(\"#root\"); // Ensure accessibility for screen readers\r\n\r\nconst WaitingModal = ({ isOpen, onClose }) => {\r\n    return (\r\n        <Modal\r\n            isOpen={isOpen}\r\n            onRequestClose={onClose}\r\n            className=\"waiting-modal-content\"\r\n            overlayClassName=\"waiting-modal-overlay\"\r\n        >\r\n            <div className=\"waiting-modal-header\">\r\n                <h2>Please confirm the payment</h2>\r\n            </div>\r\n            <div className=\"waiting-modal-timer\">\r\n                <svg\r\n                    fill=\"#253864\"\r\n                    version=\"1.1\"\r\n                    id=\"Layer_1\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    viewBox=\"0 0 512 512\"\r\n                    width=\"64px\"\r\n                    height=\"64px\"\r\n                    stroke=\"#253864\"\r\n                >\r\n                    <g>\r\n                        <path d=\"M437.019,74.981C388.668,26.629,324.38,0,256,0S123.332,26.629,74.981,74.981C26.629,123.332,0,187.62,0,256 s26.629,132.668,74.981,181.019C123.332,485.371,187.62,512,256,512c64.518,0,126.15-24.077,173.541-67.796l-10.312-11.178 c-44.574,41.12-102.544,63.766-163.229,63.766c-64.317,0-124.786-25.046-170.266-70.527 C40.254,380.786,15.208,320.317,15.208,256S40.254,131.214,85.734,85.735C131.214,40.254,191.683,15.208,256,15.208 s124.786,25.046,170.266,70.527c45.48,45.479,70.526,105.948,70.526,170.265c0,60.594-22.587,118.498-63.599,163.045 l11.188,10.301C487.986,381.983,512,320.421,512,256C512,187.62,485.371,123.332,437.019,74.981z\"></path>\r\n                        <path d=\"M282.819,263.604h63.415v-15.208h-63.415c-1.619-5.701-5.007-10.662-9.536-14.25l35.913-86.701l-14.049-5.82 l-35.908,86.688c-1.064-0.124-2.142-0.194-3.238-0.194c-15.374,0-27.881,12.508-27.881,27.881s12.507,27.881,27.881,27.881 C268.737,283.881,279.499,275.292,282.819,263.604z M243.327,256c0-6.989,5.685-12.673,12.673-12.673 c6.989,0,12.673,5.685,12.673,12.673c0,6.989-5.685,12.673-12.673,12.673C249.011,268.673,243.327,262.989,243.327,256z\"></path>\r\n                        <path d=\"M451.168,256c0-107.616-87.552-195.168-195.168-195.168S60.832,148.384,60.832,256S148.384,451.168,256,451.168 S451.168,363.616,451.168,256z M76.04,256c0-99.231,80.73-179.96,179.96-179.96S435.96,156.769,435.96,256 S355.231,435.96,256,435.96S76.04,355.231,76.04,256z\"></path>\r\n                    </g>\r\n                </svg>\r\n            </div>\r\n\r\n            <p className=\"waiting-modal-footer\">\r\n                Ensure that your payment is confirmed before the timer runs out.\r\n            </p>\r\n        </Modal>\r\n    );\r\n};\r\n\r\nexport default WaitingModal;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\ConfirmModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js", ["694", "695", "696", "697"], [], "import React, { useState, useEffect } from \"react\";\nimport { Table, Button, Space, Select, Input, message, Modal, Tag, Tooltip } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { \n  getAllStudyMaterials, \n  deleteVideo, \n  deleteNote, \n  deletePastPaper, \n  deleteBook \n} from \"../../../apicalls/study\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaEdit,\n  FaTrash,\n  FaEye,\n  FaFilter,\n  FaSearch\n} from \"react-icons/fa\";\nimport \"./StudyMaterialManager.css\";\n\nconst { Option } = Select;\nconst { Search } = Input;\n\nfunction StudyMaterialManager({ onEdit }) {\n  const dispatch = useDispatch();\n  const [materials, setMaterials] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filters, setFilters] = useState({\n    materialType: \"\",\n    level: \"\",\n    className: \"\",\n    subject: \"\"\n  });\n  const [searchText, setSearchText] = useState(\"\");\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\"];\n      case \"secondary\":\n        return [\"6\", \"7\", \"8\", \"9\", \"10\", \"11\"];\n      case \"advance\":\n        return [\"12\", \"13\"];\n      default:\n        return [];\n    }\n  };\n\n  // Fetch materials\n  const fetchMaterials = async () => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n      \n      const response = await getAllStudyMaterials(filters);\n      \n      if (response.status === 200 && response.data.success) {\n        setMaterials(response.data.data || []);\n      } else {\n        message.error(\"Failed to fetch study materials\");\n        setMaterials([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching materials:\", error);\n      message.error(\"Failed to fetch study materials\");\n      setMaterials([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  useEffect(() => {\n    fetchMaterials();\n  }, [filters]);\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => {\n      const newFilters = { ...prev, [key]: value };\n      \n      // Reset dependent filters\n      if (key === \"level\") {\n        newFilters.className = \"\";\n        newFilters.subject = \"\";\n      }\n      \n      return newFilters;\n    });\n  };\n\n  // Handle delete\n  const handleDelete = async (material) => {\n    Modal.confirm({\n      title: `Delete ${material.type.replace(\"-\", \" \")}`,\n      content: `Are you sure you want to delete \"${material.title}\"? This action cannot be undone.`,\n      okText: \"Delete\",\n      okType: \"danger\",\n      cancelText: \"Cancel\",\n      onOk: async () => {\n        try {\n          dispatch(ShowLoading());\n          \n          let response;\n          switch (material.type) {\n            case \"videos\":\n              response = await deleteVideo(material._id);\n              break;\n            case \"study-notes\":\n              response = await deleteNote(material._id);\n              break;\n            case \"past-papers\":\n              response = await deletePastPaper(material._id);\n              break;\n            case \"books\":\n              response = await deleteBook(material._id);\n              break;\n            default:\n              throw new Error(\"Invalid material type\");\n          }\n\n          if (response.status === 200 && response.data.success) {\n            message.success(response.data.message);\n            fetchMaterials(); // Refresh the list\n          } else {\n            message.error(response.data?.message || \"Failed to delete material\");\n          }\n        } catch (error) {\n          console.error(\"Error deleting material:\", error);\n          message.error(\"Failed to delete material\");\n        } finally {\n          dispatch(HideLoading());\n        }\n      }\n    });\n  };\n\n  // Get material type icon\n  const getMaterialIcon = (type) => {\n    switch (type) {\n      case \"videos\":\n        return <FaVideo className=\"material-icon video\" />;\n      case \"study-notes\":\n        return <FaFileAlt className=\"material-icon note\" />;\n      case \"past-papers\":\n        return <FaGraduationCap className=\"material-icon paper\" />;\n      case \"books\":\n        return <FaBook className=\"material-icon book\" />;\n      default:\n        return <FaFileAlt className=\"material-icon\" />;\n    }\n  };\n\n  // Get material type label\n  const getMaterialTypeLabel = (type) => {\n    switch (type) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return type;\n    }\n  };\n\n  // Filter materials based on search text\n  const filteredMaterials = materials.filter(material =>\n    material.title.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.subject.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.className.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // Table columns\n  const columns = [\n    {\n      title: \"Material\",\n      key: \"material\",\n      width: \"30%\",\n      render: (_, record) => (\n        <div className=\"material-info\">\n          <div className=\"material-header\">\n            {getMaterialIcon(record.type)}\n            <div className=\"material-details\">\n              <div className=\"material-title\">{record.title}</div>\n              <div className=\"material-meta\">\n                <Tag color=\"blue\">{getMaterialTypeLabel(record.type)}</Tag>\n                <span className=\"meta-text\">{record.subject} • Class {record.className}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: \"Level\",\n      dataIndex: \"level\",\n      key: \"level\",\n      width: \"10%\",\n      render: (level) => (\n        <Tag color={level === \"primary\" ? \"green\" : level === \"secondary\" ? \"orange\" : \"purple\"}>\n          {level.charAt(0).toUpperCase() + level.slice(1)}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Class\",\n      dataIndex: \"className\",\n      key: \"className\",\n      width: \"10%\",\n      render: (className) => <span className=\"class-badge\">Class {className}</span>,\n    },\n    {\n      title: \"Subject\",\n      dataIndex: \"subject\",\n      key: \"subject\",\n      width: \"15%\",\n    },\n    {\n      title: \"Year\",\n      dataIndex: \"year\",\n      key: \"year\",\n      width: \"10%\",\n      render: (year) => year || \"-\",\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      width: \"25%\",\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"Edit material\">\n            <Button\n              type=\"primary\"\n              icon={<FaEdit />}\n              size=\"small\"\n              onClick={() => onEdit(record)}\n            >\n              Edit\n            </Button>\n          </Tooltip>\n          \n          <Tooltip title=\"Delete material\">\n            <Button\n              danger\n              icon={<FaTrash />}\n              size=\"small\"\n              onClick={() => handleDelete(record)}\n            >\n              Delete\n            </Button>\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"study-material-manager\">\n      <div className=\"manager-header\">\n        <h2>Study Materials Management</h2>\n        <p>Manage all uploaded study materials - edit, delete, and organize content</p>\n      </div>\n\n      {/* Filters */}\n      <div className=\"filters-section\">\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label>Material Type:</label>\n            <Select\n              placeholder=\"All Types\"\n              value={filters.materialType || undefined}\n              onChange={(value) => handleFilterChange(\"materialType\", value)}\n              allowClear\n              style={{ width: 150 }}\n            >\n              <Option value=\"videos\">Videos</Option>\n              <Option value=\"study-notes\">Study Notes</Option>\n              <Option value=\"past-papers\">Past Papers</Option>\n              <Option value=\"books\">Books</Option>\n            </Select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label>Level:</label>\n            <Select\n              placeholder=\"All Levels\"\n              value={filters.level || undefined}\n              onChange={(value) => handleFilterChange(\"level\", value)}\n              allowClear\n              style={{ width: 120 }}\n            >\n              <Option value=\"primary\">Primary</Option>\n              <Option value=\"secondary\">Secondary</Option>\n              <Option value=\"advance\">Advance</Option>\n            </Select>\n          </div>\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Class:</label>\n              <Select\n                placeholder=\"All Classes\"\n                value={filters.className || undefined}\n                onChange={(value) => handleFilterChange(\"className\", value)}\n                allowClear\n                style={{ width: 120 }}\n              >\n                {getClassesForLevel(filters.level).map(cls => (\n                  <Option key={cls} value={cls}>Class {cls}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Subject:</label>\n              <Select\n                placeholder=\"All Subjects\"\n                value={filters.subject || undefined}\n                onChange={(value) => handleFilterChange(\"subject\", value)}\n                allowClear\n                style={{ width: 150 }}\n              >\n                {getSubjectsForLevel(filters.level).map(subject => (\n                  <Option key={subject} value={subject}>{subject}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          <div className=\"filter-group\">\n            <label>Search:</label>\n            <Search\n              placeholder=\"Search materials...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              style={{ width: 200 }}\n              allowClear\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Materials Table */}\n      <div className=\"materials-table\">\n        <Table\n          columns={columns}\n          dataSource={filteredMaterials}\n          rowKey=\"_id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `Total ${total} materials`,\n          }}\n          scroll={{ x: 1000 }}\n        />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterialManager;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\index.js", ["698", "699", "700"], [], "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport { Card, Button, Row, Col, Statistic, Table, Tag, Space, message } from \"antd\";\nimport { \n  FaRobot, \n  FaQuestionCircle, \n  FaHistory, \n  FaCog, \n  FaPlus,\n  FaEye,\n  FaCheck,\n  FaTimes\n} from \"react-icons/fa\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getGenerationHistory } from \"../../../apicalls/aiQuestions\";\nimport QuestionGenerationForm from \"./QuestionGenerationForm\";\nimport QuestionPreview from \"./QuestionPreview\";\nimport \"./AIQuestionGeneration.css\";\n\nfunction AIQuestionGeneration() {\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [activeView, setActiveView] = useState(\"dashboard\");\n  const [generationHistory, setGenerationHistory] = useState([]);\n  const [selectedGeneration, setSelectedGeneration] = useState(null);\n  const [stats, setStats] = useState({\n    totalGenerations: 0,\n    totalQuestions: 0,\n    approvedQuestions: 0,\n    pendingReview: 0,\n  });\n\n  useEffect(() => {\n    fetchGenerationHistory();\n  }, []);\n\n  const fetchGenerationHistory = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getGenerationHistory({ limit: 20 });\n      if (response.success) {\n        setGenerationHistory(response.data.generations);\n        calculateStats(response.data.generations);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to fetch generation history\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const calculateStats = (generations) => {\n    const stats = generations.reduce((acc, gen) => {\n      acc.totalGenerations += 1;\n      acc.totalQuestions += gen.generatedQuestions.length;\n      acc.approvedQuestions += gen.generatedQuestions.filter(q => q.approved).length;\n      acc.pendingReview += gen.generationStatus === \"completed\" ? 1 : 0;\n      return acc;\n    }, {\n      totalGenerations: 0,\n      totalQuestions: 0,\n      approvedQuestions: 0,\n      pendingReview: 0,\n    });\n    setStats(stats);\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      pending: \"orange\",\n      in_progress: \"blue\",\n      completed: \"green\",\n      failed: \"red\",\n      cancelled: \"gray\",\n    };\n    return colors[status] || \"default\";\n  };\n\n  const historyColumns = [\n    {\n      title: \"Generation ID\",\n      dataIndex: \"_id\",\n      key: \"_id\",\n      render: (id) => id.slice(-8),\n    },\n    {\n      title: \"Exam\",\n      dataIndex: [\"examId\", \"name\"],\n      key: \"examName\",\n    },\n    {\n      title: \"Questions\",\n      dataIndex: \"generatedQuestions\",\n      key: \"questionCount\",\n      render: (questions) => questions.length,\n    },\n    {\n      title: \"Status\",\n      dataIndex: \"generationStatus\",\n      key: \"status\",\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {status.toUpperCase()}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Created\",\n      dataIndex: \"createdAt\",\n      key: \"createdAt\",\n      render: (date) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            icon={<FaEye />}\n            onClick={() => {\n              setSelectedGeneration(record);\n              setActiveView(\"preview\");\n            }}\n          >\n            Preview\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  const renderDashboard = () => (\n    <div className=\"ai-question-dashboard\">\n      <Row gutter={[16, 16]} className=\"mb-4\">\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Total Generations\"\n              value={stats.totalGenerations}\n              prefix={<FaRobot />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Questions Generated\"\n              value={stats.totalQuestions}\n              prefix={<FaQuestionCircle />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Approved Questions\"\n              value={stats.approvedQuestions}\n              prefix={<FaCheck />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Pending Review\"\n              value={stats.pendingReview}\n              prefix={<FaTimes />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]}>\n        <Col xs={24} md={12}>\n          <Card\n            title=\"Generate New Questions\"\n            className=\"action-card\"\n            actions={[\n              <Button\n                type=\"primary\"\n                icon={<FaPlus />}\n                onClick={() => setActiveView(\"generate\")}\n              >\n                Start Generation\n              </Button>\n            ]}\n          >\n            <p>Create AI-generated questions for your exams using advanced language models.</p>\n            <ul>\n              <li>Multiple choice questions</li>\n              <li>Fill in the blank questions</li>\n              <li>Picture-based questions</li>\n              <li>Tanzania syllabus compliant</li>\n            </ul>\n          </Card>\n        </Col>\n        <Col xs={24} md={12}>\n          <Card\n            title=\"Generation History\"\n            className=\"action-card\"\n            actions={[\n              <Button\n                icon={<FaHistory />}\n                onClick={() => setActiveView(\"history\")}\n              >\n                View History\n              </Button>\n            ]}\n          >\n            <p>Review and manage your previous question generations.</p>\n            <ul>\n              <li>Track generation status</li>\n              <li>Preview generated questions</li>\n              <li>Approve or reject questions</li>\n              <li>Add approved questions to exams</li>\n            </ul>\n          </Card>\n        </Col>\n      </Row>\n\n      <Card title=\"Recent Generations\" className=\"mt-4\">\n        <Table\n          dataSource={generationHistory.slice(0, 5)}\n          columns={historyColumns}\n          pagination={false}\n          rowKey=\"_id\"\n        />\n        {generationHistory.length > 5 && (\n          <div className=\"text-center mt-3\">\n            <Button onClick={() => setActiveView(\"history\")}>\n              View All Generations\n            </Button>\n          </div>\n        )}\n      </Card>\n    </div>\n  );\n\n  const renderHistory = () => (\n    <Card title=\"Generation History\">\n      <Table\n        dataSource={generationHistory}\n        columns={historyColumns}\n        pagination={{\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n        }}\n        rowKey=\"_id\"\n      />\n    </Card>\n  );\n\n  const renderContent = () => {\n    switch (activeView) {\n      case \"generate\":\n        return (\n          <QuestionGenerationForm\n            onBack={() => setActiveView(\"dashboard\")}\n            onSuccess={() => {\n              setActiveView(\"dashboard\");\n              fetchGenerationHistory();\n            }}\n          />\n        );\n      case \"preview\":\n        return (\n          <QuestionPreview\n            generation={selectedGeneration}\n            onBack={() => setActiveView(\"dashboard\")}\n            onSuccess={() => {\n              setActiveView(\"dashboard\");\n              fetchGenerationHistory();\n            }}\n          />\n        );\n      case \"history\":\n        return renderHistory();\n      default:\n        return renderDashboard();\n    }\n  };\n\n  return (\n    <div className=\"ai-question-generation\">\n      <PageTitle title=\"AI Question Generation\" />\n      \n      {activeView === \"dashboard\" && (\n        <div className=\"page-header\">\n          <h2>AI Question Generation Dashboard</h2>\n          <p>Generate high-quality questions using artificial intelligence</p>\n        </div>\n      )}\n\n      {renderContent()}\n    </div>\n  );\n}\n\nexport default AIQuestionGeneration;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionGenerationForm.js", ["701"], [], "import React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport {\n  Card,\n  Form,\n  Select,\n  InputNumber,\n  Button,\n  Row,\n  Col,\n  Checkbox,\n  message,\n  Divider,\n  Alert,\n  Progress\n} from \"antd\";\nimport { FaArrowLeft, FaRobot } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllExams } from \"../../../apicalls/exams\";\nimport {\n  generateQuestions,\n  getSubjectsForLevel,\n  getSyllabusTopics\n} from \"../../../apicalls/aiQuestions\";\nimport AutoGenerateExamModal from \"./AutoGenerateExamModal\";\nimport AILoginModal from \"../../../components/AILoginModal\";\nimport { useAIAuth } from \"../../../hooks/useAIAuth\";\n\nconst { Option } = Select;\n\nfunction QuestionGenerationForm({ onBack, onSuccess }) {\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n\n  // Enhanced authentication\n  const {\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    loading: authLoading,\n    requiresUpgrade,\n    needsLogin,\n    handleLoginSuccess,\n    requireAIAuth,\n    sessionExpiringSoon,\n    timeUntilExpiry\n  } = useAIAuth();\n\n  const [exams, setExams] = useState([]);\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [availableTopics, setAvailableTopics] = useState([]);\n  const [selectedLevel, setSelectedLevel] = useState(\"\");\n  const [selectedClass, setSelectedClass] = useState(\"\");\n  const [selectedSubjects, setSelectedSubjects] = useState([]);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationProgress, setGenerationProgress] = useState(0);\n  const [showAutoGenerateModal, setShowAutoGenerateModal] = useState(false);\n  const [showLoginModal, setShowLoginModal] = useState(false);\n\n  const fetchExams = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      if (response.success && Array.isArray(response.data)) {\n        // Filter out any null or undefined values\n        const validExams = response.data.filter(exam => exam && exam._id);\n        setExams(validExams);\n      } else {\n        message.error(\"Failed to fetch exams\");\n        setExams([]); // Ensure exams is always an array\n      }\n    } catch (error) {\n      message.error(\"Error fetching exams\");\n      setExams([]); // Ensure exams is always an array\n    } finally {\n      dispatch(HideLoading());\n    }\n  }, [dispatch]);\n\n  useEffect(() => {\n    // Only fetch exams if we have authentication, otherwise let the auth hook handle it\n    if (isAuthenticated && hasAIAccess && !authLoading) {\n      fetchExams();\n    }\n  }, [isAuthenticated, hasAIAccess, authLoading, fetchExams]);\n\n  const handleLevelChange = async (level) => {\n    setSelectedLevel(level);\n    setSelectedClass(\"\");\n    setSelectedSubjects([]);\n    setAvailableTopics([]);\n    form.setFieldsValue({\n      class: undefined,\n      subjects: [],\n      syllabusTopics: []\n    });\n\n    try {\n      const response = await getSubjectsForLevel(level);\n      if (response.success) {\n        setAvailableSubjects(response.data);\n      } else {\n        message.error(\"Failed to fetch subjects\");\n      }\n    } catch (error) {\n      message.error(\"Error fetching subjects\");\n    }\n  };\n\n  const handleClassChange = (className) => {\n    setSelectedClass(className);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If subjects are already selected, fetch topics\n    if (selectedSubjects.length > 0) {\n      fetchTopicsForSubjects(selectedLevel, className, selectedSubjects);\n    }\n  };\n\n  const handleSubjectsChange = (subjects) => {\n    setSelectedSubjects(subjects);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If class is selected, fetch topics\n    if (selectedClass) {\n      fetchTopicsForSubjects(selectedLevel, selectedClass, subjects);\n    }\n\n    // Note: Auto-generate exam functionality moved to modal\n  };\n\n  const fetchTopicsForSubjects = async (level, className, subjects) => {\n    if (!level || !className || subjects.length === 0) return;\n\n    try {\n      const allTopics = [];\n\n      for (const subject of subjects) {\n        const response = await getSyllabusTopics(level, className, subject);\n        if (response.success) {\n          const subjectTopics = response.data.topics.map(topic => ({\n            ...topic,\n            subject: subject,\n            fullName: `${subject}: ${topic.topicName}`,\n          }));\n          allTopics.push(...subjectTopics);\n        }\n      }\n\n      setAvailableTopics(allTopics);\n    } catch (error) {\n      console.error(\"Error fetching topics:\", error);\n      message.error(\"Failed to fetch syllabus topics\");\n    }\n  };\n\n  const handleAutoGenerateExamSuccess = (newExam) => {\n    // Add the new exam to the list and select it\n    if (newExam && newExam._id) {\n      const updatedExams = [...exams, newExam];\n      setExams(updatedExams);\n      form.setFieldsValue({ examId: newExam._id });\n      setShowAutoGenerateModal(false);\n      message.success(`Exam created successfully: ${newExam.name}`);\n    } else {\n      message.error(\"Invalid exam data received\");\n      setShowAutoGenerateModal(false);\n    }\n  };\n\n  const openAutoGenerateModal = () => {\n    setShowAutoGenerateModal(true);\n  };\n\n  const onFinish = async (values) => {\n    console.log(\"🚀 Form submission started\");\n    console.log(\"📝 Form values:\", values);\n\n    try {\n      setIsGenerating(true);\n      setGenerationProgress(10);\n\n      // Validate question distribution\n      const totalDistribution = Object.values(values.questionDistribution || {}).reduce((sum, count) => sum + (count || 0), 0);\n      console.log(\"📊 Total distribution:\", totalDistribution, \"Total questions:\", values.totalQuestions);\n\n      if (totalDistribution !== values.totalQuestions) {\n        console.error(\"❌ Distribution validation failed\");\n        message.error(\"Question distribution must equal total questions\");\n        setIsGenerating(false);\n        return;\n      }\n\n      console.log(\"✅ Distribution validation passed\");\n\n      setGenerationProgress(30);\n\n      // Check authentication and AI access\n      if (!isAuthenticated || !hasAIAccess) {\n        setIsGenerating(false);\n        setShowLoginModal(true);\n        message.warning(\"Please login to access AI question generation features.\");\n        return;\n      }\n\n      // Double-check with server-side validation\n      const authCheck = await requireAIAuth();\n      if (!authCheck.success) {\n        setIsGenerating(false);\n\n        switch (authCheck.reason) {\n          case 'not_authenticated':\n          case 'refresh_failed':\n            setShowLoginModal(true);\n            message.warning(\"Please login to generate AI questions.\");\n            return;\n          case 'no_ai_access':\n            message.error(\"AI features are not available for your account.\");\n            return;\n          case 'requires_upgrade':\n            message.warning(\"AI question generation requires a premium subscription. Please upgrade your account.\");\n            return;\n          default:\n            setShowLoginModal(true);\n            message.warning(\"Authentication check failed. Please login again.\");\n            return;\n        }\n      }\n\n      const payload = {\n        examId: values.examId,\n        questionTypes: values.questionTypes,\n        subjects: values.subjects,\n        level: values.level,\n        class: values.class,\n        difficultyLevels: values.difficultyLevels,\n        syllabusTopics: values.syllabusTopics || [],\n        totalQuestions: values.totalQuestions,\n        questionDistribution: values.questionDistribution,\n        userId: user._id,\n      };\n\n      console.log(\"📤 Sending payload:\", payload);\n\n      setGenerationProgress(50);\n\n      console.log(\"🌐 Making API call to generate questions...\");\n\n      // Show progress message to user\n      message.info(\"AI is generating your questions... This may take a few minutes.\", 5);\n\n      const response = await generateQuestions(payload);\n      console.log(\"📥 API response received:\", response);\n\n      setGenerationProgress(90);\n\n      if (response.success) {\n        setGenerationProgress(100);\n        message.success(\"Questions generated successfully!\");\n        setTimeout(() => {\n          onSuccess();\n        }, 1000);\n      } else {\n        message.error(response.message || \"Failed to generate questions\");\n      }\n    } catch (error) {\n      console.error(\"Question generation error:\", error);\n\n      // More detailed error handling\n      let errorMessage = \"Error generating questions\";\n\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n        // Handle timeout errors specifically\n        errorMessage = \"Question generation is taking longer than expected. This might be due to high server load. Please try again with fewer questions or check your internet connection.\";\n      } else if (error.response) {\n        // Server responded with error status\n        console.error(\"Server error response:\", error.response.data);\n        console.error(\"Server error status:\", error.response.status);\n\n        if (error.response.status === 401) {\n          // Handle authentication errors for AI requests\n          const errorData = error.response.data;\n\n          if (errorData?.requiresLogin) {\n            // Show the AI login modal instead of redirecting\n            setShowLoginModal(true);\n            errorMessage = errorData.message || \"Authentication required for AI features.\";\n          } else {\n            errorMessage = errorData?.message || \"Authentication failed. Please login again.\";\n          }\n        } else if (error.response.status === 403) {\n          // Handle permission/subscription errors\n          const errorData = error.response.data;\n          if (errorData?.upgradeRequired) {\n            errorMessage = \"AI question generation requires a premium subscription. Please upgrade your account.\";\n          } else {\n            errorMessage = errorData?.message || \"Access denied for AI features.\";\n          }\n        } else if (error.response.status === 504 || error.response.status === 502) {\n          errorMessage = \"Server timeout. The AI generation process is taking longer than expected. Please try again with fewer questions.\";\n        } else {\n          errorMessage = error.response.data?.message || `Server error: ${error.response.status}`;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        console.error(\"Network error:\", error.request);\n        errorMessage = \"Network error - please check your connection. If the problem persists, try generating fewer questions at once.\";\n      } else {\n        // Something else happened\n        console.error(\"Error:\", error.message);\n        errorMessage = error.message || \"Unknown error occurred\";\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setIsGenerating(false);\n      setGenerationProgress(0);\n    }\n  };\n\n  const questionTypeOptions = [\n    { label: \"Multiple Choice\", value: \"multiple_choice\" },\n    { label: \"Fill in the Blank\", value: \"fill_blank\" },\n    { label: \"Picture-based\", value: \"picture_based\" },\n  ];\n\n  const difficultyOptions = [\n    { label: \"Easy\", value: \"easy\" },\n    { label: \"Medium\", value: \"medium\" },\n    { label: \"Hard\", value: \"hard\" },\n  ];\n\n  const levelOptions = [\n    { label: \"Primary Education (Standards I-VI)\", value: \"primary\" },\n    { label: \"Ordinary Secondary (Forms I-IV)\", value: \"ordinary_secondary\" },\n    { label: \"Advanced Secondary (Forms V-VI)\", value: \"advanced_secondary\" },\n  ];\n\n  const classOptions = {\n    primary: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\"], // Standards I-VI (TIE Primary)\n    ordinary_secondary: [\"I\", \"II\", \"III\", \"IV\"], // Forms I-IV (TIE Ordinary Secondary)\n    advanced_secondary: [\"V\", \"VI\"], // Forms V-VI (TIE Advanced Secondary)\n  };\n\n  return (\n    <div className=\"question-generation-form\">\n      <Card\n        title={\n          <div className=\"form-header\">\n            <Button\n              type=\"text\"\n              icon={<FaArrowLeft />}\n              onClick={onBack}\n              className=\"back-button\"\n            >\n              Back to Dashboard\n            </Button>\n            <div className=\"title-section\">\n              <FaRobot className=\"title-icon\" />\n              <span>Generate AI Questions</span>\n            </div>\n          </div>\n        }\n      >\n        {/* Authentication Status */}\n        {authLoading ? (\n          <Alert\n            message=\"Checking Authentication...\"\n            description=\"Verifying your access to AI features.\"\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !isAuthenticated ? (\n          <Alert\n            message=\"Login Required\"\n            description={\n              <div>\n                <p>Please login to access AI question generation features.</p>\n                <Button\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => setShowLoginModal(true)}\n                  style={{ marginTop: 8 }}\n                >\n                  Login Now\n                </Button>\n              </div>\n            }\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !hasAIAccess ? (\n          <Alert\n            message={requiresUpgrade ? \"Upgrade Required\" : \"AI Access Restricted\"}\n            description={\n              requiresUpgrade\n                ? \"AI question generation requires a premium subscription. Please upgrade your account.\"\n                : \"AI features are not available for your account. Please contact support.\"\n            }\n            type=\"error\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : sessionExpiringSoon ? (\n          <Alert\n            message=\"Session Expiring Soon\"\n            description={`Your session will expire in ${timeUntilExpiry}. Consider refreshing your login.`}\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n            action={\n              <Button\n                size=\"small\"\n                onClick={() => setShowLoginModal(true)}\n              >\n                Refresh Login\n              </Button>\n            }\n          />\n        ) : (\n          <Alert\n            message=\"AI Features Ready\"\n            description={`Welcome ${user?.name}! You have full access to AI question generation.`}\n            type=\"success\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        {isGenerating && (\n          <Alert\n            message=\"Generating Questions\"\n            description={\n              <div>\n                <p>AI is generating your questions. This may take a few moments...</p>\n                <Progress percent={generationProgress} status=\"active\" />\n              </div>\n            }\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={onFinish}\n          disabled={isGenerating || !hasAIAccess || authLoading}\n        >\n          <Row gutter={[16, 16]}>\n            <Col xs={24}>\n              <Alert\n                message=\"Exam Selection\"\n                description=\"You can either select an existing exam or create a new one using the auto-generate feature. Questions can also be generated independently without an exam.\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 16 }}\n              />\n            </Col>\n\n            <Col xs={24} md={16}>\n              <Form.Item\n                name=\"examId\"\n                label=\"Target Exam (Optional)\"\n                extra=\"Leave empty to generate standalone questions, or select an existing exam\"\n              >\n                <Select\n                  placeholder=\"Optional: Choose an existing exam\"\n                  allowClear\n                >\n                  {exams && exams.length > 0 && exams.map((exam) => (\n                    exam && exam._id ? (\n                      <Option key={exam._id} value={exam._id}>\n                        {exam.name} - {exam.category}\n                      </Option>\n                    ) : null\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item label=\"Or Create New Exam\">\n                <Button\n                  type=\"dashed\"\n                  icon={<FaRobot />}\n                  onClick={openAutoGenerateModal}\n                  style={{ width: \"100%\" }}\n                  disabled={isGenerating || !hasAIAccess || authLoading}\n                >\n                  Auto-Generate New Exam\n                </Button>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"level\"\n                label=\"Education Level\"\n                rules={[{ required: true, message: \"Please select a level\" }]}\n              >\n                <Select \n                  placeholder=\"Choose education level\"\n                  onChange={handleLevelChange}\n                >\n                  {levelOptions.map((option) => (\n                    <Option key={option.value} value={option.value}>\n                      {option.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"class\"\n                label=\"Class\"\n                rules={[{ required: true, message: \"Please select a class\" }]}\n              >\n                <Select\n                  placeholder=\"Choose class\"\n                  disabled={!selectedLevel}\n                  onChange={handleClassChange}\n                >\n                  {selectedLevel && classOptions[selectedLevel]?.map((cls) => (\n                    <Option key={cls} value={cls}>\n                      Class {cls}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"subjects\"\n                label=\"Subjects\"\n                rules={[{ required: true, message: \"Please select at least one subject\" }]}\n              >\n                <Select\n                  mode=\"multiple\"\n                  placeholder=\"Choose subjects\"\n                  disabled={!selectedLevel}\n                  onChange={handleSubjectsChange}\n                >\n                  {availableSubjects.map((subject) => (\n                    <Option key={subject} value={subject}>\n                      {subject}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"questionTypes\"\n                label=\"Question Types\"\n                rules={[{ required: true, message: \"Please select at least one question type\" }]}\n              >\n                <Checkbox.Group options={questionTypeOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"difficultyLevels\"\n                label=\"Difficulty Levels\"\n                rules={[{ required: true, message: \"Please select at least one difficulty level\" }]}\n              >\n                <Checkbox.Group options={difficultyOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"totalQuestions\"\n                label=\"Total Questions\"\n                rules={[\n                  { required: true, message: \"Please enter total questions\" },\n                  { type: \"number\", min: 1, max: 50, message: \"Must be between 1 and 50\" }\n                ]}\n              >\n                <InputNumber\n                  min={1}\n                  max={50}\n                  placeholder=\"Enter total questions\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Divider>Question Distribution</Divider>\n\n          {selectedLevel && selectedClass && selectedSubjects.length > 0 && (\n            <Alert\n              message=\"Tanzania Syllabus Information\"\n              description={\n                <div>\n                  <p><strong>Level:</strong> {selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</p>\n                  <p><strong>Class:</strong> {selectedClass}</p>\n                  <p><strong>Subjects:</strong> {selectedSubjects.join(\", \")}</p>\n                  <p><strong>Available Topics:</strong> {availableTopics.length} topics from Tanzania National Curriculum</p>\n                  <p><strong>Auto-generate:</strong> Use the button above to create a new exam with proper structure</p>\n                </div>\n              }\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n          )}\n\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"multiple_choice\"]}\n                label=\"Multiple Choice\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"fill_blank\"]}\n                label=\"Fill in the Blank\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"picture_based\"]}\n                label=\"Picture-based\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"syllabusTopics\"\n            label={`Tanzania Syllabus Topics (${availableTopics.length} available)`}\n            extra={availableTopics.length === 0 ? \"Select level, class, and subjects to see available topics\" : \"Select specific topics from Tanzania National Curriculum\"}\n          >\n            <Select\n              mode=\"multiple\"\n              placeholder={availableTopics.length === 0 ? \"No topics available - select level, class, and subjects first\" : \"Choose specific topics from Tanzania syllabus\"}\n              style={{ width: \"100%\" }}\n              disabled={availableTopics.length === 0}\n              optionFilterProp=\"children\"\n              showSearch\n              filterOption={(input, option) =>\n                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0\n              }\n            >\n              {availableTopics.map((topic, index) => (\n                <Option key={`${topic.subject}-${topic.topicName}-${index}`} value={topic.topicName}>\n                  <div>\n                    <strong>{topic.topicName}</strong>\n                    <div style={{ fontSize: \"12px\", color: \"#666\" }}>\n                      {topic.subject} • Difficulty: {topic.difficulty}\n                    </div>\n                    {topic.subtopics && topic.subtopics.length > 0 && (\n                      <div style={{ fontSize: \"11px\", color: \"#999\" }}>\n                        Subtopics: {topic.subtopics.slice(0, 3).join(\", \")}\n                        {topic.subtopics.length > 3 && ` +${topic.subtopics.length - 3} more`}\n                      </div>\n                    )}\n                  </div>\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <div className=\"form-actions\">\n            <Button onClick={onBack} disabled={isGenerating}>\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={isGenerating}\n              disabled={!hasAIAccess || authLoading}\n              icon={<FaRobot />}\n            >\n              {isGenerating ? \"Generating...\" : !hasAIAccess ? \"Login Required\" : \"Generate Questions\"}\n            </Button>\n          </div>\n        </Form>\n      </Card>\n\n      <AutoGenerateExamModal\n        visible={showAutoGenerateModal}\n        onCancel={() => setShowAutoGenerateModal(false)}\n        onSuccess={handleAutoGenerateExamSuccess}\n        prefilledData={{\n          level: selectedLevel,\n          class: selectedClass,\n          subjects: selectedSubjects,\n        }}\n      />\n\n      <AILoginModal\n        visible={showLoginModal}\n        onCancel={() => setShowLoginModal(false)}\n        onSuccess={(userData) => {\n          handleLoginSuccess(userData);\n          setShowLoginModal(false);\n        }}\n        title=\"AI Features Login Required\"\n        description=\"Please login to access AI question generation features. Your session may have expired or you need enhanced permissions.\"\n      />\n    </div>\n  );\n}\n\nexport default QuestionGenerationForm;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionPreview.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\AutoGenerateExamModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\authUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AILoginModal.js", ["702", "703"], [], "import React, { useState, useEffect } from 'react';\nimport { Modal, Form, Input, Button, Checkbox, Alert, Typography, Space, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, RobotOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { quickLogin, autoRefreshToken } from '../apicalls/auth';\nimport { getTokenExpiryInfo } from '../utils/authUtils';\n\nconst { Title, Text } = Typography;\n\nconst AILoginModal = ({ \n  visible, \n  onCancel, \n  onSuccess, \n  title = \"Login Required for AI Features\",\n  description = \"Please login to access AI question generation features.\"\n}) => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [autoRefreshing, setAutoRefreshing] = useState(false);\n\n  useEffect(() => {\n    if (visible) {\n      // Check current token status when modal opens\n      const info = getTokenExpiryInfo();\n      setTokenInfo(info);\n      \n      // Try auto-refresh if token is expiring soon\n      if (info.needsRefresh && !info.expired) {\n        handleAutoRefresh();\n      }\n    }\n  }, [visible]);\n\n  const handleAutoRefresh = async () => {\n    try {\n      setAutoRefreshing(true);\n      const success = await autoRefreshToken();\n      if (success) {\n        const newInfo = getTokenExpiryInfo();\n        setTokenInfo(newInfo);\n        \n        if (!newInfo.expired) {\n          onSuccess?.();\n          return;\n        }\n      }\n    } catch (error) {\n      console.error('Auto-refresh failed:', error);\n    } finally {\n      setAutoRefreshing(false);\n    }\n  };\n\n  const handleLogin = async (values) => {\n    try {\n      setLoading(true);\n      \n      const response = await quickLogin({\n        email: values.email,\n        password: values.password,\n        rememberMe: values.rememberMe || false\n      });\n\n      if (response.success) {\n        // Check AI access\n        const { aiAccess } = response.data;\n        \n        if (!aiAccess.enabled) {\n          Modal.warning({\n            title: 'AI Features Not Available',\n            content: aiAccess.requiresUpgrade \n              ? 'AI question generation requires a premium subscription. Please upgrade your account.'\n              : 'AI features are not available for your account. Please contact support.',\n          });\n          return;\n        }\n\n        form.resetFields();\n        onSuccess?.(response.data);\n      }\n    } catch (error) {\n      console.error('Login failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderTokenStatus = () => {\n    if (!tokenInfo) return null;\n\n    if (tokenInfo.expired) {\n      return (\n        <Alert\n          type=\"warning\"\n          icon={<ClockCircleOutlined />}\n          message=\"Session Expired\"\n          description=\"Your session has expired. Please login again to continue using AI features.\"\n          style={{ marginBottom: 16 }}\n        />\n      );\n    }\n\n    if (tokenInfo.needsRefresh) {\n      return (\n        <Alert\n          type=\"info\"\n          icon={<ClockCircleOutlined />}\n          message=\"Session Expiring Soon\"\n          description={`Your session will expire in ${tokenInfo.formattedTimeLeft}. Login to extend your session.`}\n          style={{ marginBottom: 16 }}\n        />\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Modal\n      title={\n        <Space>\n          <RobotOutlined style={{ color: '#1890ff' }} />\n          <span>{title}</span>\n        </Space>\n      }\n      open={visible}\n      onCancel={onCancel}\n      footer={null}\n      width={450}\n      destroyOnClose\n      maskClosable={false}\n    >\n      <div style={{ padding: '20px 0' }}>\n        <Text type=\"secondary\" style={{ display: 'block', marginBottom: 24, textAlign: 'center' }}>\n          {description}\n        </Text>\n\n        {renderTokenStatus()}\n\n        {autoRefreshing && (\n          <Alert\n            type=\"info\"\n            message=\"Refreshing Session...\"\n            description=\"Attempting to refresh your authentication automatically.\"\n            style={{ marginBottom: 16 }}\n            showIcon\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleLogin}\n          autoComplete=\"off\"\n          size=\"large\"\n        >\n          <Form.Item\n            name=\"email\"\n            label=\"Email\"\n            rules={[\n              { required: true, message: 'Please enter your email' },\n              { type: 'email', message: 'Please enter a valid email' }\n            ]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder=\"Enter your email\"\n              autoComplete=\"email\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            label=\"Password\"\n            rules={[{ required: true, message: 'Please enter your password' }]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"Enter your password\"\n              autoComplete=\"current-password\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"rememberMe\" valuePropName=\"checked\">\n            <Checkbox>\n              Keep me logged in for 30 days\n            </Checkbox>\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0 }}>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading || autoRefreshing}\n              block\n              size=\"large\"\n              icon={<RobotOutlined />}\n            >\n              {loading ? 'Logging in...' : autoRefreshing ? 'Refreshing...' : 'Login for AI Features'}\n            </Button>\n          </Form.Item>\n        </Form>\n\n        <Divider />\n\n        <div style={{ textAlign: 'center' }}>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            Secure authentication for AI-powered question generation\n          </Text>\n        </div>\n      </div>\n    </Modal>\n  );\n};\n\nexport default AILoginModal;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\hooks\\useAIAuth.js", ["704"], [], "import { useState, useEffect, useCallback } from 'react';\nimport { message } from 'antd';\nimport { validateSession, autoRefreshToken, checkAIAccess } from '../apicalls/auth';\nimport { getTokenExpiryInfo, isSessionValid } from '../utils/authUtils';\n\n/**\n * Enhanced authentication hook specifically for AI features\n * Provides automatic token refresh, session validation, and AI access checking\n */\nexport const useAIAuth = () => {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [hasAIAccess, setHasAIAccess] = useState(false);\n  const [user, setUser] = useState(null);\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [requiresUpgrade, setRequiresUpgrade] = useState(false);\n\n  // Check authentication status\n  const checkAuth = useCallback(async () => {\n    try {\n      setLoading(true);\n      \n      // Quick check if session is valid\n      if (!isSessionValid()) {\n        setIsAuthenticated(false);\n        setHasAIAccess(false);\n        setUser(null);\n        setTokenInfo(null);\n        return false;\n      }\n\n      // Get token expiry info\n      const expiry = getTokenExpiryInfo();\n      setTokenInfo(expiry);\n\n      // If token is expired, clear everything\n      if (expiry.expired) {\n        setIsAuthenticated(false);\n        setHasAIAccess(false);\n        setUser(null);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        return false;\n      }\n\n      // Try to auto-refresh if needed\n      if (expiry.needsRefresh) {\n        try {\n          await autoRefreshToken();\n          const newExpiry = getTokenExpiryInfo();\n          setTokenInfo(newExpiry);\n        } catch (error) {\n          console.warn('Auto-refresh failed:', error);\n        }\n      }\n\n      // Validate session and check AI access\n      const accessCheck = await checkAIAccess();\n      \n      if (accessCheck.hasAccess) {\n        setIsAuthenticated(true);\n        setHasAIAccess(true);\n        setUser(accessCheck.user);\n        setRequiresUpgrade(accessCheck.requiresUpgrade || false);\n        return true;\n      } else {\n        setIsAuthenticated(!!accessCheck.user);\n        setHasAIAccess(false);\n        setUser(accessCheck.user || null);\n        setRequiresUpgrade(accessCheck.requiresUpgrade || false);\n        return false;\n      }\n\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setIsAuthenticated(false);\n      setHasAIAccess(false);\n      setUser(null);\n      setTokenInfo(null);\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  // Refresh authentication\n  const refreshAuth = useCallback(async () => {\n    return await checkAuth();\n  }, [checkAuth]);\n\n  // Logout\n  const logout = useCallback(() => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setIsAuthenticated(false);\n    setHasAIAccess(false);\n    setUser(null);\n    setTokenInfo(null);\n    message.info('Logged out successfully');\n  }, []);\n\n  // Login success handler\n  const handleLoginSuccess = useCallback((userData) => {\n    setIsAuthenticated(true);\n    setUser(userData.user);\n    \n    // Check AI access from login response\n    const aiEnabled = userData.aiAccess?.enabled !== false;\n    setHasAIAccess(aiEnabled);\n    setRequiresUpgrade(userData.aiAccess?.requiresUpgrade || false);\n    \n    // Update token info\n    const expiry = getTokenExpiryInfo();\n    setTokenInfo(expiry);\n    \n    message.success('Successfully logged in for AI features!');\n  }, []);\n\n  // Require authentication for AI operations\n  const requireAIAuth = useCallback(async () => {\n    if (loading) {\n      return { success: false, reason: 'loading' };\n    }\n\n    if (!isAuthenticated) {\n      return { success: false, reason: 'not_authenticated' };\n    }\n\n    if (!hasAIAccess) {\n      if (requiresUpgrade) {\n        return { success: false, reason: 'requires_upgrade' };\n      }\n      return { success: false, reason: 'no_ai_access' };\n    }\n\n    // Check if token is about to expire\n    if (tokenInfo?.needsRefresh) {\n      try {\n        await autoRefreshToken();\n        const newExpiry = getTokenExpiryInfo();\n        setTokenInfo(newExpiry);\n      } catch (error) {\n        return { success: false, reason: 'refresh_failed' };\n      }\n    }\n\n    return { success: true };\n  }, [isAuthenticated, hasAIAccess, requiresUpgrade, tokenInfo, loading]);\n\n  // Auto-refresh timer\n  useEffect(() => {\n    let refreshTimer;\n\n    if (isAuthenticated && tokenInfo && !tokenInfo.expired) {\n      // Set timer to refresh token 5 minutes before expiry\n      const refreshTime = Math.max(0, (tokenInfo.timeLeft - 300) * 1000);\n      \n      refreshTimer = setTimeout(async () => {\n        try {\n          await autoRefreshToken();\n          const newExpiry = getTokenExpiryInfo();\n          setTokenInfo(newExpiry);\n          console.log('🔄 Token auto-refreshed');\n        } catch (error) {\n          console.warn('Auto-refresh timer failed:', error);\n        }\n      }, refreshTime);\n    }\n\n    return () => {\n      if (refreshTimer) {\n        clearTimeout(refreshTimer);\n      }\n    };\n  }, [isAuthenticated, tokenInfo]);\n\n  // Initial auth check\n  useEffect(() => {\n    checkAuth();\n  }, [checkAuth]);\n\n  return {\n    // State\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    tokenInfo,\n    loading,\n    requiresUpgrade,\n    \n    // Actions\n    checkAuth,\n    refreshAuth,\n    logout,\n    handleLoginSuccess,\n    requireAIAuth,\n    \n    // Computed values\n    needsLogin: !isAuthenticated,\n    needsUpgrade: isAuthenticated && !hasAIAccess && requiresUpgrade,\n    sessionExpiringSoon: tokenInfo?.needsRefresh || false,\n    timeUntilExpiry: tokenInfo?.formattedTimeLeft || 'Unknown'\n  };\n};\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js", ["705", "706", "707"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizStart.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js", ["708", "709", "710"], [], "import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport QuizRenderer from '../../../components/QuizRenderer';\nimport QuizErrorBoundary from '../../../components/QuizErrorBoundary';\n\nconst QuizPlay = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  const getExamData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({ examId: id });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        setQuestions(response.data?.questions || []);\n        setExamData(response.data);\n        setSecondsLeft((response.data?.duration || 0) * 60);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n      navigate('/user/quiz');\n    }\n  };\n\n  const checkFreeTextAnswers = async (payload) => {\n    if (!payload.length) return [];\n    const { data } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n\n      dispatch(ShowLoading());\n\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\",\n          });\n        }\n      });\n\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n\n      gptResults.forEach((r) => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\n        }\n      });\n\n      const correctAnswers = [];\n      const wrongAnswers = [];\n\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = { ...q, userAnswer: userAnswerKey };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = (examData?.duration || 0) * 60;\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);\n      const points = correctCount * 10;\n\n      // Handle both passingMarks and passingPercentage for backward compatibility\n      const passingPercentage = examData.passingPercentage || examData.passingMarks || 70;\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict,\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id,\n      });\n\n      if (response.success) {\n        // Clear ranking cache for real-time updates\n        localStorage.removeItem('rankingCache');\n        localStorage.removeItem('userRankingPosition');\n        localStorage.removeItem('leaderboardData');\n\n        // Trigger ranking update event for real-time updates\n        window.dispatchEvent(new CustomEvent('rankingUpdate', {\n          detail: {\n            userId: user._id,\n            xpGained: response.xpData?.xpAwarded || 0,\n            newTotalXP: response.xpData?.newTotalXP || 0,\n            levelUp: response.xpData?.levelUp || false,\n            newLevel: response.xpData?.newLevel || user.currentLevel\n          }\n        }));\n\n        // Debug XP data\n        console.log('🔍 Quiz completion response:', response);\n        console.log('💰 XP Data received:', response.xpData);\n\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n\n        console.log('📊 Final result with XP:', resultWithXP);\n        navigate(`/quiz/${id}/result`, { state: { result: resultWithXP } });\n      } else {\n        message.error(response.message);\n        console.error('❌ Quiz submission failed:', response.message);\n      }\n      dispatch(HideLoading());\n\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n\n  const startTimer = useCallback(() => {\n    const totalSeconds = (examData?.duration || 0) * 60;\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now());\n\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft((prevSeconds) => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n\n  useEffect(() => {\n    if (timeUp && intervalId) {\n      clearInterval(intervalId);\n      setIntervalId(null);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n\n  useEffect(() => {\n    if (id) {\n      getExamData();\n    }\n  }, []);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions]);\n\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n        setIntervalId(null);\n      }\n    };\n  }, [intervalId]);\n\n  if (!examData || questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading quiz questions...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <QuizErrorBoundary>\n      <QuizRenderer\n        question={questions[selectedQuestionIndex]}\n        questionIndex={selectedQuestionIndex}\n        totalQuestions={questions.length}\n        selectedAnswer={selectedOptions[selectedQuestionIndex]}\n        onAnswerChange={(answer) =>\n          setSelectedOptions({\n            ...selectedOptions,\n            [selectedQuestionIndex]: answer,\n          })\n        }\n        timeLeft={secondsLeft}\n        examTitle={examData?.name || \"Quiz\"}\n        isTimeWarning={secondsLeft <= 60}\n        onNext={() => {\n          if (selectedQuestionIndex === questions.length - 1) {\n            calculateResult();\n          } else {\n            setSelectedQuestionIndex(selectedQuestionIndex + 1);\n          }\n        }}\n        onPrevious={() => setSelectedQuestionIndex(selectedQuestionIndex - 1)}\n      />\n    </QuizErrorBoundary>\n  );\n};\n\nexport default QuizPlay;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizRenderer.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js", ["711", "712", "713", "714"], [], "import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n  const inspiringQuotes = [\n    \"Education is the most powerful weapon which you can use to change the world. - <PERSON>\",\n    \"The future belongs to those who believe in the beauty of their dreams. - <PERSON>\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts. - <PERSON> Churchill\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\"\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const navigationItems = [\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, videos & notes',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: 'Ask AI',\n      description: 'Get instant help from AI',\n      icon: FaRobot,\n      path: '/user/chat',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: 'Reports',\n      description: 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-orange-500 to-orange-600',\n      hoverColor: 'from-orange-600 to-orange-700'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage your account',\n      icon: FaUser,\n      path: '/profile',\n      color: 'from-indigo-500 to-indigo-600',\n      hoverColor: 'from-indigo-600 to-indigo-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    },\n    {\n      title: 'Plans',\n      description: 'Upgrade your learning',\n      icon: FaCreditCard,\n      path: '/user/plans',\n      color: 'from-emerald-500 to-emerald-600',\n      hoverColor: 'from-emerald-600 to-emerald-700'\n    },\n    {\n      title: 'About Us',\n      description: 'Learn about our mission',\n      icon: FaInfoCircle,\n      path: '/user/about-us',\n      color: 'from-cyan-500 to-cyan-600',\n      hoverColor: 'from-cyan-600 to-cyan-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      <div className=\"hub-content\">\n\n\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"hub-header\"\n        >\n\n\n          {/* Dynamic Inspiring Study Smarter Animation */}\n          <div className=\"hub-welcome relative overflow-hidden min-h-[200px]\">\n\n\n            {/* Dynamic Background Waves */}\n            <motion.div\n              className=\"absolute inset-0 rounded-3xl\"\n              style={{\n                background: 'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.06), transparent)',\n                filter: 'blur(30px)'\n              }}\n              animate={{\n                scale: [1, 1.1, 1],\n                opacity: [0.3, 0.6, 0.3],\n                rotate: [0, 2, -2, 0]\n              }}\n              transition={{\n                duration: 8,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n            />\n\n            {/* Main Content */}\n            <motion.div\n              className=\"relative z-10 text-center\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 1.5 }}\n            >\n              {/* Study Text with Motion */}\n              <motion.div\n                className=\"relative inline-block mr-6\"\n                initial={{ x: -200, opacity: 0, rotateY: -90 }}\n                animate={{\n                  x: 0,\n                  opacity: 1,\n                  rotateY: 0\n                }}\n                transition={{\n                  duration: 1.8,\n                  delay: 0.5,\n                  ease: \"easeOut\"\n                }}\n              >\n                <motion.span\n                  className=\"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\"\n                  style={{\n                    background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 30%, #60a5fa 60%, #93c5fd 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                    letterSpacing: '-0.06em',\n                    textShadow: '0 0 50px rgba(59, 130, 246, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    y: [0, -5, 0],\n                    textShadow: [\n                      '0 0 50px rgba(59, 130, 246, 0.4)',\n                      '0 0 80px rgba(59, 130, 246, 0.7)',\n                      '0 0 50px rgba(59, 130, 246, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 6, repeat: Infinity, ease: \"easeInOut\" },\n                    y: { duration: 3, repeat: Infinity, ease: \"easeInOut\" },\n                    textShadow: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                  }}\n                  whileHover={{\n                    scale: 1.05,\n                    rotateZ: [0, 2, -2, 0],\n                    transition: { duration: 0.6 }\n                  }}\n                >\n                  Study\n                </motion.span>\n\n\n              </motion.div>\n\n              {/* Smarter Text with Motion */}\n              <motion.div\n                className=\"relative inline-block\"\n                initial={{ x: 200, opacity: 0, rotateY: 90 }}\n                animate={{\n                  x: 0,\n                  opacity: 1,\n                  rotateY: 0\n                }}\n                transition={{\n                  duration: 1.8,\n                  delay: 1,\n                  ease: \"easeOut\"\n                }}\n              >\n                <motion.span\n                  className=\"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\"\n                  style={{\n                    background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                    letterSpacing: '-0.06em',\n                    textShadow: '0 0 50px rgba(16, 185, 129, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    y: [0, 5, 0],\n                    textShadow: [\n                      '0 0 50px rgba(16, 185, 129, 0.4)',\n                      '0 0 80px rgba(16, 185, 129, 0.7)',\n                      '0 0 50px rgba(16, 185, 129, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 5, repeat: Infinity, ease: \"easeInOut\", delay: 1 },\n                    y: { duration: 3.5, repeat: Infinity, ease: \"easeInOut\", delay: 0.5 },\n                    textShadow: { duration: 4.5, repeat: Infinity, ease: \"easeInOut\", delay: 1 }\n                  }}\n                  whileHover={{\n                    scale: 1.05,\n                    rotateZ: [0, -2, 2, 0],\n                    transition: { duration: 0.6 }\n                  }}\n                >\n                  Smarter\n                </motion.span>\n\n\n              </motion.div>\n\n              {/* User Name with Inspiring Animation */}\n              <motion.div\n                className=\"mt-8 relative\"\n                initial={{ opacity: 0, y: 50, scale: 0.8 }}\n                animate={{ opacity: 1, y: 0, scale: 1 }}\n                transition={{ duration: 1.5, delay: 2 }}\n              >\n                <motion.span\n                  className=\"text-3xl sm:text-4xl font-bold block\"\n                  style={{\n                    background: 'linear-gradient(45deg, #f59e0b, #f97316, #ef4444, #ec4899)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    textShadow: '0 0 30px rgba(245, 158, 11, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    scale: [1, 1.02, 1],\n                    textShadow: [\n                      '0 0 30px rgba(245, 158, 11, 0.4)',\n                      '0 0 50px rgba(245, 158, 11, 0.7)',\n                      '0 0 30px rgba(245, 158, 11, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                    scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                    textShadow: { duration: 3, repeat: Infinity, ease: \"easeInOut\" }\n                  }}\n                  whileHover={{\n                    scale: 1.1,\n                    rotate: [0, 3, -3, 0],\n                    transition: { duration: 0.4 }\n                  }}\n                >\n                  {user?.name}!\n                </motion.span>\n\n\n              </motion.div>\n\n              {/* Dynamic Inspiring Underline */}\n              <motion.div\n                className=\"mt-6 relative\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 1, delay: 2.5 }}\n              >\n                <motion.div\n                  className=\"h-2 mx-auto rounded-full relative overflow-hidden\"\n                  style={{\n                    width: '90%',\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444, #8b5cf6)',\n                    boxShadow: '0 0 30px rgba(59, 130, 246, 0.5)'\n                  }}\n                  animate={{\n                    boxShadow: [\n                      '0 0 30px rgba(59, 130, 246, 0.5)',\n                      '0 0 50px rgba(16, 185, 129, 0.7)',\n                      '0 0 40px rgba(245, 158, 11, 0.6)',\n                      '0 0 30px rgba(59, 130, 246, 0.5)'\n                    ]\n                  }}\n                  transition={{\n                    duration: 6,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                >\n                  {/* Moving Light Effect */}\n                  <motion.div\n                    className=\"absolute inset-0 rounded-full\"\n                    style={{\n                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.9), transparent)',\n                      width: '40%'\n                    }}\n                    animate={{\n                      x: ['-100%', '250%']\n                    }}\n                    transition={{\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\",\n                      delay: 3\n                    }}\n                  />\n                </motion.div>\n              </motion.div>\n            </motion.div>\n          </div>\n\n          <p className=\"hub-subtitle\">\n            Ready to shine today? ✨ Choose your learning path below.\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </motion.div>\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                >\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js", ["715"], [], "import React, { useState, useEffect, Fragment } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbCheck, TbX, TbClock, TbBulb } from 'react-icons/tb';\nimport { Card, Button } from './index';\n\nconst QuizQuestion = ({\n  question,\n  questionNumber,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerSelect,\n  onNext,\n  onPrevious,\n  showResult = false,\n  correctAnswer = null,\n  timeRemaining = null,\n  isLastQuestion = false,\n  className = '',\n}) => {\n  const [selectedOption, setSelectedOption] = useState(selectedAnswer);\n\n  useEffect(() => {\n    setSelectedOption(selectedAnswer);\n  }, [selectedAnswer]);\n\n  const handleOptionSelect = (optionIndex) => {\n    if (showResult) return; // Prevent selection when showing results\n    \n    setSelectedOption(optionIndex);\n    onAnswerSelect(optionIndex);\n  };\n\n  const getOptionClassName = (optionIndex) => {\n    const baseClasses = 'quiz-option group cursor-pointer';\n    \n    if (showResult) {\n      if (optionIndex === correctAnswer) {\n        return `${baseClasses} quiz-option-correct cursor-default`;\n      }\n      if (optionIndex === selectedOption && optionIndex !== correctAnswer) {\n        return `${baseClasses} quiz-option-incorrect cursor-default`;\n      }\n      return `${baseClasses} opacity-60 cursor-default`;\n    }\n    \n    if (optionIndex === selectedOption) {\n      return `${baseClasses} quiz-option-selected`;\n    }\n    \n    return baseClasses;\n  };\n\n  const getOptionIcon = (optionIndex) => {\n    if (!showResult) return null;\n    \n    if (optionIndex === correctAnswer) {\n      return <TbCheck className=\"w-5 h-5 text-success-600\" />;\n    }\n    if (optionIndex === selectedOption && optionIndex !== correctAnswer) {\n      return <TbX className=\"w-5 h-5 text-error-600\" />;\n    }\n    return null;\n  };\n\n  const renderQuestionContent = () => {\n    // Check for image in multiple possible properties\n    const questionImage = question.image || question.imageUrl;\n\n    // Debug logging for image detection removed to prevent React rendering issues\n\n    switch (question.type) {\n      case 'image':\n        return (\n          <div className=\"space-y-6\">\n            {questionImage && (\n              <div className=\"quiz-image-container-modern\">\n                <div className=\"quiz-image-wrapper\">\n                  <img\n                    src={questionImage}\n                    alt=\"Question diagram\"\n                    className=\"quiz-image-modern\"\n                    onError={(e) => {\n                      console.error('Image failed to load:', questionImage);\n                      e.target.style.display = 'none';\n                      // Show error message\n                      const errorDiv = document.createElement('div');\n                      errorDiv.className = 'text-red-500 text-sm text-center p-4 bg-red-50 rounded-lg border border-red-200';\n                      errorDiv.textContent = 'Image could not be loaded. Please check the image URL.';\n                      e.target.parentNode.appendChild(errorDiv);\n                    }}\n                    onLoad={() => {\n                      console.log('Image loaded successfully:', questionImage);\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n            <div className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {question.name}\n            </div>\n          </div>\n        );\n      \n      case 'fill':\n        return (\n          <div className=\"space-y-4\">\n            {/* Show image if available */}\n            {questionImage && (\n              <div className=\"quiz-image-container-modern\">\n                <div className=\"quiz-image-wrapper\">\n                  <img\n                    src={questionImage}\n                    alt=\"Question diagram\"\n                    className=\"quiz-image-modern\"\n                    onError={(e) => {\n                      console.error('Image failed to load:', questionImage);\n                      e.target.style.display = 'none';\n                      // Show error message\n                      const errorDiv = document.createElement('div');\n                      errorDiv.className = 'text-red-500 text-sm text-center p-4 bg-red-50 rounded-lg border border-red-200';\n                      errorDiv.textContent = 'Image could not be loaded. Please check the image URL.';\n                      e.target.parentNode.appendChild(errorDiv);\n                    }}\n                    onLoad={() => {\n                      console.log('Image loaded successfully:', questionImage);\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n            <div className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {String(question.name || '')}\n            </div>\n            {!showResult ? (\n              <input\n                type=\"text\"\n                value={selectedOption || ''}\n                onChange={(e) => handleOptionSelect(e.target.value)}\n                className=\"input-modern\"\n                placeholder=\"Type your answer here...\"\n              />\n            ) : (\n              <div className=\"space-y-2\">\n                <div className=\"p-4 bg-gray-50 rounded-lg\">\n                  <span className=\"text-sm text-gray-600\">Your answer: </span>\n                  <span className={selectedOption === correctAnswer ? 'text-success-600 font-medium' : 'text-error-600 font-medium'}>\n                    {selectedOption || 'No answer provided'}\n                  </span>\n                </div>\n                <div className=\"p-4 bg-success-50 rounded-lg\">\n                  <span className=\"text-sm text-success-600\">Correct answer: </span>\n                  <span className=\"text-success-700 font-medium\">{correctAnswer}</span>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n      \n      default: // MCQ\n        return (\n          <div className=\"space-y-6\">\n            {/* Show image if available for any question type */}\n            {questionImage && (\n              <div className=\"quiz-image-container-modern\">\n                <div className=\"quiz-image-wrapper\">\n                  <img\n                    src={questionImage}\n                    alt=\"Question diagram\"\n                    className=\"quiz-image-modern\"\n                    onError={(e) => {\n                      console.error('Image failed to load:', questionImage);\n                      e.target.style.display = 'none';\n                      // Show error message\n                      const errorDiv = document.createElement('div');\n                      errorDiv.className = 'text-red-500 text-sm text-center p-4 bg-red-50 rounded-lg border border-red-200';\n                      errorDiv.textContent = 'Image could not be loaded. Please check the image URL.';\n                      e.target.parentNode.appendChild(errorDiv);\n                    }}\n                    onLoad={() => {\n                      console.log('Image loaded successfully:', questionImage);\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n            <div className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {String(question.name || '')}\n            </div>\n            <div className=\"space-y-3\">\n              {question.options ? (() => {\n                try {\n                  // Handle both object and array formats\n                  let optionsArray = [];\n                  if (Array.isArray(question.options)) {\n                    optionsArray = question.options\n                      .filter(option => option && typeof option === 'string')\n                      .map(option => String(option).trim())\n                      .filter(option => option.length > 0);\n                  } else if (typeof question.options === 'object' && question.options !== null) {\n                    optionsArray = Object.values(question.options)\n                      .filter(option => option && typeof option === 'string')\n                      .map(option => String(option).trim())\n                      .filter(option => option.length > 0);\n                  }\n\n                  // Ensure we always return valid JSX\n                  if (optionsArray.length === 0) {\n                    return (\n                      <div className=\"text-gray-500 text-center py-4\">\n                        No valid options available\n                      </div>\n                    );\n                  }\n\n                  return (\n                    <React.Fragment>\n                      {optionsArray.map((option, index) => (\n                        <motion.div\n                          key={`option-${index}`}\n                          whileHover={!showResult ? { scale: 1.02 } : {}}\n                          whileTap={!showResult ? { scale: 0.98 } : {}}\n                          className={getOptionClassName(index)}\n                          onClick={() => handleOptionSelect(index)}\n                        >\n                          <div className=\"flex items-center justify-between\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-medium text-gray-600\">\n                                {String.fromCharCode(65 + index)}\n                              </div>\n                              <span className=\"text-gray-900 dark:text-white\">{String(option)}</span>\n                            </div>\n                            {getOptionIcon(index)}\n                          </div>\n                        </motion.div>\n                      ))}\n                    </React.Fragment>\n                  );\n                } catch (error) {\n                  console.error('Error rendering options:', error);\n                  return (\n                    <div className=\"text-red-500 text-center py-4\">\n                      Error loading options\n                    </div>\n                  );\n                }\n              })() : (\n                <div className=\"text-gray-500 text-center py-4\">\n                  No options available\n                </div>\n              )}\n            </div>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Question Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"text-sm font-medium text-gray-500\">\n            {questionNumber} of {totalQuestions}\n          </div>\n          <div className=\"progress-bar w-32\">\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: `${(questionNumber / totalQuestions) * 100}%` }}\n              className=\"progress-fill\"\n            />\n          </div>\n        </div>\n        \n        {timeRemaining !== null && (\n          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${\n            timeRemaining <= 60 ? 'bg-error-100 text-error-700' : 'bg-primary-100 text-primary-700'\n          }`}>\n            <TbClock className=\"w-4 h-4\" />\n            <span className=\"text-sm font-medium\">\n              {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Question Card */}\n      <Card className=\"p-8\">\n        {renderQuestionContent()}\n      </Card>\n\n      {/* Explanation (shown after answer) */}\n      <AnimatePresence>\n        {showResult && question.explanation && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n          >\n            <Card className=\"p-6 bg-blue-50 border-blue-200\">\n              <div className=\"flex items-start space-x-3\">\n                <TbBulb className=\"w-6 h-6 text-blue-600 flex-shrink-0 mt-0.5\" />\n                <div>\n                  <h4 className=\"font-medium text-blue-900 mb-2\">Explanation</h4>\n                  <p className=\"text-blue-800 text-sm leading-relaxed\">\n                    {String(question.explanation || '')}\n                  </p>\n                </div>\n              </div>\n            </Card>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Navigation */}\n      <div className=\"flex items-center justify-between pt-6\">\n        <Button\n          variant=\"secondary\"\n          onClick={onPrevious}\n          disabled={questionNumber === 1}\n        >\n          Previous\n        </Button>\n        \n        <div className=\"text-sm text-gray-500\">\n          {selectedOption !== null && selectedOption !== undefined ? (\n            <span className=\"text-primary-600 font-medium\">Answer selected</span>\n          ) : (\n            <span>Select an answer to continue</span>\n          )}\n        </div>\n        \n        <Button\n          variant=\"primary\"\n          onClick={onNext}\n          disabled={!showResult && (selectedOption === null || selectedOption === undefined)}\n        >\n          {isLastQuestion ? 'Finish Quiz' : 'Next'}\n        </Button>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizQuestion;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js", ["716"], [], "import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { TbClock, TbAlertTriangle } from 'react-icons/tb';\n\nconst QuizTimer = ({\n  duration, // in seconds\n  onTimeUp,\n  isActive = true,\n  showWarning = true,\n  warningThreshold = 300, // 5 minutes\n  className = '',\n}) => {\n  const [timeRemaining, setTimeRemaining] = useState(duration);\n  const [isWarning, setIsWarning] = useState(false);\n\n  useEffect(() => {\n    setTimeRemaining(duration);\n  }, [duration]);\n\n  useEffect(() => {\n    if (!isActive) return;\n\n    const interval = setInterval(() => {\n      setTimeRemaining((prev) => {\n        if (prev <= 1) {\n          onTimeUp?.();\n          return 0;\n        }\n        \n        const newTime = prev - 1;\n        \n        // Check if we should show warning\n        if (showWarning && newTime <= warningThreshold && !isWarning) {\n          setIsWarning(true);\n        }\n        \n        return newTime;\n      });\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, [isActive, onTimeUp, showWarning, warningThreshold, isWarning]);\n\n  const formatTime = (seconds) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getProgressPercentage = () => {\n    return ((duration - timeRemaining) / duration) * 100;\n  };\n\n  const getTimerColor = () => {\n    if (timeRemaining <= 60) return 'text-white'; // Last minute\n    if (timeRemaining <= warningThreshold) return 'text-white'; // Warning\n    return 'text-white'; // Normal\n  };\n\n  const getProgressColor = () => {\n    if (timeRemaining <= 60) return 'from-red-500 to-red-600';\n    if (timeRemaining <= warningThreshold) return 'from-yellow-500 to-yellow-600';\n    return 'from-primary-500 to-blue-500';\n  };\n\n  return (\n    <div className={`${className}`}>\n      {/* Compact Timer Display */}\n      <motion.div\n        animate={isWarning ? { scale: [1, 1.05, 1] } : {}}\n        transition={{ duration: 1, repeat: isWarning ? Infinity : 0 }}\n        className={`inline-flex items-center space-x-3 px-6 py-3 rounded-xl shadow-lg border-2 ${\n          timeRemaining <= 60\n            ? 'bg-gradient-to-r from-red-600 to-red-700 border-red-300 text-red-50'\n            : timeRemaining <= warningThreshold\n            ? 'bg-gradient-to-r from-yellow-500 to-orange-500 border-yellow-300 text-yellow-50'\n            : 'bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-300 text-blue-50'\n        }`}\n        style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}\n      >\n        {timeRemaining <= warningThreshold && (\n          <motion.div\n            animate={{ rotate: [0, 10, -10, 0] }}\n            transition={{ duration: 0.5, repeat: Infinity }}\n          >\n            <TbAlertTriangle className={`w-5 h-5 drop-shadow-md ${\n              timeRemaining <= 60 ? 'text-red-100' : 'text-yellow-100'\n            }`} />\n          </motion.div>\n        )}\n\n        <TbClock className={`w-5 h-5 drop-shadow-md ${\n          timeRemaining <= 60\n            ? 'text-red-100'\n            : timeRemaining <= warningThreshold\n            ? 'text-yellow-100'\n            : 'text-blue-100'\n        }`} />\n\n        <div className=\"text-center\">\n          <div className={`text-xs font-semibold opacity-90 mb-1 ${\n            timeRemaining <= 60\n              ? 'text-red-100'\n              : timeRemaining <= warningThreshold\n              ? 'text-yellow-100'\n              : 'text-blue-100'\n          }`}>TIME</div>\n          <span className={`font-mono font-black text-lg ${\n            timeRemaining <= 60\n              ? 'text-red-50'\n              : timeRemaining <= warningThreshold\n              ? 'text-yellow-50'\n              : 'text-blue-50'\n          }`} style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.7)' }}>\n            {formatTime(timeRemaining)}\n          </span>\n        </div>\n      </motion.div>\n\n      {/* Progress Bar */}\n      <div className=\"mt-3 w-full bg-gray-300 rounded-full h-2 overflow-hidden shadow-inner\">\n        <motion.div\n          initial={{ width: 0 }}\n          animate={{ width: `${getProgressPercentage()}%` }}\n          transition={{ duration: 0.5 }}\n          className={`h-full bg-gradient-to-r ${getProgressColor()} rounded-full shadow-sm`}\n        />\n      </div>\n\n      {/* Warning Message */}\n      {isWarning && timeRemaining > 60 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-3 text-sm font-semibold bg-yellow-100 text-yellow-800 px-3 py-2 rounded-lg border border-yellow-300\"\n        >\n          ⚠️ {Math.floor(timeRemaining / 60)} minutes remaining\n        </motion.div>\n      )}\n\n      {/* Critical Warning */}\n      {timeRemaining <= 60 && timeRemaining > 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-3 text-sm font-bold bg-red-100 text-red-800 px-3 py-2 rounded-lg border border-red-300\"\n        >\n          🚨 Less than 1 minute left!\n        </motion.div>\n      )}\n\n      {/* Time's Up */}\n      {timeRemaining === 0 && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          className=\"mt-3 text-sm font-black bg-red-200 text-red-900 px-3 py-2 rounded-lg border border-red-400\"\n        >\n          ⏰ Time's up!\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\n// Full-screen timer overlay for critical moments\nexport const QuizTimerOverlay = ({ timeRemaining, onClose }) => {\n  if (timeRemaining > 10) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center\"\n    >\n      <motion.div\n        initial={{ scale: 0.8, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        className=\"bg-white rounded-2xl p-8 text-center shadow-2xl max-w-sm mx-4\"\n      >\n        <motion.div\n          animate={{ scale: [1, 1.2, 1] }}\n          transition={{ duration: 1, repeat: Infinity }}\n          className=\"text-6xl mb-4\"\n        >\n          ⏰\n        </motion.div>\n        \n        <h3 className=\"text-2xl font-bold text-red-600 mb-2\">\n          Time Almost Up!\n        </h3>\n        \n        <motion.div\n          animate={{ scale: [1, 1.1, 1] }}\n          transition={{ duration: 0.5, repeat: Infinity }}\n          className=\"text-4xl font-mono font-bold text-red-600 mb-4\"\n        >\n          {timeRemaining}\n        </motion.div>\n        \n        <p className=\"text-gray-600 mb-4\">\n          Submit your answers now!\n        </p>\n        \n        <button\n          onClick={onClose}\n          className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\n        >\n          Continue Quiz\n        </button>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default QuizTimer;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js", ["717"], [], "import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbSun, TbMoon } from 'react-icons/tb';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nconst ThemeToggle = ({ className = '', size = 'md' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  const sizes = {\n    sm: 'w-8 h-8',\n    md: 'w-10 h-10',\n    lg: 'w-12 h-12',\n  };\n\n  const iconSizes = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6',\n  };\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      onClick={toggleTheme}\n      className={`\n        ${sizes[size]} \n        relative rounded-full p-2 \n        bg-gray-200 dark:bg-gray-700 \n        hover:bg-gray-300 dark:hover:bg-gray-600 \n        transition-all duration-300 \n        focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\n        ${className}\n      `}\n      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      <div className=\"relative w-full h-full flex items-center justify-center\">\n        <AnimatePresence mode=\"wait\" initial={false}>\n          {isDarkMode ? (\n            <motion.div\n              key=\"sun\"\n              initial={{ y: -20, opacity: 0, rotate: -90 }}\n              animate={{ y: 0, opacity: 1, rotate: 0 }}\n              exit={{ y: 20, opacity: 0, rotate: 90 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute\"\n            >\n              <TbSun className={`${iconSizes[size]} text-yellow-500`} />\n            </motion.div>\n          ) : (\n            <motion.div\n              key=\"moon\"\n              initial={{ y: -20, opacity: 0, rotate: -90 }}\n              animate={{ y: 0, opacity: 1, rotate: 0 }}\n              exit={{ y: 20, opacity: 0, rotate: 90 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute\"\n            >\n              <TbMoon className={`${iconSizes[size]} text-blue-600`} />\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.button>\n  );\n};\n\n// Advanced Theme Toggle with Switch Design\nexport const ThemeSwitch = ({ className = '' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={toggleTheme}\n      className={`\n        relative inline-flex h-6 w-11 items-center rounded-full \n        transition-colors duration-300 focus:outline-none focus:ring-2 \n        focus:ring-primary-500 focus:ring-offset-2\n        ${isDarkMode ? 'bg-primary-600' : 'bg-gray-200'}\n        ${className}\n      `}\n      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      <motion.span\n        layout\n        className={`\n          inline-block h-4 w-4 transform rounded-full bg-white shadow-lg \n          transition-transform duration-300 flex items-center justify-center\n          ${isDarkMode ? 'translate-x-6' : 'translate-x-1'}\n        `}\n      >\n        <motion.div\n          initial={false}\n          animate={{ rotate: isDarkMode ? 0 : 180 }}\n          transition={{ duration: 0.3 }}\n        >\n          {isDarkMode ? (\n            <TbMoon className=\"w-2.5 h-2.5 text-primary-600\" />\n          ) : (\n            <TbSun className=\"w-2.5 h-2.5 text-yellow-500\" />\n          )}\n        </motion.div>\n      </motion.span>\n    </motion.button>\n  );\n};\n\n// Theme Toggle with Label\nexport const ThemeToggleWithLabel = ({ className = '', showLabel = true }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <div className={`flex items-center space-x-3 ${className}`}>\n      {showLabel && (\n        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n          {isDarkMode ? 'Dark Mode' : 'Light Mode'}\n        </span>\n      )}\n      <ThemeSwitch />\n    </div>\n  );\n};\n\nexport default ThemeToggle;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js", ["718"], [], "import React from 'react';\nimport { motion } from 'framer-motion';\n\nconst ResponsiveContainer = ({ \n  children, \n  className = '', \n  maxWidth = '7xl',\n  padding = 'responsive',\n  ...props \n}) => {\n  const maxWidths = {\n    'sm': 'max-w-sm',\n    'md': 'max-w-md',\n    'lg': 'max-w-lg',\n    'xl': 'max-w-xl',\n    '2xl': 'max-w-2xl',\n    '3xl': 'max-w-3xl',\n    '4xl': 'max-w-4xl',\n    '5xl': 'max-w-5xl',\n    '6xl': 'max-w-6xl',\n    '7xl': 'max-w-7xl',\n    'full': 'max-w-full',\n  };\n\n  const paddings = {\n    'none': '',\n    'sm': 'px-4',\n    'md': 'px-6',\n    'lg': 'px-8',\n    'responsive': 'px-4 sm:px-6 lg:px-8',\n  };\n\n  return (\n    <div \n      className={`${maxWidths[maxWidth]} mx-auto ${paddings[padding]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Grid Component\nexport const ResponsiveGrid = ({ \n  children, \n  cols = { xs: 1, sm: 2, md: 3, lg: 4 },\n  gap = 6,\n  className = '',\n  ...props \n}) => {\n  const gridCols = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-2',\n    3: 'grid-cols-3',\n    4: 'grid-cols-4',\n    5: 'grid-cols-5',\n    6: 'grid-cols-6',\n  };\n\n  const gaps = {\n    2: 'gap-2',\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  const responsiveClasses = [\n    cols.xs && gridCols[cols.xs],\n    cols.sm && `sm:${gridCols[cols.sm]}`,\n    cols.md && `md:${gridCols[cols.md]}`,\n    cols.lg && `lg:${gridCols[cols.lg]}`,\n    cols.xl && `xl:${gridCols[cols.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div \n      className={`grid ${responsiveClasses} ${gaps[gap]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Text Component\nexport const ResponsiveText = ({ \n  children, \n  size = { xs: 'sm', sm: 'base', md: 'lg' },\n  weight = 'normal',\n  className = '',\n  ...props \n}) => {\n  const textSizes = {\n    'xs': 'text-xs',\n    'sm': 'text-sm',\n    'base': 'text-base',\n    'lg': 'text-lg',\n    'xl': 'text-xl',\n    '2xl': 'text-2xl',\n    '3xl': 'text-3xl',\n    '4xl': 'text-4xl',\n  };\n\n  const fontWeights = {\n    'light': 'font-light',\n    'normal': 'font-normal',\n    'medium': 'font-medium',\n    'semibold': 'font-semibold',\n    'bold': 'font-bold',\n  };\n\n  const responsiveClasses = [\n    size.xs && textSizes[size.xs],\n    size.sm && `sm:${textSizes[size.sm]}`,\n    size.md && `md:${textSizes[size.md]}`,\n    size.lg && `lg:${textSizes[size.lg]}`,\n    size.xl && `xl:${textSizes[size.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <span \n      className={`${responsiveClasses} ${fontWeights[weight]} ${className}`}\n      {...props}\n    >\n      {children}\n    </span>\n  );\n};\n\n// Mobile-First Responsive Component\nexport const MobileFirst = ({ children, className = '' }) => {\n  return (\n    <div className={`block lg:hidden ${className}`}>\n      {children}\n    </div>\n  );\n};\n\n// Desktop-First Responsive Component\nexport const DesktopFirst = ({ children, className = '' }) => {\n  return (\n    <div className={`hidden lg:block ${className}`}>\n      {children}\n    </div>\n  );\n};\n\n// Responsive Stack Component\nexport const ResponsiveStack = ({ \n  children, \n  direction = { xs: 'col', md: 'row' },\n  spacing = 4,\n  align = 'start',\n  justify = 'start',\n  className = '',\n  ...props \n}) => {\n  const directions = {\n    'row': 'flex-row',\n    'col': 'flex-col',\n    'row-reverse': 'flex-row-reverse',\n    'col-reverse': 'flex-col-reverse',\n  };\n\n  const spacings = {\n    2: 'gap-2',\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  const alignments = {\n    'start': 'items-start',\n    'center': 'items-center',\n    'end': 'items-end',\n    'stretch': 'items-stretch',\n  };\n\n  const justifications = {\n    'start': 'justify-start',\n    'center': 'justify-center',\n    'end': 'justify-end',\n    'between': 'justify-between',\n    'around': 'justify-around',\n    'evenly': 'justify-evenly',\n  };\n\n  const responsiveClasses = [\n    direction.xs && directions[direction.xs],\n    direction.sm && `sm:${directions[direction.sm]}`,\n    direction.md && `md:${directions[direction.md]}`,\n    direction.lg && `lg:${directions[direction.lg]}`,\n    direction.xl && `xl:${directions[direction.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div \n      className={`flex ${responsiveClasses} ${spacings[spacing]} ${alignments[align]} ${justifications[justify]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Show/Hide Component\nexport const ResponsiveShow = ({ \n  children, \n  breakpoint = 'md',\n  direction = 'up',\n  className = '' \n}) => {\n  const breakpoints = {\n    'sm': direction === 'up' ? 'sm:block' : 'sm:hidden',\n    'md': direction === 'up' ? 'md:block' : 'md:hidden',\n    'lg': direction === 'up' ? 'lg:block' : 'lg:hidden',\n    'xl': direction === 'up' ? 'xl:block' : 'xl:hidden',\n  };\n\n  const baseClass = direction === 'up' ? 'hidden' : 'block';\n\n  return (\n    <div className={`${baseClass} ${breakpoints[breakpoint]} ${className}`}>\n      {children}\n    </div>\n  );\n};\n\nexport default ResponsiveContainer;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js", ["719", "720"], [], "import React, { useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// Performance monitoring hook\nexport const usePerformanceMonitor = () => {\n  const [metrics, setMetrics] = useState({\n    loadTime: 0,\n    renderTime: 0,\n    memoryUsage: 0,\n    fps: 0,\n  });\n\n  useEffect(() => {\n    // Measure page load time\n    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;\n    \n    // Measure memory usage (if available)\n    const memoryUsage = performance.memory ? performance.memory.usedJSHeapSize / 1048576 : 0; // MB\n\n    setMetrics(prev => ({\n      ...prev,\n      loadTime,\n      memoryUsage,\n    }));\n\n    // FPS monitoring\n    let frameCount = 0;\n    let lastTime = performance.now();\n    \n    const measureFPS = () => {\n      frameCount++;\n      const currentTime = performance.now();\n      \n      if (currentTime >= lastTime + 1000) {\n        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));\n        setMetrics(prev => ({ ...prev, fps }));\n        frameCount = 0;\n        lastTime = currentTime;\n      }\n      \n      requestAnimationFrame(measureFPS);\n    };\n    \n    requestAnimationFrame(measureFPS);\n  }, []);\n\n  return metrics;\n};\n\n// Performance indicator component\nconst PerformanceIndicator = ({ show = false }) => {\n  const metrics = usePerformanceMonitor();\n  const [isVisible, setIsVisible] = useState(show);\n\n  useEffect(() => {\n    const handleKeyPress = (e) => {\n      if (e.ctrlKey && e.shiftKey && e.key === 'P') {\n        setIsVisible(!isVisible);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [isVisible]);\n\n  // Completely disable the performance indicator\n  return null;\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: 20 }}\n          className=\"fixed bottom-4 right-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs font-mono backdrop-blur-sm\"\n        >\n          <div className=\"space-y-1\">\n            <div className=\"font-bold text-green-400 mb-2\">Performance Metrics</div>\n            <div>Load Time: {metrics.loadTime}ms</div>\n            <div>Memory: {metrics.memoryUsage.toFixed(1)}MB</div>\n            <div>FPS: {metrics.fps}</div>\n            <div className=\"text-gray-400 mt-2 text-xs\">\n              Press Ctrl+Shift+P to toggle\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\n// Lazy loading wrapper with intersection observer\nexport const LazyWrapper = ({ children, threshold = 0.1, rootMargin = '50px' }) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [ref, setRef] = useState(null);\n\n  useEffect(() => {\n    if (!ref) return;\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n          observer.disconnect();\n        }\n      },\n      { threshold, rootMargin }\n    );\n\n    observer.observe(ref);\n    return () => observer.disconnect();\n  }, [ref, threshold, rootMargin]);\n\n  return (\n    <div ref={setRef}>\n      {isVisible ? children : <div className=\"h-32 bg-gray-100 animate-pulse rounded\" />}\n    </div>\n  );\n};\n\n// Optimized image component with WebP support\nexport const OptimizedImage = ({ \n  src, \n  webpSrc, \n  alt, \n  className = '',\n  loading = 'lazy',\n  ...props \n}) => {\n  const [imageError, setImageError] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  const handleError = () => {\n    setImageError(true);\n  };\n\n  const handleLoad = () => {\n    setIsLoaded(true);\n  };\n\n  return (\n    <div className={`relative overflow-hidden ${className}`}>\n      {!isLoaded && (\n        <div className=\"absolute inset-0 bg-gray-200 animate-pulse\" />\n      )}\n      \n      {!imageError ? (\n        <picture>\n          {webpSrc && <source srcSet={webpSrc} type=\"image/webp\" />}\n          <motion.img\n            src={src}\n            alt={alt}\n            loading={loading}\n            onError={handleError}\n            onLoad={handleLoad}\n            className={`w-full h-full object-cover transition-opacity duration-300 ${\n              isLoaded ? 'opacity-100' : 'opacity-0'\n            }`}\n            {...props}\n          />\n        </picture>\n      ) : (\n        <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n          <span className=\"text-gray-400 text-sm\">Image not available</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Debounced search hook\nexport const useDebouncedSearch = (searchTerm, delay = 300) => {\n  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedTerm(searchTerm);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [searchTerm, delay]);\n\n  return debouncedTerm;\n};\n\n// Virtual scrolling component for large lists\nexport const VirtualList = ({ \n  items, \n  itemHeight = 60, \n  containerHeight = 400,\n  renderItem,\n  className = '' \n}) => {\n  const [scrollTop, setScrollTop] = useState(0);\n  const [containerRef, setContainerRef] = useState(null);\n\n  const visibleStart = Math.floor(scrollTop / itemHeight);\n  const visibleEnd = Math.min(\n    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,\n    items.length\n  );\n\n  const visibleItems = items.slice(visibleStart, visibleEnd);\n  const totalHeight = items.length * itemHeight;\n  const offsetY = visibleStart * itemHeight;\n\n  const handleScroll = (e) => {\n    setScrollTop(e.target.scrollTop);\n  };\n\n  return (\n    <div\n      ref={setContainerRef}\n      className={`overflow-auto ${className}`}\n      style={{ height: containerHeight }}\n      onScroll={handleScroll}\n    >\n      <div style={{ height: totalHeight, position: 'relative' }}>\n        <div style={{ transform: `translateY(${offsetY}px)` }}>\n          {visibleItems.map((item, index) => (\n            <div key={visibleStart + index} style={{ height: itemHeight }}>\n              {renderItem(item, visibleStart + index)}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PerformanceIndicator;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js", ["721", "722", "723"], [], "import React from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  TbTrophy, \n  TbMedal, \n  TbCrown, \n  TbStar, \n  TbFlame, \n  TbTarget, \n  TbTrendingUp, \n  TbBolt,\n  TbAward,\n  TbDiamond\n} from 'react-icons/tb';\n\nconst AchievementBadge = ({ \n  achievement, \n  size = 'medium', \n  showDetails = true, \n  className = '',\n  onClick = null \n}) => {\n  // Achievement type configurations\n  const achievementConfig = {\n    first_quiz: {\n      icon: TbStar,\n      title: 'First Steps',\n      description: 'Completed your first quiz',\n      color: 'from-blue-400 to-blue-600',\n      bgColor: 'bg-blue-50',\n      textColor: 'text-blue-700'\n    },\n    perfect_score: {\n      icon: TbTrophy,\n      title: 'Perfect Score',\n      description: 'Achieved 100% on a quiz',\n      color: 'from-yellow-400 to-yellow-600',\n      bgColor: 'bg-yellow-50',\n      textColor: 'text-yellow-700'\n    },\n    streak_5: {\n      icon: TbFlame,\n      title: 'Hot Streak',\n      description: '5 correct answers in a row',\n      color: 'from-orange-400 to-red-500',\n      bgColor: 'bg-orange-50',\n      textColor: 'text-orange-700'\n    },\n    streak_10: {\n      icon: TbFlame,\n      title: 'Fire Streak',\n      description: '10 correct answers in a row',\n      color: 'from-red-400 to-red-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    streak_20: {\n      icon: TbFlame,\n      title: 'Blazing Streak',\n      description: '20 correct answers in a row',\n      color: 'from-red-500 to-purple-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    subject_master: {\n      icon: TbCrown,\n      title: 'Subject Master',\n      description: 'Mastered a subject',\n      color: 'from-purple-400 to-purple-600',\n      bgColor: 'bg-purple-50',\n      textColor: 'text-purple-700'\n    },\n    speed_demon: {\n      icon: TbBolt,\n      title: 'Speed Demon',\n      description: 'Completed quiz in record time',\n      color: 'from-cyan-400 to-blue-500',\n      bgColor: 'bg-cyan-50',\n      textColor: 'text-cyan-700'\n    },\n    consistent_learner: {\n      icon: TbTarget,\n      title: 'Consistent Learner',\n      description: 'Maintained consistent performance',\n      color: 'from-green-400 to-green-600',\n      bgColor: 'bg-green-50',\n      textColor: 'text-green-700'\n    },\n    improvement_star: {\n      icon: TbTrendingUp,\n      title: 'Improvement Star',\n      description: 'Showed remarkable improvement',\n      color: 'from-indigo-400 to-indigo-600',\n      bgColor: 'bg-indigo-50',\n      textColor: 'text-indigo-700'\n    }\n  };\n\n  // Size configurations\n  const sizeConfig = {\n    small: {\n      container: 'w-12 h-12',\n      icon: 'w-6 h-6',\n      text: 'text-xs',\n      padding: 'p-2'\n    },\n    medium: {\n      container: 'w-16 h-16',\n      icon: 'w-8 h-8',\n      text: 'text-sm',\n      padding: 'p-3'\n    },\n    large: {\n      container: 'w-20 h-20',\n      icon: 'w-10 h-10',\n      text: 'text-base',\n      padding: 'p-4'\n    }\n  };\n\n  const config = achievementConfig[achievement.type] || achievementConfig.first_quiz;\n  const sizes = sizeConfig[size];\n  const IconComponent = config.icon;\n\n  const badgeVariants = {\n    hidden: { opacity: 0, scale: 0.8, rotate: -10 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      rotate: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 20\n      }\n    },\n    hover: { \n      scale: 1.1, \n      rotate: 5,\n      transition: {\n        type: \"spring\",\n        stiffness: 400,\n        damping: 10\n      }\n    }\n  };\n\n  const glowVariants = {\n    hidden: { opacity: 0 },\n    visible: { \n      opacity: [0.5, 1, 0.5],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      variants={badgeVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      whileHover=\"hover\"\n      onClick={onClick}\n      className={`\n        relative cursor-pointer group\n        ${className}\n      `}\n    >\n      {/* Glow effect */}\n      <motion.div\n        variants={glowVariants}\n        className={`\n          absolute inset-0 rounded-full blur-md opacity-75\n          bg-gradient-to-r ${config.color}\n          ${sizes.container}\n        `}\n      />\n      \n      {/* Main badge */}\n      <div className={`\n        relative flex items-center justify-center rounded-full\n        bg-gradient-to-r ${config.color}\n        ${sizes.container} ${sizes.padding}\n        shadow-lg border-2 border-white\n        group-hover:shadow-xl transition-shadow duration-200\n      `}>\n        <IconComponent className={`${sizes.icon} text-white drop-shadow-sm`} />\n      </div>\n\n      {/* Achievement details tooltip */}\n      {showDetails && (\n        <motion.div\n          initial={{ opacity: 0, y: 10, scale: 0.9 }}\n          whileHover={{ opacity: 1, y: 0, scale: 1 }}\n          className={`\n            absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2\n            ${config.bgColor} ${config.textColor}\n            px-3 py-2 rounded-lg shadow-lg border\n            whitespace-nowrap z-10\n            pointer-events-none\n            ${sizes.text}\n          `}\n        >\n          <div className=\"font-semibold\">{config.title}</div>\n          <div className=\"text-xs opacity-75\">{config.description}</div>\n          {achievement.subject && (\n            <div className=\"text-xs font-medium mt-1\">\n              Subject: {achievement.subject}\n            </div>\n          )}\n          {achievement.earnedAt && (\n            <div className=\"text-xs opacity-60 mt-1\">\n              {new Date(achievement.earnedAt).toLocaleDateString()}\n            </div>\n          )}\n          \n          {/* Tooltip arrow */}\n          <div className={`\n            absolute top-full left-1/2 transform -translate-x-1/2\n            w-0 h-0 border-l-4 border-r-4 border-t-4\n            border-l-transparent border-r-transparent\n            ${config.bgColor.replace('bg-', 'border-t-')}\n          `} />\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\n// Achievement list component\nexport const AchievementList = ({\n  achievements = [],\n  maxDisplay = 5,\n  size = 'medium',\n  layout = 'horizontal', // 'horizontal' or 'grid'\n  className = ''\n}) => {\n  const displayAchievements = achievements.slice(0, maxDisplay);\n  const remainingCount = Math.max(0, achievements.length - maxDisplay);\n\n  // Size configurations for the list\n  const sizeConfig = {\n    small: {\n      container: 'w-12 h-12',\n      text: 'text-xs',\n      padding: 'p-2'\n    },\n    medium: {\n      container: 'w-16 h-16',\n      text: 'text-sm',\n      padding: 'p-3'\n    },\n    large: {\n      container: 'w-20 h-20',\n      text: 'text-base',\n      padding: 'p-4'\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const layoutClasses = layout === 'grid'\n    ? 'grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4'\n    : 'flex flex-wrap gap-2';\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      className={`${layoutClasses} ${className}`}\n    >\n      {displayAchievements.map((achievement, index) => (\n        <AchievementBadge\n          key={`${achievement.type}-${index}`}\n          achievement={achievement}\n          size={size}\n          showDetails={true}\n        />\n      ))}\n\n      {remainingCount > 0 && (\n        <motion.div\n          variants={{\n            hidden: { opacity: 0, scale: 0.8 },\n            visible: { opacity: 1, scale: 1 }\n          }}\n          className={`\n            flex items-center justify-center rounded-full\n            bg-gray-100 border-2 border-gray-200\n            ${sizeConfig[size]?.container} ${sizeConfig[size]?.padding}\n            text-gray-600 font-semibold\n            ${sizeConfig[size]?.text}\n          `}\n        >\n          +{remainingCount}\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\n// Achievement notification component\nexport const AchievementNotification = ({\n  achievement,\n  onClose,\n  autoClose = true,\n  duration = 4000\n}) => {\n  // Achievement type configurations for notifications\n  const achievementConfig = {\n    first_quiz: {\n      icon: TbStar,\n      title: 'First Steps',\n      description: 'Completed your first quiz',\n      color: 'from-blue-400 to-blue-600',\n      bgColor: 'bg-blue-50',\n      textColor: 'text-blue-700'\n    },\n    perfect_score: {\n      icon: TbTrophy,\n      title: 'Perfect Score',\n      description: 'Achieved 100% on a quiz',\n      color: 'from-yellow-400 to-yellow-600',\n      bgColor: 'bg-yellow-50',\n      textColor: 'text-yellow-700'\n    },\n    streak_5: {\n      icon: TbFlame,\n      title: 'Hot Streak',\n      description: '5 correct answers in a row',\n      color: 'from-orange-400 to-red-500',\n      bgColor: 'bg-orange-50',\n      textColor: 'text-orange-700'\n    },\n    streak_10: {\n      icon: TbFlame,\n      title: 'Fire Streak',\n      description: '10 correct answers in a row',\n      color: 'from-red-400 to-red-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    streak_20: {\n      icon: TbFlame,\n      title: 'Blazing Streak',\n      description: '20 correct answers in a row',\n      color: 'from-red-500 to-purple-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    subject_master: {\n      icon: TbCrown,\n      title: 'Subject Master',\n      description: 'Mastered a subject',\n      color: 'from-purple-400 to-purple-600',\n      bgColor: 'bg-purple-50',\n      textColor: 'text-purple-700'\n    },\n    speed_demon: {\n      icon: TbBolt,\n      title: 'Speed Demon',\n      description: 'Completed quiz in record time',\n      color: 'from-cyan-400 to-blue-500',\n      bgColor: 'bg-cyan-50',\n      textColor: 'text-cyan-700'\n    },\n    consistent_learner: {\n      icon: TbTarget,\n      title: 'Consistent Learner',\n      description: 'Maintained consistent performance',\n      color: 'from-green-400 to-green-600',\n      bgColor: 'bg-green-50',\n      textColor: 'text-green-700'\n    },\n    improvement_star: {\n      icon: TbTrendingUp,\n      title: 'Improvement Star',\n      description: 'Showed remarkable improvement',\n      color: 'from-indigo-400 to-indigo-600',\n      bgColor: 'bg-indigo-50',\n      textColor: 'text-indigo-700'\n    }\n  };\n\n  React.useEffect(() => {\n    if (autoClose && onClose) {\n      const timer = setTimeout(onClose, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [autoClose, duration, onClose]);\n\n  const config = achievementConfig[achievement.type] || achievementConfig.first_quiz;\n  const IconComponent = config.icon;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: -50, scale: 0.9 }}\n      animate={{ opacity: 1, y: 0, scale: 1 }}\n      exit={{ opacity: 0, y: -50, scale: 0.9 }}\n      className={`\n        fixed top-4 right-4 z-50\n        ${config.bgColor} border border-gray-200\n        rounded-lg shadow-lg p-4 max-w-sm\n      `}\n    >\n      <div className=\"flex items-center space-x-3\">\n        <div className={`\n          flex items-center justify-center w-12 h-12 rounded-full\n          bg-gradient-to-r ${config.color}\n        `}>\n          <IconComponent className=\"w-6 h-6 text-white\" />\n        </div>\n        \n        <div className=\"flex-1\">\n          <div className={`font-semibold ${config.textColor}`}>\n            Achievement Unlocked!\n          </div>\n          <div className=\"text-sm text-gray-600\">\n            {config.title}\n          </div>\n          <div className=\"text-xs text-gray-500\">\n            {config.description}\n          </div>\n        </div>\n        \n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            ×\n          </button>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\nexport default AchievementBadge;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js", ["724"], [], "import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Tb<PERSON>lam<PERSON>, TbTrophy, TbBolt } from 'react-icons/tb';\n\nconst XPProgressBar = ({\n  currentXP = 0,\n  totalXP = 0,\n  currentLevel = 1,\n  xpToNextLevel = 100,\n  showAnimation = true,\n  size = 'medium', // 'small', 'medium', 'large'\n  showLevel = true,\n  showXPNumbers = true,\n  className = ''\n}) => {\n  const [animatedXP, setAnimatedXP] = useState(0);\n  const [isLevelingUp, setIsLevelingUp] = useState(false);\n\n  // Calculate progress percentage\n  const xpForCurrentLevel = totalXP - xpToNextLevel;\n  const xpProgressInLevel = currentXP - xpForCurrentLevel;\n  const xpNeededForLevel = totalXP - xpForCurrentLevel;\n  const progressPercentage = Math.min(100, Math.max(0, (xpProgressInLevel / xpNeededForLevel) * 100));\n\n  // Size configurations\n  const sizeConfig = {\n    small: {\n      height: 'h-2',\n      levelSize: 'w-6 h-6 text-xs',\n      textSize: 'text-xs',\n      padding: 'px-2 py-1'\n    },\n    medium: {\n      height: 'h-3',\n      levelSize: 'w-8 h-8 text-sm',\n      textSize: 'text-sm',\n      padding: 'px-3 py-2'\n    },\n    large: {\n      height: 'h-4',\n      levelSize: 'w-10 h-10 text-base',\n      textSize: 'text-base',\n      padding: 'px-4 py-3'\n    }\n  };\n\n  const config = sizeConfig[size];\n\n  // Animate XP changes\n  useEffect(() => {\n    if (showAnimation) {\n      const timer = setTimeout(() => {\n        setAnimatedXP(currentXP);\n      }, 100);\n      return () => clearTimeout(timer);\n    } else {\n      setAnimatedXP(currentXP);\n    }\n  }, [currentXP, showAnimation]);\n\n  // Level up animation\n  const triggerLevelUpAnimation = () => {\n    setIsLevelingUp(true);\n    setTimeout(() => setIsLevelingUp(false), 2000);\n  };\n\n  // Get level color based on level\n  const getLevelColor = (level) => {\n    if (level >= 10) return 'from-purple-600 to-pink-600';\n    if (level >= 8) return 'from-yellow-500 to-orange-600';\n    if (level >= 6) return 'from-green-500 to-blue-600';\n    if (level >= 4) return 'from-blue-500 to-purple-600';\n    if (level >= 2) return 'from-indigo-500 to-blue-600';\n    return 'from-gray-500 to-gray-600';\n  };\n\n  // Get XP bar gradient based on progress\n  const getXPBarGradient = () => {\n    if (progressPercentage >= 90) return 'from-yellow-400 via-orange-500 to-red-500';\n    if (progressPercentage >= 70) return 'from-green-400 via-blue-500 to-purple-500';\n    if (progressPercentage >= 50) return 'from-blue-400 via-purple-500 to-pink-500';\n    if (progressPercentage >= 25) return 'from-indigo-400 via-blue-500 to-cyan-500';\n    return 'from-gray-400 via-gray-500 to-gray-600';\n  };\n\n  return (\n    <div className={`xp-progress-container ${className}`}>\n      {/* Level Up Animation Overlay */}\n      <AnimatePresence>\n        {isLevelingUp && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.5 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.5 }}\n            className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm\"\n          >\n            <motion.div\n              initial={{ y: -50, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              exit={{ y: 50, opacity: 0 }}\n              className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-8 py-6 rounded-2xl shadow-2xl text-center\"\n            >\n              <motion.div\n                animate={{ rotate: 360 }}\n                transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                className=\"text-4xl mb-2\"\n              >\n                🎉\n              </motion.div>\n              <h2 className=\"text-2xl font-bold mb-1\">LEVEL UP!</h2>\n              <p className=\"text-lg\">You reached Level {currentLevel}!</p>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      <div className=\"flex items-center space-x-3\">\n        {/* Level Badge */}\n        {showLevel && (\n          <motion.div\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.95 }}\n            className={`\n              ${config.levelSize} rounded-full flex items-center justify-center\n              bg-gradient-to-r ${getLevelColor(currentLevel)}\n              text-white font-bold shadow-lg border-2 border-white\n              relative overflow-hidden\n            `}\n          >\n            {/* Level glow effect */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\" />\n            \n            {/* Level number */}\n            <span className={`relative z-10 ${config.textSize}`}>\n              {currentLevel}\n            </span>\n\n            {/* Level icon for high levels */}\n            {currentLevel >= 10 && (\n              <TbTrophy className=\"absolute top-0 right-0 w-3 h-3 text-yellow-300\" />\n            )}\n          </motion.div>\n        )}\n\n        {/* XP Progress Bar Container */}\n        <div className=\"flex-1\">\n          {/* XP Numbers */}\n          {showXPNumbers && (\n            <div className={`flex justify-between items-center mb-1 ${config.textSize} text-gray-600`}>\n              <span className=\"font-medium\">\n                {animatedXP.toLocaleString()} XP\n              </span>\n              <span className=\"text-gray-500\">\n                {xpToNextLevel > 0 ? `${xpToNextLevel} to next level` : 'Max Level'}\n              </span>\n            </div>\n          )}\n\n          {/* Progress Bar */}\n          <div className={`\n            relative ${config.height} bg-gray-200 rounded-full overflow-hidden\n            shadow-inner border border-gray-300\n          `}>\n            {/* Background gradient */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-gray-100 to-gray-200\" />\n            \n            {/* Progress fill */}\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: `${progressPercentage}%` }}\n              transition={{ duration: 1, ease: \"easeOut\" }}\n              className={`\n                absolute inset-y-0 left-0 rounded-full\n                bg-gradient-to-r ${getXPBarGradient()}\n                shadow-lg relative overflow-hidden\n              `}\n            >\n              {/* Animated shine effect */}\n              <motion.div\n                animate={{ x: ['0%', '100%'] }}\n                transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform skew-x-12\"\n              />\n              \n              {/* Progress glow */}\n              <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\" />\n            </motion.div>\n\n            {/* XP gain animation particles */}\n            <AnimatePresence>\n              {showAnimation && (\n                <motion.div\n                  initial={{ opacity: 0, y: 0 }}\n                  animate={{ opacity: [0, 1, 0], y: -20 }}\n                  exit={{ opacity: 0 }}\n                  transition={{ duration: 1 }}\n                  className=\"absolute right-2 top-1/2 transform -translate-y-1/2\"\n                >\n                  <TbBolt className=\"w-4 h-4 text-yellow-400\" />\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n\n          {/* Level progress indicators */}\n          {xpToNextLevel > 0 && (\n            <div className=\"flex justify-between mt-1\">\n              <span className={`${config.textSize} text-gray-500 font-medium`}>\n                Level {currentLevel}\n              </span>\n              <span className={`${config.textSize} text-gray-500 font-medium`}>\n                Level {currentLevel + 1}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* XP Boost Indicator */}\n        {currentLevel > 1 && (\n          <motion.div\n            whileHover={{ scale: 1.1 }}\n            className=\"flex items-center space-x-1 bg-gradient-to-r from-yellow-100 to-orange-100 px-2 py-1 rounded-full border border-yellow-300\"\n          >\n            <TbFlame className=\"w-3 h-3 text-orange-500\" />\n            <span className=\"text-xs font-medium text-orange-700\">\n              +{((currentLevel - 1) * 10)}% XP\n            </span>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default XPProgressBar;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js", ["725"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js", ["726"], [], "import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Card, Row, Col, Statistic, Progress } from 'antd';\nimport {\n  TbUsers,\n  TbBook,\n  TbFileText,\n  TbChartBar,\n  TbTrendingUp,\n  TbTarget,\n  TbAward,\n  TbClock\n} from 'react-icons/tb';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReports } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\n\nconst AdminDashboard = () => {\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    totalExams: 0,\n    totalReports: 0,\n    averageScore: 0,\n    completionRate: 0\n  });\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      dispatch(ShowLoading());\n\n      // Fetch users data\n      const usersResponse = await getAllUsers();\n      const users = usersResponse.success ? usersResponse.users : [];\n\n      // Fetch exams data\n      const examsResponse = await getAllExams();\n      const exams = examsResponse.success ? examsResponse.data : [];\n\n      // Fetch reports data (with empty filters to get all reports)\n      const reportsResponse = await getAllReports({ examName: '', userName: '', page: 1, limit: 1000 });\n      const reports = reportsResponse.success ? reportsResponse.data : [];\n\n      // Calculate statistics\n      const totalUsers = users.length;\n      const activeUsers = users.filter(u => !u.isBlocked).length;\n      const totalExams = exams.length;\n      const totalReports = reports.length;\n      \n      // Calculate average score from reports\n      const averageScore = reports.length > 0 \n        ? reports.reduce((sum, report) => sum + (report.percentage || 0), 0) / reports.length\n        : 0;\n      \n      // Calculate completion rate\n      const completionRate = totalUsers > 0 ? (totalReports / totalUsers) * 100 : 0;\n\n      setStats({\n        totalUsers,\n        activeUsers,\n        totalExams,\n        totalReports,\n        averageScore: Math.round(averageScore),\n        completionRate: Math.round(completionRate)\n      });\n\n      dispatch(HideLoading());\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      dispatch(HideLoading());\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n\n  const quickActions = [\n    {\n      title: 'Manage Users',\n      description: 'View and manage student accounts',\n      icon: TbUsers,\n      path: '/admin/users',\n      color: 'bg-blue-500'\n    },\n    {\n      title: 'Create Exam',\n      description: 'Add new exams and questions',\n      icon: TbFileText,\n      path: '/admin/exams/add',\n      color: 'bg-green-500'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Upload learning resources',\n      icon: TbBook,\n      path: '/admin/study-materials',\n      color: 'bg-purple-500'\n    },\n    {\n      title: 'View Reports',\n      description: 'Analyze student performance',\n      icon: TbChartBar,\n      path: '/admin/reports',\n      color: 'bg-orange-500'\n    }\n  ];\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      className=\"p-6 max-w-7xl mx-auto\"\n    >\n      {/* Welcome Header */}\n      <motion.div variants={itemVariants} className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Welcome back, {user?.name}! 👋\n        </h1>\n        <p className=\"text-gray-600\">\n          Here's what's happening with your educational platform today.\n        </p>\n      </motion.div>\n\n      {/* Statistics Cards */}\n      <motion.div variants={itemVariants} className=\"mb-8\">\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"h-full\">\n              <Statistic\n                title=\"Total Students\"\n                value={stats.totalUsers}\n                prefix={<TbUsers className=\"text-blue-500\" />}\n                valueStyle={{ color: '#1890ff' }}\n              />\n              <div className=\"mt-2\">\n                <span className=\"text-green-500 text-sm\">\n                  {stats.activeUsers} active\n                </span>\n              </div>\n            </Card>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"h-full\">\n              <Statistic\n                title=\"Total Exams\"\n                value={stats.totalExams}\n                prefix={<TbFileText className=\"text-green-500\" />}\n                valueStyle={{ color: '#52c41a' }}\n              />\n            </Card>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"h-full\">\n              <Statistic\n                title=\"Exam Attempts\"\n                value={stats.totalReports}\n                prefix={<TbTarget className=\"text-orange-500\" />}\n                valueStyle={{ color: '#fa8c16' }}\n              />\n            </Card>\n          </Col>\n          \n          <Col xs={24} sm={12} lg={6}>\n            <Card className=\"h-full\">\n              <Statistic\n                title=\"Average Score\"\n                value={stats.averageScore}\n                suffix=\"%\"\n                prefix={<TbAward className=\"text-purple-500\" />}\n                valueStyle={{ color: '#722ed1' }}\n              />\n            </Card>\n          </Col>\n        </Row>\n      </motion.div>\n\n      {/* Performance Overview */}\n      <motion.div variants={itemVariants} className=\"mb-8\">\n        <Row gutter={[16, 16]}>\n          <Col xs={24} lg={12}>\n            <Card title=\"Student Engagement\" className=\"h-full\">\n              <div className=\"space-y-4\">\n                <div>\n                  <div className=\"flex justify-between mb-2\">\n                    <span>Active Students</span>\n                    <span>{Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0}%</span>\n                  </div>\n                  <Progress \n                    percent={Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0} \n                    strokeColor=\"#52c41a\"\n                  />\n                </div>\n                \n                <div>\n                  <div className=\"flex justify-between mb-2\">\n                    <span>Exam Completion Rate</span>\n                    <span>{stats.completionRate}%</span>\n                  </div>\n                  <Progress \n                    percent={stats.completionRate} \n                    strokeColor=\"#1890ff\"\n                  />\n                </div>\n              </div>\n            </Card>\n          </Col>\n          \n          <Col xs={24} lg={12}>\n            <Card title=\"Quick Actions\" className=\"h-full\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                {quickActions.map((action, index) => {\n                  const IconComponent = action.icon;\n                  return (\n                    <motion.div\n                      key={action.title}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-all\"\n                      onClick={() => window.location.href = action.path}\n                    >\n                      <div className={`w-8 h-8 ${action.color} rounded-lg flex items-center justify-center mb-2`}>\n                        <IconComponent className=\"w-5 h-5 text-white\" />\n                      </div>\n                      <h4 className=\"font-medium text-sm mb-1\">{action.title}</h4>\n                      <p className=\"text-xs text-gray-500\">{action.description}</p>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </Card>\n          </Col>\n        </Row>\n      </motion.div>\n\n      {/* Recent Activity */}\n      <motion.div variants={itemVariants}>\n        <Card title=\"System Status\" className=\"mb-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2\">\n                <TbTrendingUp className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <h4 className=\"font-medium\">System Health</h4>\n              <p className=\"text-green-600 text-sm\">All systems operational</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2\">\n                <TbClock className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <h4 className=\"font-medium\">Last Updated</h4>\n              <p className=\"text-gray-600 text-sm\">Just now</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2\">\n                <TbChartBar className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <h4 className=\"font-medium\">Data Sync</h4>\n              <p className=\"text-purple-600 text-sm\">Synchronized</p>\n            </div>\n          </div>\n        </Card>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default AdminDashboard;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js", ["727"], [], "import React, { useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\n\nconst AdminProtectedRoute = ({ children }) => {\n  const { user } = useSelector((state) => state.user);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    // Check if user is loaded and is not an admin\n    if (user && !user.isAdmin) {\n      message.error('Access denied. Admin privileges required.');\n      navigate('/user/hub');\n    }\n  }, [user, navigate]);\n\n  // If user is not loaded yet, show loading or return null\n  if (!user) {\n    return null;\n  }\n\n  // If user is not admin, return null (will redirect in useEffect)\n  if (!user.isAdmin) {\n    return null;\n  }\n\n  // If user is admin, render the children\n  return children;\n};\n\nexport default AdminProtectedRoute;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js", ["728"], [], "import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport {\n  TbMenu2,\n  TbX,\n  TbHome,\n  TbBrain,\n  TbBook,\n  TbRobot,\n  TbChartLine,\n  TbTrophy,\n  TbUser,\n  TbMessageCircle,\n  TbCreditCard,\n  TbLogout,\n  TbChevronRight\n} from 'react-icons/tb';\n\nconst ModernSidebar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user } = useSelector((state) => state.user);\n\n  // Handle keyboard events\n  useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === 'Escape' && isOpen) {\n        setIsOpen(false);\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when sidebar is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  const navigationItems = [\n    {\n      title: 'Hub',\n      description: 'Main dashboard',\n      icon: TbHome,\n      path: '/user/hub',\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: TbBrain,\n      path: '/user/quiz',\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, videos & notes',\n      icon: TbBook,\n      path: '/user/study-material',\n      color: 'from-green-500 to-green-600'\n    },\n    {\n      title: 'Ask AI',\n      description: 'Get instant help',\n      icon: TbRobot,\n      path: '/user/chat',\n      color: 'from-orange-500 to-orange-600'\n    },\n    {\n      title: 'Reports',\n      description: 'Track progress',\n      icon: TbChartLine,\n      path: '/user/reports',\n      color: 'from-red-500 to-red-600'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: TbTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage account',\n      icon: TbUser,\n      path: '/profile',\n      color: 'from-indigo-500 to-indigo-600'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: TbMessageCircle,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600'\n    },\n    {\n      title: 'Plans',\n      description: 'Upgrade learning',\n      icon: TbCreditCard,\n      path: '/user/plans',\n      color: 'from-emerald-500 to-emerald-600'\n    },\n    {\n      title: 'Logout',\n      description: 'Sign out of account',\n      icon: TbLogout,\n      path: 'logout',\n      color: 'from-red-500 to-red-600'\n    }\n  ];\n\n  const handleNavigation = (path) => {\n    if (path === 'logout') {\n      handleLogout();\n    } else {\n      navigate(path);\n    }\n    setIsOpen(false);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    navigate(\"/login\");\n  };\n\n  const isActivePath = (path) => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n\n  return (\n    <>\n      {/* Toggle Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"fixed top-4 left-4 z-50 p-3 bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105\"\n        title={isOpen ? \"Close Menu\" : \"Open Menu\"}\n      >\n        {isOpen ? (\n          <TbX className=\"w-6 h-6 text-gray-700\" />\n        ) : (\n          <TbMenu2 className=\"w-6 h-6 text-gray-700\" />\n        )}\n      </button>\n\n      {/* Backdrop */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={() => setIsOpen(false)}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ x: -400, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            exit={{ x: -400, opacity: 0 }}\n            transition={{ type: \"spring\", damping: 25, stiffness: 200 }}\n            className=\"fixed left-0 top-0 h-full w-80 sm:w-80 md:w-80 lg:w-80 max-w-[90vw] bg-white shadow-2xl z-50 flex flex-col\"\n          >\n            {/* Header */}\n            <div className=\"p-4 sm:p-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white relative\">\n              {/* Close Button */}\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"absolute top-4 right-4 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200\"\n                title=\"Close Menu\"\n              >\n                <TbX className=\"w-5 h-5 text-white\" />\n              </button>\n\n              <div className=\"text-center pr-12\">\n                <h1 className=\"text-xl sm:text-2xl font-bold mb-2\">Navigation</h1>\n                <p className=\"text-blue-200 text-xs sm:text-sm\">Choose your destination</p>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <div className=\"flex-1 p-4 space-y-2 overflow-y-auto\">\n              {navigationItems.map((item, index) => {\n                const IconComponent = item.icon;\n                const isActive = item.path !== 'logout' && isActivePath(item.path);\n                const isLogout = item.path === 'logout';\n\n                return (\n                  <motion.button\n                    key={item.path}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.05 }}\n                    onClick={() => handleNavigation(item.path)}\n                    className={`w-full flex items-center justify-between p-3 sm:p-4 rounded-xl transition-all duration-200 ${\n                      isActive\n                        ? 'bg-blue-50 border-2 border-blue-200 shadow-md'\n                        : isLogout\n                        ? 'hover:bg-red-50 border-2 border-transparent'\n                        : 'hover:bg-gray-50 border-2 border-transparent'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`}>\n                        <IconComponent className=\"w-5 h-5 text-white\" />\n                      </div>\n                      <div className=\"text-left\">\n                        <p className={`font-medium ${\n                          isActive\n                            ? 'text-blue-700'\n                            : isLogout\n                            ? 'text-red-700'\n                            : 'text-gray-900'\n                        }`}>\n                          {item.title}\n                        </p>\n                        <p className={`text-sm ${\n                          isActive\n                            ? 'text-blue-600'\n                            : isLogout\n                            ? 'text-red-600'\n                            : 'text-gray-500'\n                        }`}>\n                          {item.description}\n                        </p>\n                      </div>\n                    </div>\n                    <TbChevronRight className={`w-5 h-5 ${\n                      isActive\n                        ? 'text-blue-600'\n                        : isLogout\n                        ? 'text-red-400'\n                        : 'text-gray-400'\n                    }`} />\n                  </motion.button>\n                );\n              })}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default ModernSidebar;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\ModernQuizPage.js", ["729", "730"], [], "import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { \n  TbBrain,\n  TbTrophy,\n  TbClock,\n  TbStar,\n  TbBook,\n  TbChevronRight\n} from 'react-icons/tb';\nimport QuizDashboard from '../../../components/modern/QuizDashboard';\nimport QuizInterface from '../../../components/modern/QuizInterface';\nimport { getAllQuizzes, getQuizById, submitQuizResult, getUserResults } from '../../../apicalls/quiz';\nimport Loading from '../../../components/modern/Loading';\n\nconst ModernQuizPage = () => {\n  const navigate = useNavigate();\n  const [currentView, setCurrentView] = useState('dashboard'); // 'dashboard', 'quiz', 'result'\n  const [quizzes, setQuizzes] = useState([]);\n  const [userResults, setUserResults] = useState({});\n  const [currentQuiz, setCurrentQuiz] = useState(null);\n  const [currentQuestions, setCurrentQuestions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [quizLoading, setQuizLoading] = useState(false);\n\n  // Load initial data\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // Load quizzes and user results in parallel\n      const [quizzesResponse, resultsResponse] = await Promise.all([\n        getAllQuizzes(),\n        getUserResults()\n      ]);\n\n      if (quizzesResponse.success) {\n        setQuizzes(quizzesResponse.data || []);\n      } else {\n        // Fallback to demo data if API fails\n        setQuizzes([\n          {\n            _id: 'demo1',\n            name: 'Mathematics Quiz - Algebra Basics',\n            subject: 'Mathematics',\n            duration: 30,\n            questions: Array(15).fill({}),\n            xpPoints: 150,\n            passingMarks: 60,\n            class: '7',\n            category: 'Practice Test'\n          },\n          {\n            _id: 'demo2',\n            name: 'Science Quiz - Physics Fundamentals',\n            subject: 'Physics',\n            duration: 45,\n            questions: Array(20).fill({}),\n            xpPoints: 200,\n            passingMarks: 70,\n            class: '8',\n            category: 'Chapter Test'\n          },\n          {\n            _id: 'demo3',\n            name: 'English Grammar and Comprehension',\n            subject: 'English',\n            duration: 25,\n            questions: Array(12).fill({}),\n            xpPoints: 120,\n            passingMarks: 60,\n            class: '7',\n            category: 'Weekly Test'\n          },\n          {\n            _id: 'demo4',\n            name: 'Chemistry - Periodic Table',\n            subject: 'Chemistry',\n            duration: 35,\n            questions: Array(18).fill({}),\n            xpPoints: 180,\n            passingMarks: 65,\n            class: '9',\n            category: 'Unit Test'\n          },\n          {\n            _id: 'demo5',\n            name: 'History - World War II',\n            subject: 'History',\n            duration: 40,\n            questions: Array(16).fill({}),\n            xpPoints: 160,\n            passingMarks: 60,\n            class: '10',\n            category: 'Chapter Test'\n          }\n        ]);\n      }\n\n      if (resultsResponse.success) {\n        // Convert results array to object with quiz ID as key\n        const resultsMap = {};\n        (resultsResponse.data || []).forEach(result => {\n          resultsMap[result.quiz] = result;\n        });\n        setUserResults(resultsMap);\n      } else {\n        // Demo results\n        setUserResults({\n          'demo1': {\n            percentage: 85,\n            correctAnswers: 13,\n            totalQuestions: 15,\n            xpEarned: 150,\n            completedAt: new Date().toISOString()\n          },\n          'demo2': {\n            percentage: 45,\n            correctAnswers: 9,\n            totalQuestions: 20,\n            xpEarned: 0,\n            completedAt: new Date(Date.now() - 86400000).toISOString() // Yesterday\n          }\n        });\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n      // Set demo data on error\n      setQuizzes([\n        {\n          _id: 'demo1',\n          name: 'Mathematics Quiz - Algebra Basics',\n          subject: 'Mathematics',\n          duration: 30,\n          questions: Array(15).fill({}),\n          xpPoints: 150,\n          passingMarks: 60,\n          class: '7',\n          category: 'Practice Test'\n        },\n        {\n          _id: 'demo2',\n          name: 'Science Quiz - Physics Fundamentals',\n          subject: 'Physics',\n          duration: 45,\n          questions: Array(20).fill({}),\n          xpPoints: 200,\n          passingMarks: 70,\n          class: '8',\n          category: 'Chapter Test'\n        },\n        {\n          _id: 'demo3',\n          name: 'English Grammar Test',\n          subject: 'English',\n          duration: 25,\n          questions: Array(12).fill({}),\n          xpPoints: 120,\n          passingMarks: 60,\n          class: '7',\n          category: 'Weekly Test'\n        }\n      ]);\n      setUserResults({\n        'demo1': {\n          percentage: 85,\n          correctAnswers: 13,\n          totalQuestions: 15,\n          xpEarned: 150,\n          completedAt: new Date().toISOString()\n        },\n        'demo3': {\n          percentage: 75,\n          correctAnswers: 9,\n          totalQuestions: 12,\n          xpEarned: 120,\n          completedAt: new Date(Date.now() - 172800000).toISOString() // 2 days ago\n        }\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle quiz start\n  const handleQuizStart = async (quiz) => {\n    try {\n      setQuizLoading(true);\n\n      // Get full quiz details with questions\n      const response = await getQuizById(quiz._id);\n\n      if (response.success && response.data) {\n        setCurrentQuiz(response.data);\n        setCurrentQuestions(response.data.questions || []);\n        setCurrentView('quiz');\n      } else {\n        // Demo questions for testing\n        const demoQuestions = [\n          {\n            _id: 'q1',\n            name: 'What is the value of x in the equation 2x + 5 = 15?',\n            type: 'Options',\n            answerType: 'Options',\n            questionType: 'Options',\n            options: {\n              A: 'x = 5',\n              B: 'x = 10',\n              C: 'x = 7.5',\n              D: 'x = 2.5'\n            },\n            correctAnswer: 'A',\n            correctOption: 'A'\n          },\n          {\n            _id: 'q2',\n            name: 'Solve for y: 3y - 7 = 14',\n            type: 'Options',\n            answerType: 'Options',\n            questionType: 'Options',\n            options: {\n              A: 'y = 7',\n              B: 'y = 21',\n              C: 'y = 5',\n              D: 'y = 3'\n            },\n            correctAnswer: 'A',\n            correctOption: 'A'\n          },\n          {\n            _id: 'q3',\n            name: 'What is the chemical symbol for Gold?',\n            type: 'Fill in the Blank',\n            answerType: 'Fill in the Blank',\n            questionType: 'Fill in the Blank',\n            options: {},\n            correctAnswer: 'Au',\n            inputType: 'short' // For short answers\n          },\n          {\n            _id: 'q4',\n            name: 'Which planet is known as the Red Planet?',\n            type: 'Options',\n            answerType: 'Options',\n            questionType: 'Options',\n            options: {\n              A: 'Mars',\n              B: 'Venus',\n              C: 'Jupiter',\n              D: 'Saturn'\n            },\n            correctAnswer: 'A',\n            correctOption: 'A'\n          },\n          {\n            _id: 'q5',\n            name: 'Explain the process of photosynthesis in plants.',\n            type: 'Fill in the Blank',\n            answerType: 'Fill in the Blank',\n            questionType: 'Fill in the Blank',\n            options: {},\n            correctAnswer: 'Photosynthesis is the process by which plants convert sunlight, carbon dioxide, and water into glucose and oxygen.',\n            inputType: 'long' // For longer answers\n          },\n          {\n            _id: 'q6',\n            name: 'What geometric shape is shown in the image below?',\n            type: 'picture_based',\n            answerType: 'Options',\n            questionType: 'picture_based',\n            imageUrl: 'https://picsum.photos/300/200?random=1',\n            image: 'https://picsum.photos/300/200?random=1',\n            options: {\n              A: 'Triangle',\n              B: 'Square',\n              C: 'Circle',\n              D: 'Rectangle'\n            },\n            correctAnswer: 'A',\n            correctOption: 'A'\n          }\n        ];\n\n        // Validate demo questions before setting\n        const validatedQuestions = demoQuestions.filter(q => q && q._id && q.name);\n        console.log('Setting demo questions:', validatedQuestions);\n\n        setCurrentQuiz(quiz);\n        setCurrentQuestions(validatedQuestions);\n        setCurrentView('quiz');\n      }\n    } catch (error) {\n      console.error('Error starting quiz:', error);\n      // Demo questions on error\n      const demoQuestions = [\n        {\n          _id: 'q1',\n          name: 'What is the value of x in the equation 2x + 5 = 15?',\n          type: 'Options',\n          answerType: 'Options',\n          questionType: 'Options',\n          options: {\n            A: 'x = 5',\n            B: 'x = 10',\n            C: 'x = 7.5',\n            D: 'x = 2.5'\n          },\n          correctAnswer: 'A',\n          correctOption: 'A'\n        },\n        {\n          _id: 'q2',\n          name: 'What is the capital of France?',\n          type: 'Fill in the Blank',\n          answerType: 'Fill in the Blank',\n          questionType: 'Fill in the Blank',\n          options: {},\n          correctAnswer: 'Paris'\n        }\n      ];\n\n      // Validate demo questions before setting\n      const validatedQuestions = demoQuestions.filter(q => q && q._id && q.name);\n      console.log('Setting error demo questions:', validatedQuestions);\n\n      setCurrentQuiz(quiz);\n      setCurrentQuestions(validatedQuestions);\n      setCurrentView('quiz');\n    } finally {\n      setQuizLoading(false);\n    }\n  };\n\n  // Handle quiz submission\n  const handleQuizSubmit = async (answers) => {\n    try {\n      setQuizLoading(true);\n      \n      // Prepare submission data\n      const submissionData = {\n        quizId: currentQuiz._id,\n        answers: Object.entries(answers).map(([questionId, answer]) => ({\n          questionId,\n          answer\n        }))\n      };\n\n      const response = await submitQuizResult(submissionData);\n      \n      if (response.success) {\n        message.success('Quiz submitted successfully!');\n        \n        // Refresh dashboard data to show updated results\n        await loadDashboardData();\n        \n        // Navigate to results page\n        navigate(`/quiz/${currentQuiz._id}/result`);\n      } else {\n        message.error(response.message || 'Failed to submit quiz');\n      }\n    } catch (error) {\n      console.error('Error submitting quiz:', error);\n      message.error('Failed to submit quiz');\n    } finally {\n      setQuizLoading(false);\n    }\n  };\n\n  // Handle back to dashboard\n  const handleBackToDashboard = () => {\n    setCurrentView('dashboard');\n    setCurrentQuiz(null);\n    setCurrentQuestions([]);\n  };\n\n  // Render loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <Loading size=\"lg\" />\n          <p className=\"mt-4 text-gray-600\">Loading your quizzes...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Render quiz interface\n  if (currentView === 'quiz' && currentQuiz) {\n    return (\n      <div className=\"relative\">\n        {quizLoading && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n            <div className=\"bg-white rounded-lg p-6 text-center\">\n              <Loading size=\"lg\" />\n              <p className=\"mt-4 text-gray-600\">Processing your quiz...</p>\n            </div>\n          </div>\n        )}\n        \n        {/* Add error boundary around QuizInterface */}\n        {Array.isArray(currentQuestions) && currentQuestions.length > 0 ? (\n          <QuizInterface\n            quiz={currentQuiz}\n            questions={currentQuestions}\n            onSubmit={handleQuizSubmit}\n            onExit={handleBackToDashboard}\n          />\n        ) : (\n          <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n            <div className=\"text-center\">\n              <div className=\"text-6xl mb-4\">📝</div>\n              <h2 className=\"text-xl font-bold text-gray-900 mb-2\">No Questions Available</h2>\n              <p className=\"text-gray-600 mb-4\">This quiz doesn't have any questions yet.</p>\n              <button\n                onClick={handleBackToDashboard}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Back to Dashboard\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  // Render dashboard\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Hero Section */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center\"\n          >\n            <div className=\"flex items-center justify-center mb-6\">\n              <TbBrain className=\"w-16 h-16 text-blue-200 mr-4\" />\n              <h1 className=\"text-4xl lg:text-6xl font-bold\">\n                Brain<span className=\"text-blue-200\">Wave</span>\n              </h1>\n            </div>\n            <p className=\"text-xl lg:text-2xl text-blue-100 mb-8\">\n              Challenge your brain, Beat the rest\n            </p>\n            <div className=\"flex items-center justify-center gap-8 text-blue-100\">\n              <div className=\"flex items-center gap-2\">\n                <TbTrophy className=\"w-6 h-6\" />\n                <span>Track Progress</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <TbStar className=\"w-6 h-6\" />\n                <span>Earn XP</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <TbBook className=\"w-6 h-6\" />\n                <span>Learn & Grow</span>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Dashboard */}\n      <QuizDashboard\n        quizzes={quizzes}\n        userResults={userResults}\n        onQuizStart={handleQuizStart}\n        loading={loading}\n      />\n\n      {/* Quick Stats Footer */}\n      <div className=\"bg-white border-t\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\">\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"p-6 bg-blue-50 rounded-xl\"\n            >\n              <TbBrain className=\"w-12 h-12 text-blue-600 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Smart Learning</h3>\n              <p className=\"text-gray-600\">AI-powered questions adapted to your learning pace</p>\n            </motion.div>\n            \n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"p-6 bg-green-50 rounded-xl\"\n            >\n              <TbTrophy className=\"w-12 h-12 text-green-600 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Track Progress</h3>\n              <p className=\"text-gray-600\">Monitor your improvement with detailed analytics</p>\n            </motion.div>\n            \n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"p-6 bg-purple-50 rounded-xl\"\n            >\n              <TbStar className=\"w-12 h-12 text-purple-600 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Earn Rewards</h3>\n              <p className=\"text-gray-600\">Collect XP points and unlock achievements</p>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ModernQuizPage;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizDashboard.js", ["731", "732", "733", "734"], [], "import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport ModernQuizCard from './ModernQuizCard';\nimport {\n  TbSearch,\n  TbFilter,\n  TbGridDots,\n  Tb<PERSON>ist,\n  TbSortAscending,\n  TbBook,\n  TbClock,\n  TbStar,\n} from 'react-icons/tb';\n\nconst QuizDashboard = ({\n  quizzes = [],\n  userResults = {},\n  onQuizStart,\n  loading = false,\n  className = ''\n}) => {\n  const { user } = useSelector((state) => state.user);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [viewMode, setViewMode] = useState('grid');\n\n  // Set default class filter to user's class\n  useEffect(() => {\n    if (user?.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Get unique subjects and classes from quizzes\n  const subjects = [...new Set(quizzes.map(quiz => quiz.subject).filter(Boolean))];\n  const classes = [...new Set(quizzes.map(quiz => quiz.class).filter(Boolean))].sort();\n\n  // Filter and sort quizzes\n  const filteredQuizzes = quizzes\n    .filter(quiz => {\n      const matchesSearch = quiz.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           quiz.subject?.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesSubject = selectedSubject === 'all' || quiz.subject === selectedSubject;\n      const matchesClass = selectedClass === 'all' || quiz.class === selectedClass;\n      return matchesSearch && matchesSubject && matchesClass;\n    })\n    .sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return (a.name || '').localeCompare(b.name || '');\n        case 'duration':\n          return (a.duration || 0) - (b.duration || 0);\n        case 'questions':\n          return (a.questions?.length || 0) - (b.questions?.length || 0);\n        case 'xp':\n          return (b.xpPoints || 0) - (a.xpPoints || 0);\n        default:\n          return 0;\n      }\n    });\n\n  // Stats - calculate based on filtered quizzes and actual user results\n  const stats = {\n    total: filteredQuizzes.length,\n    completed: filteredQuizzes.filter(quiz => userResults[quiz._id]).length,\n    passed: filteredQuizzes.filter(quiz => {\n      const result = userResults[quiz._id];\n      if (!result) return false;\n      const passingMarks = quiz.passingMarks || 60;\n      return result.percentage >= passingMarks;\n    }).length,\n    totalXP: filteredQuizzes.reduce((sum, quiz) => {\n      const result = userResults[quiz._id];\n      return sum + (result?.xpEarned || 0);\n    }, 0)\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${className}`}>\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Quiz Dashboard</h1>\n              <p className=\"text-gray-600 mt-1\">Challenge yourself and track your progress</p>\n            </div>\n            \n            {/* Stats */}\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div className=\"bg-blue-50 rounded-lg p-3 text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">{stats.total}</div>\n                <div className=\"text-xs text-blue-500 font-medium\">Total Quizzes</div>\n              </div>\n              <div className=\"bg-green-50 rounded-lg p-3 text-center\">\n                <div className=\"text-2xl font-bold text-green-600\">{stats.passed}</div>\n                <div className=\"text-xs text-green-500 font-medium\">Passed</div>\n              </div>\n              <div className=\"bg-purple-50 rounded-lg p-3 text-center\">\n                <div className=\"text-2xl font-bold text-purple-600\">{stats.completed}</div>\n                <div className=\"text-xs text-purple-500 font-medium\">Attempted</div>\n              </div>\n              <div className=\"bg-yellow-50 rounded-lg p-3 text-center\">\n                <div className=\"text-2xl font-bold text-yellow-600\">{stats.totalXP}</div>\n                <div className=\"text-xs text-yellow-500 font-medium\">Total XP</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters and Controls */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border p-4 mb-6\">\n          <div className=\"flex flex-col lg:flex-row gap-4\">\n            {/* Search */}\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <TbSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search quizzes...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"lg:w-48\">\n              <select\n                value={selectedSubject}\n                onChange={(e) => setSelectedSubject(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {subjects.map(subject => (\n                  <option key={subject} value={subject}>{subject}</option>\n                ))}\n              </select>\n            </div>\n\n            {/* Class Filter */}\n            <div className=\"lg:w-48\">\n              <select\n                value={selectedClass}\n                onChange={(e) => setSelectedClass(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Classes</option>\n                {classes.map(cls => (\n                  <option key={cls} value={cls}>Class {cls}</option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"lg:w-48\">\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"name\">Sort by Name</option>\n                <option value=\"duration\">Sort by Duration</option>\n                <option value=\"questions\">Sort by Questions</option>\n                <option value=\"xp\">Sort by XP</option>\n              </select>\n            </div>\n\n            {/* View Mode */}\n            <div className=\"flex border border-gray-300 rounded-lg overflow-hidden\">\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`px-3 py-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600'}`}\n              >\n                <TbGridDots className=\"w-5 h-5\" />\n              </button>\n              <button\n                onClick={() => setViewMode('list')}\n                className={`px-3 py-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600'}`}\n              >\n                <TbList className=\"w-5 h-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Quiz Grid */}\n        {loading ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n          </div>\n        ) : filteredQuizzes.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <TbBook className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No quizzes found</h3>\n            <p className=\"text-gray-500\">Try adjusting your search or filter criteria.</p>\n          </div>\n        ) : (\n          <div className={`\n            ${viewMode === 'grid' \n              ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' \n              : 'space-y-4'\n            }\n          `}>\n            {filteredQuizzes.map((quiz, index) => (\n              <motion.div\n                key={quiz._id || index}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3, delay: Math.min(index * 0.1, 0.8) }}\n                className={viewMode === 'list' ? 'w-full' : ''}\n              >\n                <ModernQuizCard\n                  quiz={quiz}\n                  userResult={userResults[quiz._id]}\n                  onStart={onQuizStart}\n                  className=\"h-full\"\n                />\n              </motion.div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default QuizDashboard;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizInterface.js", ["735", "736", "737", "738", "739"], [], "import React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Tb<PERSON>lock,\n  TbChevronLeft,\n  TbChevronRight,\n  TbCheck,\n  TbX,\n  TbFlag,\n  TbAlertCircle,\n} from 'react-icons/tb';\n\nconst QuizInterface = ({\n  quiz,\n  questions = [],\n  onSubmit,\n  onExit,\n  className = ''\n}) => {\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeRemaining, setTimeRemaining] = useState((quiz?.duration || 30) * 60); // Convert to seconds\n  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const totalQuestions = questions.length;\n  const progress = ((currentQuestionIndex + 1) / totalQuestions) * 100;\n\n  // Timer effect\n  useEffect(() => {\n    if (timeRemaining <= 0) {\n      handleSubmit();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeRemaining(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeRemaining]);\n\n  // Format time as MM:SS\n  const formatTime = (seconds) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerChange = (questionId, answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < totalQuestions - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  const goToQuestion = (index) => {\n    setCurrentQuestionIndex(index);\n  };\n\n  // Submit quiz\n  const handleSubmit = () => {\n    onSubmit && onSubmit(answers);\n  };\n\n  // Render question based on type\n  const renderQuestion = () => {\n    if (!currentQuestion) {\n      return (\n        <div className=\"text-center py-8 text-gray-500\">\n          <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n          <p>Question not available</p>\n        </div>\n      );\n    }\n\n    // Validate question structure\n    if (typeof currentQuestion !== 'object' || !currentQuestion._id) {\n      console.warn('Invalid question structure:', currentQuestion);\n      return (\n        <div className=\"text-center py-8 text-gray-500\">\n          <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n          <p>Invalid question data</p>\n        </div>\n      );\n    }\n\n    const questionType = currentQuestion.type || currentQuestion.answerType || currentQuestion.questionType;\n    console.log('Question type detected:', questionType, 'Question:', currentQuestion);\n\n    switch (questionType) {\n      case 'multiple-choice':\n      case 'mcq':\n      case 'Options':\n        // Handle both object and array formats for options\n        let optionsToRender = [];\n\n        console.log('Processing options for MCQ:', currentQuestion.options);\n\n        if (Array.isArray(currentQuestion.options)) {\n          // Array format: ['option1', 'option2', ...]\n          optionsToRender = currentQuestion.options.map((option, index) => ({\n            key: String.fromCharCode(65 + index), // A, B, C, D\n            value: option\n          }));\n        } else if (typeof currentQuestion.options === 'object' && currentQuestion.options !== null) {\n          // Object format: {A: 'option1', B: 'option2', ...}\n          optionsToRender = Object.entries(currentQuestion.options).map(([key, value]) => ({\n            key: key,\n            value: value\n          }));\n        }\n\n        console.log('Options to render:', optionsToRender);\n\n        if (optionsToRender.length === 0) {\n          return (\n            <div className=\"text-center py-8 text-gray-500\">\n              <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n              <p>No options available for this question</p>\n            </div>\n          );\n        }\n\n        return (\n          <div className=\"space-y-3\">\n            {optionsToRender.map((option, index) => {\n              const isSelected = answers[currentQuestion._id] === option.key;\n\n              return (\n                <motion.button\n                  key={option.key}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  onClick={() => handleAnswerChange(currentQuestion._id, option.key)}\n                  className={`\n                    w-full p-4 rounded-lg border-2 text-left transition-all duration-200 shadow-sm hover:shadow-md\n                    ${isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900'\n                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n                    }\n                  `}\n                >\n                  <div className=\"flex items-center gap-3\">\n                    <div className={`\n                      w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm\n                      ${isSelected\n                        ? 'border-blue-500 bg-blue-500 text-white'\n                        : 'border-gray-300 text-gray-500'\n                      }\n                    `}>\n                      {option.key}\n                    </div>\n                    <span className={`flex-1 text-base font-medium leading-relaxed ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>\n                      {String(option.value || 'No content')}\n                    </span>\n                  </div>\n                </motion.button>\n              );\n            })}\n          </div>\n        );\n\n      case 'fill-in-the-blank':\n      case 'text':\n      case 'Fill in the Blank':\n        const isShortAnswer = currentQuestion.inputType === 'short';\n\n        return (\n          <div className=\"space-y-4\">\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <p className=\"text-blue-800 text-sm font-medium mb-2\">\n                ✏️ Instructions: {isShortAnswer ? 'Type your short answer below' : 'Write your detailed answer below'}\n              </p>\n              <p className=\"text-blue-600 text-sm\">\n                This is a free-text question. {isShortAnswer ? 'A few words or short phrase is expected.' : 'Write your complete answer with explanations.'}\n              </p>\n            </div>\n\n            <div className=\"space-y-2\">\n              <label className=\"block text-sm font-medium text-gray-700\">\n                Your Answer:\n              </label>\n\n              {isShortAnswer ? (\n                <input\n                  type=\"text\"\n                  value={answers[currentQuestion._id] || ''}\n                  onChange={(e) => handleAnswerChange(currentQuestion._id, e.target.value)}\n                  placeholder=\"Type your answer here... (e.g., 'Au' for Gold)\"\n                  className=\"w-full p-4 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 text-base\"\n                />\n              ) : (\n                <textarea\n                  value={answers[currentQuestion._id] || ''}\n                  onChange={(e) => handleAnswerChange(currentQuestion._id, e.target.value)}\n                  placeholder=\"Type your detailed answer here... Explain your reasoning and provide examples if needed.\"\n                  className=\"w-full p-4 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 resize-none transition-all duration-200 text-base\"\n                  rows={6}\n                  style={{ minHeight: '150px' }}\n                />\n              )}\n\n              <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                <span>\n                  {answers[currentQuestion._id]?.length || 0} characters\n                  {isShortAnswer && answers[currentQuestion._id]?.length > 50 && (\n                    <span className=\"text-amber-600 ml-2\">⚠ Consider a shorter answer</span>\n                  )}\n                </span>\n                <span className={answers[currentQuestion._id] ? 'text-green-600' : 'text-gray-500'}>\n                  {answers[currentQuestion._id] ? '✓ Answer provided' : '⚠ No answer yet'}\n                </span>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 'image':\n      case 'picture_based':\n        // Handle both object and array formats for image questions too\n        let imageOptionsToRender = [];\n\n        if (Array.isArray(currentQuestion.options)) {\n          imageOptionsToRender = currentQuestion.options.map((option, index) => ({\n            key: String.fromCharCode(65 + index),\n            value: option\n          }));\n        } else if (typeof currentQuestion.options === 'object' && currentQuestion.options !== null) {\n          imageOptionsToRender = Object.entries(currentQuestion.options).map(([key, value]) => ({\n            key: key,\n            value: value\n          }));\n        }\n\n        return (\n          <div className=\"space-y-4\">\n            {/* Image Display Section */}\n            <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200\">\n              <div className=\"text-center mb-4\">\n                <h4 className=\"text-lg font-semibold text-blue-900 mb-2\">📸 Image Question</h4>\n                <p className=\"text-blue-700 text-sm\">Look at the image below and select the correct answer</p>\n              </div>\n\n              {(currentQuestion.imageUrl || currentQuestion.image) ? (\n                <div className=\"bg-white rounded-lg p-4 shadow-sm\">\n                  <img\n                    src={currentQuestion.imageUrl || currentQuestion.image}\n                    alt=\"Question Image\"\n                    className=\"max-w-full h-auto rounded-lg mx-auto shadow-md max-h-64 object-contain\"\n                    onLoad={(e) => {\n                      console.log('Image loaded successfully:', e.target.src);\n                    }}\n                    onError={(e) => {\n                      console.error('Image failed to load:', e.target.src);\n                      e.target.style.display = 'none';\n                      const fallback = e.target.parentNode.querySelector('.image-fallback');\n                      if (fallback) fallback.style.display = 'block';\n                    }}\n                  />\n                  <div className=\"image-fallback text-center text-gray-500 py-8 hidden\">\n                    <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2 text-gray-400\" />\n                    <p className=\"text-gray-600\">Image could not be loaded</p>\n                    <p className=\"text-sm text-gray-500 mt-1\">Please answer based on the question text</p>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"bg-white rounded-lg p-8 text-center text-gray-500 border-2 border-dashed border-gray-300\">\n                  <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2 text-gray-400\" />\n                  <p className=\"text-gray-600\">No image provided for this question</p>\n                </div>\n              )}\n            </div>\n\n            {imageOptionsToRender.length === 0 ? (\n              <div className=\"text-center py-8 text-gray-500\">\n                <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n                <p>No options available for this image question</p>\n              </div>\n            ) : (\n              <div className=\"space-y-3\">\n                {imageOptionsToRender.map((option) => {\n                  const isSelected = answers[currentQuestion._id] === option.key;\n\n                  return (\n                    <button\n                      key={option.key}\n                      onClick={() => handleAnswerChange(currentQuestion._id, option.key)}\n                      className={`\n                        w-full p-4 rounded-lg border-2 text-left transition-all duration-200\n                        ${isSelected\n                          ? 'border-blue-500 bg-blue-50 text-blue-900'\n                          : 'border-gray-200 bg-white hover:border-gray-300'\n                        }\n                      `}\n                    >\n                      <div className=\"flex items-center gap-3\">\n                        <div className={`\n                          w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm\n                          ${isSelected\n                            ? 'border-blue-500 bg-blue-500 text-white'\n                            : 'border-gray-300 text-gray-500'\n                          }\n                        `}>\n                          {option.key}\n                        </div>\n                        <span className={`text-base font-medium ${isSelected ? 'text-blue-900' : 'text-gray-800'}`}>\n                          {option.value || 'No content'}\n                        </span>\n                      </div>\n                    </button>\n                  );\n                })}\n              </div>\n            )}\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"text-center py-8 text-gray-500\">\n            <TbAlertCircle className=\"w-12 h-12 mx-auto mb-2\" />\n            <p>Unsupported question type: {currentQuestion.type || currentQuestion.answerType}</p>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 flex flex-col ${className}`}>\n      {/* Top Bar */}\n      <div className=\"bg-white shadow-sm border-b sticky top-0 z-10\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Quiz Title */}\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-900\">{quiz?.name || 'Quiz'}</h1>\n              <p className=\"text-sm text-gray-600\">{quiz?.subject || 'Subject'}</p>\n            </div>\n\n            {/* Timer */}\n            <div className={`\n              flex items-center gap-2 px-4 py-2 rounded-lg font-mono text-lg font-bold\n              ${timeRemaining <= 300 ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'}\n            `}>\n              <TbClock className=\"w-5 h-5\" />\n              {formatTime(timeRemaining)}\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-4\">\n            <div className=\"flex items-center justify-between text-sm text-gray-600 mb-2\">\n              <span>Question {currentQuestionIndex + 1} of {totalQuestions}</span>\n              <span>{Math.round(progress)}% Complete</span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-blue-500 h-2 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${progress}%` }}\n                transition={{ duration: 0.3 }}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Question Content */}\n      <div className=\"flex-1 max-w-4xl mx-auto w-full px-4 py-8\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentQuestionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-xl shadow-sm border p-6 lg:p-8\"\n          >\n            {/* Question */}\n            <div className=\"mb-8\">\n              <h2 className=\"text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                {currentQuestion?.name || currentQuestion?.question || 'Question not available'}\n              </h2>\n              \n              {currentQuestion?.description && (\n                <p className=\"text-gray-600 mb-4\">{currentQuestion.description}</p>\n              )}\n            </div>\n\n            {/* Answer Options */}\n            {renderQuestion()}\n          </motion.div>\n        </AnimatePresence>\n      </div>\n\n      {/* Bottom Navigation */}\n      <div className=\"bg-white border-t sticky bottom-0\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Previous Button */}\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestionIndex === 0}\n              className={`\n                flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors\n                ${currentQuestionIndex === 0\n                  ? 'text-gray-400 cursor-not-allowed'\n                  : 'text-gray-700 hover:bg-gray-100'\n                }\n              `}\n            >\n              <TbChevronLeft className=\"w-5 h-5\" />\n              Previous\n            </button>\n\n            {/* Question Numbers */}\n            <div className=\"flex items-center gap-2 overflow-x-auto max-w-md\">\n              {questions && Array.isArray(questions) ? questions.map((question, index) => {\n                // Safety check for question object\n                if (!question || typeof question !== 'object') {\n                  console.warn('Invalid question at index:', index, question);\n                  return null;\n                }\n\n                return (\n                  <button\n                    key={question._id || index}\n                    onClick={() => goToQuestion(index)}\n                    className={`\n                      w-8 h-8 rounded-full text-sm font-medium transition-colors flex-shrink-0\n                      ${index === currentQuestionIndex\n                        ? 'bg-blue-500 text-white'\n                        : answers[question._id]\n                        ? 'bg-green-100 text-green-600'\n                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                      }\n                    `}\n                  >\n                    {index + 1}\n                  </button>\n                );\n              }) : null}\n            </div>\n\n            {/* Next/Submit Button */}\n            {currentQuestionIndex === totalQuestions - 1 ? (\n              <button\n                onClick={() => setShowSubmitConfirm(true)}\n                className=\"flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors\"\n              >\n                <TbCheck className=\"w-5 h-5\" />\n                Submit Quiz\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors\"\n              >\n                Next\n                <TbChevronRight className=\"w-5 h-5\" />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Submit Confirmation Modal */}\n      <AnimatePresence>\n        {showSubmitConfirm && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\"\n          >\n            <motion.div\n              initial={{ scale: 0.9, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0.9, opacity: 0 }}\n              className=\"bg-white rounded-xl p-6 max-w-md w-full\"\n            >\n              <h3 className=\"text-lg font-bold text-gray-900 mb-4\">Submit Quiz?</h3>\n              <p className=\"text-gray-600 mb-6\">\n                Are you sure you want to submit your quiz? You won't be able to change your answers after submission.\n              </p>\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={() => setShowSubmitConfirm(false)}\n                  className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSubmit}\n                  className=\"flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n                >\n                  Submit\n                </button>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default QuizInterface;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ModernQuizCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\quiz.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js", ["740", "741", "742", "743", "744"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx", [], ["745"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\SyllabusManagement\\SyllabusManagement.jsx", ["746", "747", "748", "749"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js", [], [], {"ruleId": "750", "severity": 1, "message": "751", "line": 8, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 8, "endColumn": 21}, {"ruleId": "750", "severity": 1, "message": "754", "line": 8, "column": 23, "nodeType": "752", "messageId": "753", "endLine": 8, "endColumn": 34}, {"ruleId": "750", "severity": 1, "message": "755", "line": 15, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 16}, {"ruleId": "750", "severity": 1, "message": "756", "line": 15, "column": 18, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 33}, {"ruleId": "750", "severity": 1, "message": "757", "line": 15, "column": 35, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 42}, {"ruleId": "750", "severity": 1, "message": "758", "line": 15, "column": 44, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 47}, {"ruleId": "750", "severity": 1, "message": "759", "line": 15, "column": 49, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 62}, {"ruleId": "750", "severity": 1, "message": "760", "line": 15, "column": 64, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 72}, {"ruleId": "750", "severity": 1, "message": "761", "line": 15, "column": 74, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 80}, {"ruleId": "750", "severity": 1, "message": "762", "line": 15, "column": 82, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 92}, {"ruleId": "750", "severity": 1, "message": "763", "line": 15, "column": 94, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 100}, {"ruleId": "750", "severity": 1, "message": "764", "line": 15, "column": 102, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 108}, {"ruleId": "750", "severity": 1, "message": "765", "line": 16, "column": 8, "nodeType": "752", "messageId": "753", "endLine": 16, "endColumn": 29}, {"ruleId": "766", "severity": 1, "message": "767", "line": 60, "column": 6, "nodeType": "768", "endLine": 60, "endColumn": 8, "suggestions": "769"}, {"ruleId": "766", "severity": 1, "message": "770", "line": 107, "column": 6, "nodeType": "768", "endLine": 107, "endColumn": 33, "suggestions": "771"}, {"ruleId": "766", "severity": 1, "message": "772", "line": 114, "column": 6, "nodeType": "768", "endLine": 114, "endColumn": 25, "suggestions": "773"}, {"ruleId": "750", "severity": 1, "message": "774", "line": 145, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 145, "endColumn": 23}, {"ruleId": "750", "severity": 1, "message": "775", "line": 1, "column": 35, "nodeType": "752", "messageId": "753", "endLine": 1, "endColumn": 41}, {"ruleId": "766", "severity": 1, "message": "776", "line": 81, "column": 6, "nodeType": "768", "endLine": 81, "endColumn": 8, "suggestions": "777"}, {"ruleId": "766", "severity": 1, "message": "778", "line": 94, "column": 6, "nodeType": "768", "endLine": 94, "endColumn": 8, "suggestions": "779"}, {"ruleId": "766", "severity": 1, "message": "780", "line": 65, "column": 8, "nodeType": "768", "endLine": 65, "endColumn": 32, "suggestions": "781"}, {"ruleId": "750", "severity": 1, "message": "782", "line": 17, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 17, "endColumn": 31}, {"ruleId": "750", "severity": 1, "message": "783", "line": 17, "column": 33, "nodeType": "752", "messageId": "753", "endLine": 17, "endColumn": 43}, {"ruleId": "766", "severity": 1, "message": "784", "line": 777, "column": 6, "nodeType": "768", "endLine": 777, "endColumn": 81, "suggestions": "785"}, {"ruleId": "750", "severity": 1, "message": "786", "line": 19, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 19, "endColumn": 16}, {"ruleId": "750", "severity": 1, "message": "787", "line": 20, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 20, "endColumn": 11}, {"ruleId": "750", "severity": 1, "message": "788", "line": 29, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 29, "endColumn": 11}, {"ruleId": "750", "severity": 1, "message": "789", "line": 30, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 30, "endColumn": 11}, {"ruleId": "750", "severity": 1, "message": "790", "line": 31, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 31, "endColumn": 18}, {"ruleId": "750", "severity": 1, "message": "791", "line": 32, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 32, "endColumn": 9}, {"ruleId": "750", "severity": 1, "message": "792", "line": 33, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 33, "endColumn": 13}, {"ruleId": "750", "severity": 1, "message": "793", "line": 34, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 34, "endColumn": 8}, {"ruleId": "750", "severity": 1, "message": "794", "line": 35, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 35, "endColumn": 13}, {"ruleId": "750", "severity": 1, "message": "761", "line": 36, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 36, "endColumn": 9}, {"ruleId": "750", "severity": 1, "message": "795", "line": 38, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 38, "endColumn": 14}, {"ruleId": "750", "severity": 1, "message": "758", "line": 39, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 39, "endColumn": 6}, {"ruleId": "750", "severity": 1, "message": "796", "line": 40, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 40, "endColumn": 18}, {"ruleId": "750", "severity": 1, "message": "797", "line": 41, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 41, "endColumn": 15}, {"ruleId": "750", "severity": 1, "message": "798", "line": 42, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 42, "endColumn": 10}, {"ruleId": "750", "severity": 1, "message": "799", "line": 43, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 43, "endColumn": 14}, {"ruleId": "766", "severity": 1, "message": "800", "line": 71, "column": 9, "nodeType": "801", "endLine": 75, "endColumn": 29}, {"ruleId": "750", "severity": 1, "message": "802", "line": 4, "column": 19, "nodeType": "752", "messageId": "753", "endLine": 4, "endColumn": 24}, {"ruleId": "766", "severity": 1, "message": "803", "line": 67, "column": 6, "nodeType": "768", "endLine": 67, "endColumn": 8, "suggestions": "804"}, {"ruleId": "750", "severity": 1, "message": "805", "line": 9, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 9, "endColumn": 10}, {"ruleId": "750", "severity": 1, "message": "806", "line": 10, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 10, "endColumn": 17}, {"ruleId": "750", "severity": 1, "message": "807", "line": 11, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 11, "endColumn": 11}, {"ruleId": "750", "severity": 1, "message": "808", "line": 12, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 12, "endColumn": 15}, {"ruleId": "750", "severity": 1, "message": "798", "line": 15, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 15, "endColumn": 10}, {"ruleId": "750", "severity": 1, "message": "758", "line": 16, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 16, "endColumn": 6}, {"ruleId": "750", "severity": 1, "message": "764", "line": 17, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 17, "endColumn": 9}, {"ruleId": "750", "severity": 1, "message": "755", "line": 18, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 18, "endColumn": 9}, {"ruleId": "750", "severity": 1, "message": "809", "line": 19, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 19, "endColumn": 9}, {"ruleId": "766", "severity": 1, "message": "810", "line": 112, "column": 6, "nodeType": "768", "endLine": 112, "endColumn": 53, "suggestions": "811"}, {"ruleId": "766", "severity": 1, "message": "810", "line": 135, "column": 6, "nodeType": "768", "endLine": 135, "endColumn": 8, "suggestions": "812"}, {"ruleId": "750", "severity": 1, "message": "813", "line": 2, "column": 18, "nodeType": "752", "messageId": "753", "endLine": 2, "endColumn": 33}, {"ruleId": "750", "severity": 1, "message": "814", "line": 21, "column": 53, "nodeType": "752", "messageId": "753", "endLine": 21, "endColumn": 67}, {"ruleId": "750", "severity": 1, "message": "815", "line": 74, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 74, "endColumn": 18}, {"ruleId": "750", "severity": 1, "message": "816", "line": 74, "column": 20, "nodeType": "752", "messageId": "753", "endLine": 74, "endColumn": 31}, {"ruleId": "750", "severity": 1, "message": "817", "line": 75, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 75, "endColumn": 19}, {"ruleId": "750", "severity": 1, "message": "818", "line": 75, "column": 21, "nodeType": "752", "messageId": "753", "endLine": 75, "endColumn": 33}, {"ruleId": "750", "severity": 1, "message": "819", "line": 76, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 76, "endColumn": 24}, {"ruleId": "750", "severity": 1, "message": "820", "line": 79, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 79, "endColumn": 27}, {"ruleId": "750", "severity": 1, "message": "821", "line": 81, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 81, "endColumn": 24}, {"ruleId": "750", "severity": 1, "message": "822", "line": 87, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 87, "endColumn": 18}, {"ruleId": "750", "severity": 1, "message": "823", "line": 88, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 88, "endColumn": 23}, {"ruleId": "766", "severity": 1, "message": "824", "line": 786, "column": 6, "nodeType": "768", "endLine": 786, "endColumn": 8, "suggestions": "825"}, {"ruleId": "750", "severity": 1, "message": "826", "line": 807, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 807, "endColumn": 24}, {"ruleId": "750", "severity": 1, "message": "827", "line": 895, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 895, "endColumn": 29}, {"ruleId": "750", "severity": 1, "message": "828", "line": 1324, "column": 39, "nodeType": "752", "messageId": "753", "endLine": 1324, "endColumn": 48}, {"ruleId": "829", "severity": 1, "message": "830", "line": 1910, "column": 27, "nodeType": "831", "messageId": "832", "endLine": 1910, "endColumn": 28}, {"ruleId": "750", "severity": 1, "message": "833", "line": 1, "column": 38, "nodeType": "752", "messageId": "753", "endLine": 1, "endColumn": 46}, {"ruleId": "750", "severity": 1, "message": "834", "line": 8, "column": 12, "nodeType": "752", "messageId": "753", "endLine": 8, "endColumn": 19}, {"ruleId": "766", "severity": 1, "message": "835", "line": 58, "column": 8, "nodeType": "768", "endLine": 58, "endColumn": 10, "suggestions": "836"}, {"ruleId": "766", "severity": 1, "message": "837", "line": 96, "column": 6, "nodeType": "768", "endLine": 96, "endColumn": 35, "suggestions": "838"}, {"ruleId": "750", "severity": 1, "message": "839", "line": 98, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 98, "endColumn": 26}, {"ruleId": "750", "severity": 1, "message": "840", "line": 12, "column": 8, "nodeType": "752", "messageId": "753", "endLine": 12, "endColumn": 17}, {"ruleId": "750", "severity": 1, "message": "793", "line": 22, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 22, "endColumn": 8}, {"ruleId": "750", "severity": 1, "message": "841", "line": 32, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 32, "endColumn": 17}, {"ruleId": "750", "severity": 1, "message": "842", "line": 38, "column": 19, "nodeType": "752", "messageId": "753", "endLine": 38, "endColumn": 29}, {"ruleId": "766", "severity": 1, "message": "843", "line": 177, "column": 6, "nodeType": "768", "endLine": 177, "endColumn": 8, "suggestions": "844"}, {"ruleId": "750", "severity": 1, "message": "845", "line": 1, "column": 38, "nodeType": "752", "messageId": "753", "endLine": 1, "endColumn": 44}, {"ruleId": "750", "severity": 1, "message": "846", "line": 4, "column": 40, "nodeType": "752", "messageId": "753", "endLine": 4, "endColumn": 46}, {"ruleId": "750", "severity": 1, "message": "840", "line": 5, "column": 8, "nodeType": "752", "messageId": "753", "endLine": 5, "endColumn": 17}, {"ruleId": "750", "severity": 1, "message": "847", "line": 17, "column": 8, "nodeType": "752", "messageId": "753", "endLine": 17, "endColumn": 13}, {"ruleId": "750", "severity": 1, "message": "848", "line": 36, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 36, "endColumn": 20}, {"ruleId": "766", "severity": 1, "message": "849", "line": 62, "column": 6, "nodeType": "768", "endLine": 62, "endColumn": 26, "suggestions": "850"}, {"ruleId": "766", "severity": 1, "message": "851", "line": 94, "column": 6, "nodeType": "768", "endLine": 94, "endColumn": 8, "suggestions": "852"}, {"ruleId": "766", "severity": 1, "message": "853", "line": 214, "column": 6, "nodeType": "768", "endLine": 214, "endColumn": 20, "suggestions": "854"}, {"ruleId": "750", "severity": 1, "message": "840", "line": 3, "column": 8, "nodeType": "752", "messageId": "753", "endLine": 3, "endColumn": 17}, {"ruleId": "750", "severity": 1, "message": "855", "line": 10, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 10, "endColumn": 14}, {"ruleId": "750", "severity": 1, "message": "802", "line": 10, "column": 25, "nodeType": "752", "messageId": "753", "endLine": 10, "endColumn": 30}, {"ruleId": "750", "severity": 1, "message": "856", "line": 10, "column": 32, "nodeType": "752", "messageId": "753", "endLine": 10, "endColumn": 37}, {"ruleId": "750", "severity": 1, "message": "857", "line": 10, "column": 39, "nodeType": "752", "messageId": "753", "endLine": 10, "endColumn": 45}, {"ruleId": "750", "severity": 1, "message": "858", "line": 19, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 19, "endColumn": 21}, {"ruleId": "750", "severity": 1, "message": "859", "line": 21, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 21, "endColumn": 14}, {"ruleId": "750", "severity": 1, "message": "860", "line": 22, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 22, "endColumn": 22}, {"ruleId": "750", "severity": 1, "message": "861", "line": 33, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 33, "endColumn": 30}, {"ruleId": "766", "severity": 1, "message": "862", "line": 110, "column": 6, "nodeType": "768", "endLine": 110, "endColumn": 32, "suggestions": "863"}, {"ruleId": "766", "severity": 1, "message": "851", "line": 144, "column": 6, "nodeType": "768", "endLine": 144, "endColumn": 8, "suggestions": "864"}, {"ruleId": "750", "severity": 1, "message": "865", "line": 146, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 146, "endColumn": 21}, {"ruleId": "750", "severity": 1, "message": "866", "line": 228, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 228, "endColumn": 33}, {"ruleId": "750", "severity": 1, "message": "867", "line": 238, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 238, "endColumn": 32}, {"ruleId": "750", "severity": 1, "message": "868", "line": 286, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 286, "endColumn": 26}, {"ruleId": "750", "severity": 1, "message": "869", "line": 305, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 305, "endColumn": 19}, {"ruleId": "766", "severity": 1, "message": "851", "line": 316, "column": 6, "nodeType": "768", "endLine": 316, "endColumn": 8, "suggestions": "870"}, {"ruleId": "766", "severity": 1, "message": "871", "line": 323, "column": 6, "nodeType": "768", "endLine": 323, "endColumn": 19, "suggestions": "872"}, {"ruleId": "750", "severity": 1, "message": "873", "line": 18, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 18, "endColumn": 14}, {"ruleId": "750", "severity": 1, "message": "874", "line": 23, "column": 8, "nodeType": "752", "messageId": "753", "endLine": 23, "endColumn": 14}, {"ruleId": "750", "severity": 1, "message": "857", "line": 26, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 26, "endColumn": 16}, {"ruleId": "750", "severity": 1, "message": "875", "line": 32, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 32, "endColumn": 17}, {"ruleId": "766", "severity": 1, "message": "876", "line": 40, "column": 38, "nodeType": "768", "endLine": 40, "endColumn": 40, "suggestions": "877"}, {"ruleId": "750", "severity": 1, "message": "878", "line": 2, "column": 8, "nodeType": "752", "messageId": "753", "endLine": 2, "endColumn": 13}, {"ruleId": "750", "severity": 1, "message": "879", "line": 74, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 74, "endColumn": 23}, {"ruleId": "750", "severity": 1, "message": "880", "line": 19, "column": 11, "nodeType": "752", "messageId": "753", "endLine": 19, "endColumn": 24}, {"ruleId": "881", "severity": 1, "message": "882", "line": 73, "column": 111, "nodeType": "883", "messageId": "884", "endLine": 73, "endColumn": 112, "suggestions": "885"}, {"ruleId": "881", "severity": 1, "message": "882", "line": 95, "column": 89, "nodeType": "883", "messageId": "884", "endLine": 95, "endColumn": 90, "suggestions": "886"}, {"ruleId": "750", "severity": 1, "message": "887", "line": 7, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 7, "endColumn": 19}, {"ruleId": "766", "severity": 1, "message": "888", "line": 126, "column": 6, "nodeType": "768", "endLine": 126, "endColumn": 32, "suggestions": "889", "suppressions": "890"}, {"ruleId": "750", "severity": 1, "message": "891", "line": 1, "column": 17, "nodeType": "752", "messageId": "753", "endLine": 1, "endColumn": 26}, {"ruleId": "750", "severity": 1, "message": "892", "line": 1, "column": 28, "nodeType": "752", "messageId": "753", "endLine": 1, "endColumn": 36}, {"ruleId": "750", "severity": 1, "message": "893", "line": 20, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 20, "endColumn": 8}, {"ruleId": "750", "severity": 1, "message": "894", "line": 21, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 21, "endColumn": 11}, {"ruleId": "750", "severity": 1, "message": "787", "line": 22, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 22, "endColumn": 11}, {"ruleId": "766", "severity": 1, "message": "895", "line": 95, "column": 6, "nodeType": "768", "endLine": 95, "endColumn": 15, "suggestions": "896"}, {"ruleId": "750", "severity": 1, "message": "897", "line": 9, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 9, "endColumn": 8}, {"ruleId": "750", "severity": 1, "message": "841", "line": 23, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 23, "endColumn": 17}, {"ruleId": "766", "severity": 1, "message": "898", "line": 37, "column": 6, "nodeType": "768", "endLine": 37, "endColumn": 8, "suggestions": "899"}, {"ruleId": "750", "severity": 1, "message": "900", "line": 42, "column": 5, "nodeType": "752", "messageId": "753", "endLine": 42, "endColumn": 15}, {"ruleId": "750", "severity": 1, "message": "901", "line": 7, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 7, "endColumn": 14}, {"ruleId": "766", "severity": 1, "message": "902", "line": 32, "column": 6, "nodeType": "768", "endLine": 32, "endColumn": 15, "suggestions": "903"}, {"ruleId": "750", "severity": 1, "message": "904", "line": 3, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 3, "endColumn": 25}, {"ruleId": "750", "severity": 1, "message": "783", "line": 25, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 25, "endColumn": 13}, {"ruleId": "750", "severity": 1, "message": "905", "line": 26, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 26, "endColumn": 13}, {"ruleId": "750", "severity": 1, "message": "906", "line": 48, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 48, "endColumn": 21}, {"ruleId": "766", "severity": 1, "message": "784", "line": 180, "column": 6, "nodeType": "768", "endLine": 180, "endColumn": 74, "suggestions": "907"}, {"ruleId": "766", "severity": 1, "message": "908", "line": 212, "column": 6, "nodeType": "768", "endLine": 212, "endColumn": 8, "suggestions": "909"}, {"ruleId": "766", "severity": 1, "message": "910", "line": 225, "column": 6, "nodeType": "768", "endLine": 225, "endColumn": 27, "suggestions": "911"}, {"ruleId": "750", "severity": 1, "message": "912", "line": 8, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 8, "endColumn": 9}, {"ruleId": "750", "severity": 1, "message": "913", "line": 21, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 21, "endColumn": 15}, {"ruleId": "750", "severity": 1, "message": "914", "line": 30, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 30, "endColumn": 21}, {"ruleId": "766", "severity": 1, "message": "915", "line": 56, "column": 6, "nodeType": "768", "endLine": 56, "endColumn": 8, "suggestions": "916"}, {"ruleId": "750", "severity": 1, "message": "917", "line": 1, "column": 38, "nodeType": "752", "messageId": "753", "endLine": 1, "endColumn": 46}, {"ruleId": "750", "severity": 1, "message": "918", "line": 59, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 59, "endColumn": 22}, {"ruleId": "750", "severity": 1, "message": "919", "line": 112, "column": 23, "nodeType": "752", "messageId": "753", "endLine": 112, "endColumn": 34}, {"ruleId": "750", "severity": 1, "message": "920", "line": 2, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 2, "endColumn": 16}, {"ruleId": "921", "severity": 1, "message": "922", "line": 69, "column": 3, "nodeType": "923", "messageId": "924", "endLine": 90, "endColumn": 5}, {"ruleId": "750", "severity": 1, "message": "925", "line": 198, "column": 10, "nodeType": "752", "messageId": "753", "endLine": 198, "endColumn": 22}, {"ruleId": "750", "severity": 1, "message": "926", "line": 5, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 5, "endColumn": 10}, {"ruleId": "750", "severity": 1, "message": "927", "line": 12, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 12, "endColumn": 10}, {"ruleId": "750", "severity": 1, "message": "928", "line": 13, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 13, "endColumn": 12}, {"ruleId": "750", "severity": 1, "message": "929", "line": 62, "column": 9, "nodeType": "752", "messageId": "753", "endLine": 62, "endColumn": 32}, {"ruleId": "750", "severity": 1, "message": "930", "line": 128, "column": 5, "nodeType": "752", "messageId": "753", "endLine": 128, "endColumn": 14}, {"ruleId": "766", "severity": 1, "message": "931", "line": 34, "column": 6, "nodeType": "768", "endLine": 34, "endColumn": 8, "suggestions": "932"}, {"ruleId": "750", "severity": 1, "message": "933", "line": 1, "column": 8, "nodeType": "752", "messageId": "753", "endLine": 1, "endColumn": 13}, {"ruleId": "750", "severity": 1, "message": "934", "line": 25, "column": 11, "nodeType": "752", "messageId": "753", "endLine": 25, "endColumn": 15}, {"ruleId": "750", "severity": 1, "message": "805", "line": 8, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 8, "endColumn": 10}, {"ruleId": "750", "severity": 1, "message": "935", "line": 11, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 11, "endColumn": 17}, {"ruleId": "750", "severity": 1, "message": "789", "line": 7, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 7, "endColumn": 11}, {"ruleId": "750", "severity": 1, "message": "790", "line": 10, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 10, "endColumn": 18}, {"ruleId": "750", "severity": 1, "message": "805", "line": 12, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 12, "endColumn": 10}, {"ruleId": "750", "severity": 1, "message": "764", "line": 13, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 13, "endColumn": 9}, {"ruleId": "750", "severity": 1, "message": "936", "line": 1, "column": 38, "nodeType": "752", "messageId": "753", "endLine": 1, "endColumn": 49}, {"ruleId": "750", "severity": 1, "message": "758", "line": 8, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 8, "endColumn": 6}, {"ruleId": "750", "severity": 1, "message": "937", "line": 9, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 9, "endColumn": 9}, {"ruleId": "766", "severity": 1, "message": "938", "line": 41, "column": 6, "nodeType": "768", "endLine": 41, "endColumn": 21, "suggestions": "939"}, {"ruleId": "940", "severity": 1, "message": "941", "line": 261, "column": 19, "nodeType": "942", "endLine": 274, "endColumn": 21}, {"ruleId": "750", "severity": 1, "message": "798", "line": 7, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 7, "endColumn": 10}, {"ruleId": "750", "severity": 1, "message": "758", "line": 8, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 8, "endColumn": 6}, {"ruleId": "750", "severity": 1, "message": "762", "line": 9, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 9, "endColumn": 13}, {"ruleId": "750", "severity": 1, "message": "943", "line": 10, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 10, "endColumn": 10}, {"ruleId": "766", "severity": 1, "message": "944", "line": 78, "column": 6, "nodeType": "768", "endLine": 78, "endColumn": 14, "suggestions": "945"}, {"ruleId": "766", "severity": 1, "message": "946", "line": 42, "column": 6, "nodeType": "768", "endLine": 42, "endColumn": 8, "suggestions": "947", "suppressions": "948"}, {"ruleId": "750", "severity": 1, "message": "949", "line": 24, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 24, "endColumn": 9}, {"ruleId": "750", "severity": 1, "message": "950", "line": 29, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 29, "endColumn": 10}, {"ruleId": "750", "severity": 1, "message": "951", "line": 42, "column": 3, "nodeType": "752", "messageId": "753", "endLine": 42, "endColumn": 17}, {"ruleId": "766", "severity": 1, "message": "952", "line": 69, "column": 6, "nodeType": "768", "endLine": 69, "endColumn": 15, "suggestions": "953"}, "no-unused-vars", "'HideLoading' is defined but never used.", "Identifier", "unusedVar", "'ShowLoading' is defined but never used.", "'TbHome' is defined but never used.", "'TbBrandTanzania' is defined but never used.", "'TbMenu2' is defined but never used.", "'TbX' is defined but never used.", "'TbChevronDown' is defined but never used.", "'TbLogout' is defined but never used.", "'TbUser' is defined but never used.", "'TbSettings' is defined but never used.", "'TbBell' is defined but never used.", "'TbStar' is defined but never used.", "'OnlineStatusIndicator' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'getUserData' and 'navigate'. Either include them or remove the dependency array.", "ArrayExpression", ["954"], "React Hook useEffect has missing dependencies: 'dispatch', 'user?.isAdmin', 'user?.paymentRequired', and 'verifyPaymentStatus'. Either include them or remove the dependency array.", ["955"], "React Hook useEffect has a missing dependency: 'verifyPaymentStatus'. Either include it or remove the dependency array.", ["956"], "'getButtonClass' is assigned a value but never used.", "'Select' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExamData' and 'params.id'. Either include them or remove the dependency array.", ["957"], "React Hook useEffect has a missing dependency: 'getExamsData'. Either include it or remove the dependency array.", ["958"], "React Hook useEffect has a missing dependency: 'paymentInProgress'. Either include it or remove the dependency array.", ["959"], "'extractUserResultData' is defined but never used.", "'safeNumber' is defined but never used.", "React Hook useCallback has a missing dependency: 'startTime'. Either include it or remove the dependency array.", ["960"], "'FaChevronDown' is defined but never used.", "'FaSearch' is defined but never used.", "'TbSearch' is defined but never used.", "'TbFilter' is defined but never used.", "'TbSortAscending' is defined but never used.", "'TbPlay' is defined but never used.", "'TbDownload' is defined but never used.", "'TbEye' is defined but never used.", "'TbCalendar' is defined but never used.", "'TbChevronUp' is defined but never used.", "'TbAlertTriangle' is defined but never used.", "'TbInfoCircle' is defined but never used.", "'TbCheck' is defined but never used.", "'TbSubtitles' is defined but never used.", "The 'allPossibleClasses' conditional could make the dependencies of useCallback Hook (at line 127) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'allPossibleClasses' in its own useMemo() Hook.", "VariableDeclarator", "'Modal' is defined but never used.", "React Hook useEffect has a missing dependency: 'getData'. Either include it or remove the dependency array.", ["961"], "'TbClock' is defined but never used.", "'TbQuestionMark' is defined but never used.", "'TbTrophy' is defined but never used.", "'TbPlayerPlay' is defined but never used.", "'TbBolt' is defined but never used.", "React Hook useEffect has a missing dependency: 'getUserResults'. Either include it or remove the dependency array.", ["962"], ["963"], "'AnimatePresence' is defined but never used.", "'getUserRanking' is defined but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'showStats' is assigned a value but never used.", "'setShowStats' is assigned a value but never used.", "'animationPhase' is assigned a value but never used.", "'currentUserLeague' is assigned a value but never used.", "'showLeagueView' is assigned a value but never used.", "'headerRef' is assigned a value but never used.", "'currentUserRef' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchFullUserData', 'fetchRankingData', 'motivationalQuotes', 'rankingData', and 'user'. Either include them or remove the dependency array.", ["964"], "'otherPerformers' is assigned a value but never used.", "'getSubscriptionBadge' is assigned a value but never used.", "'leagueKey' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'y'.", "ObjectExpression", "unexpected", "'Suspense' is defined but never used.", "'isAdmin' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'getUserData'. Either include them or remove the dependency array.", ["965"], "React Hook useEffect has missing dependencies: 'getData' and 'pagination'. Either include them or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["966"], "'handleTableChange' is assigned a value but never used.", "'PageTitle' is defined but never used.", "'navigate' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUsersData'. Either include it or remove the dependency array.", ["967"], "'useRef' is defined but never used.", "'Avatar' is defined but never used.", "'image' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["968"], "React Hook useEffect has a missing dependency: 'getUserData'. Either include it or remove the dependency array.", ["969"], "React Hook useEffect has a missing dependency: 'form2'. Either include it or remove the dependency array.", ["970"], "'Form' is defined but never used.", "'Input' is defined but never used.", "'Button' is defined but never used.", "'userRanking' is assigned a value but never used.", "'edit' is assigned a value but never used.", "'imagePreview' is assigned a value but never used.", "'showLevelChangeModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUserStats'. Either include it or remove the dependency array.", ["971"], ["972"], "'handleChange' is assigned a value but never used.", "'handleLevelChangeConfirm' is assigned a value but never used.", "'handleLevelChangeCancel' is assigned a value but never used.", "'handleImageUpload' is assigned a value but never used.", "'verifyUser' is assigned a value but never used.", ["973"], "React Hook useEffect has a missing dependency: 'fetchUserRankingData'. Either include it or remove the dependency array.", ["974"], "'Rate' is defined but never used.", "'Image2' is defined but never used.", "'reviews' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getReviews'. Either include it or remove the dependency array.", ["975"], "'axios' is defined but never used.", "'handleKeyPress' is assigned a value but never used.", "'restoredLines' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\[.", "Literal", "unnecessaryEscape", ["976", "977"], ["978", "979"], "'formatTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'renderPDF'. Either include it or remove the dependency array.", ["980"], ["981"], "'useEffect' is defined but never used.", "'useState' is defined but never used.", "'FaEye' is defined but never used.", "'FaFilter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMaterials'. Either include it or remove the dependency array.", ["982"], "'FaCog' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchGenerationHistory'. Either include it or remove the dependency array.", ["983"], "'needsLogin' is assigned a value but never used.", "'Title' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleAutoRefresh'. Either include it or remove the dependency array.", ["984"], "'validateSession' is defined but never used.", "'formatTime' is defined but never used.", "'examDataSafe' is assigned a value but never used.", ["985"], "React Hook useEffect has missing dependencies: 'getExamData' and 'id'. Either include them or remove the dependency array.", ["986"], "React Hook useEffect has a missing dependency: 'startTimer'. Either include it or remove the dependency array.", ["987"], "'FaHome' is defined but never used.", "'FaSignOutAlt' is defined but never used.", "'handleLogout' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'inspiringQuotes.length'. Either include it or remove the dependency array.", ["988"], "'Fragment' is defined but never used.", "'getTimerColor' is assigned a value but never used.", "'toggleTheme' is assigned a value but never used.", "'motion' is defined but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'containerRef' is assigned a value but never used.", "'TbMedal' is defined but never used.", "'TbAward' is defined but never used.", "'TbDiamond' is defined but never used.", "'triggerLevelUpAnimation' is assigned a value but never used.", "'xpAwarded' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["989"], "'React' is defined but never used.", "'user' is assigned a value but never used.", "'TbChevronRight' is defined but never used.", "'useCallback' is defined but never used.", "'TbFlag' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["990"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'TbTrash' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchNotifications' and 'notifications.length'. Either include them or remove the dependency array.", ["991"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["992"], ["993"], "'TbEdit' is defined but never used.", "'TbUsers' is defined but never used.", "'updateSyllabus' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSyllabuses'. Either include it or remove the dependency array.", ["994"], {"desc": "995", "fix": "996"}, {"desc": "997", "fix": "998"}, {"desc": "999", "fix": "1000"}, {"desc": "1001", "fix": "1002"}, {"desc": "1003", "fix": "1004"}, {"desc": "1005", "fix": "1006"}, {"desc": "1007", "fix": "1008"}, {"desc": "1009", "fix": "1010"}, {"desc": "1011", "fix": "1012"}, {"desc": "1013", "fix": "1014"}, {"desc": "1015", "fix": "1016"}, {"desc": "1017", "fix": "1018"}, {"desc": "1019", "fix": "1020"}, {"desc": "1021", "fix": "1022"}, {"desc": "1023", "fix": "1024"}, {"desc": "1025", "fix": "1026"}, {"desc": "1027", "fix": "1028"}, {"desc": "1029", "fix": "1030"}, {"desc": "1025", "fix": "1031"}, {"desc": "1025", "fix": "1032"}, {"desc": "1033", "fix": "1034"}, {"desc": "1035", "fix": "1036"}, {"messageId": "1037", "fix": "1038", "desc": "1039"}, {"messageId": "1040", "fix": "1041", "desc": "1042"}, {"messageId": "1037", "fix": "1043", "desc": "1039"}, {"messageId": "1040", "fix": "1044", "desc": "1042"}, {"desc": "1045", "fix": "1046"}, {"kind": "1047", "justification": "1048"}, {"desc": "1049", "fix": "1050"}, {"desc": "1051", "fix": "1052"}, {"desc": "1053", "fix": "1054"}, {"desc": "1055", "fix": "1056"}, {"desc": "1057", "fix": "1058"}, {"desc": "1059", "fix": "1060"}, {"desc": "1061", "fix": "1062"}, {"desc": "1063", "fix": "1064"}, {"desc": "1065", "fix": "1066"}, {"desc": "1067", "fix": "1068"}, {"desc": "1069", "fix": "1070"}, {"kind": "1047", "justification": "1048"}, {"desc": "1071", "fix": "1072"}, "Update the dependencies array to be: [getUserData, navigate]", {"range": "1073", "text": "1074"}, "Update the dependencies array to be: [dispatch, paymentVerificationNeeded, user?.isAdmin, user?.paymentRequired, verifyPaymentStatus]", {"range": "1075", "text": "1076"}, "Update the dependencies array to be: [user, activeRoute, verifyPaymentStatus]", {"range": "1077", "text": "1078"}, "Update the dependencies array to be: [getExamData, params.id]", {"range": "1079", "text": "1080"}, "Update the dependencies array to be: [getExamsData]", {"range": "1081", "text": "1082"}, "Update the dependencies array to be: [user, subscriptionData, paymentInProgress]", {"range": "1083", "text": "1084"}, "Update the dependencies array to be: [user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", {"range": "1085", "text": "1086"}, "Update the dependencies array to be: [getData]", {"range": "1087", "text": "1088"}, "Update the dependencies array to be: [dispatch, user.level, user.class, user._id, getUserResults]", {"range": "1089", "text": "1090"}, "Update the dependencies array to be: [getUserResults]", {"range": "1091", "text": "1092"}, "Update the dependencies array to be: [fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", {"range": "1093", "text": "1094"}, "Update the dependencies array to be: [dispatch, getUserData]", {"range": "1095", "text": "1096"}, "Update the dependencies array to be: [filters, getData, pagination]", {"range": "1097", "text": "1098"}, "Update the dependencies array to be: [getUsersData]", {"range": "1099", "text": "1100"}, "Update the dependencies array to be: [currentPage, fetchQuestions, limit]", {"range": "1101", "text": "1102"}, "Update the dependencies array to be: [getUserData]", {"range": "1103", "text": "1104"}, "Update the dependencies array to be: [editQuestion, form2]", {"range": "1105", "text": "1106"}, "Update the dependencies array to be: [getUserStats, rankingData, userDetails]", {"range": "1107", "text": "1108"}, {"range": "1109", "text": "1104"}, {"range": "1110", "text": "1104"}, "Update the dependencies array to be: [fetchUserRankingData, userDetails]", {"range": "1111", "text": "1112"}, "Update the dependencies array to be: [getReviews]", {"range": "1113", "text": "1114"}, "removeEscape", {"range": "1115", "text": "1048"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1116", "text": "1117"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1118", "text": "1048"}, {"range": "1119", "text": "1117"}, "Update the dependencies array to be: [modalIsOpen, documentUrl, renderPDF]", {"range": "1120", "text": "1121"}, "directive", "", "Update the dependencies array to be: [fetchMaterials, filters]", {"range": "1122", "text": "1123"}, "Update the dependencies array to be: [fetchGenerationHistory]", {"range": "1124", "text": "1125"}, "Update the dependencies array to be: [handleAutoRefresh, visible]", {"range": "1126", "text": "1127"}, "Update the dependencies array to be: [user, dispatch, questions, startTime, examData?.duration, examData.passingPercentage, examData.passingMarks, id, navigate, selectedOptions]", {"range": "1128", "text": "1129"}, "Update the dependencies array to be: [getExamData, id]", {"range": "1130", "text": "1131"}, "Update the dependencies array to be: [examData, questions, startTimer]", {"range": "1132", "text": "1133"}, "Update the dependencies array to be: [inspiringQuotes.length]", {"range": "1134", "text": "1135"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1136", "text": "1137"}, "Update the dependencies array to be: [handleSubmit, timeRemaining]", {"range": "1138", "text": "1139"}, "Update the dependencies array to be: [fetchNotifications, isOpen, notifications.length]", {"range": "1140", "text": "1141"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1142", "text": "1143"}, "Update the dependencies array to be: [fetchSyllabuses, filters]", {"range": "1144", "text": "1145"}, [2245, 2247], "[get<PERSON><PERSON><PERSON><PERSON>, navigate]", [3612, 3639], "[dispatch, paymentVerificationNeeded, user?.isAdmin, user?.paymentRequired, verifyPaymentStatus]", [3800, 3819], "[user, activeRoute, verifyPaymentStatus]", [2375, 2377], "[getExamData, params.id]", [2437, 2439], "[getExamsData]", [2446, 2470], "[user, subscriptionData, paymentInProgress]", [28756, 28831], "[user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", [1901, 1903], "[getData]", [3639, 3686], "[dispatch, user.level, user.class, user._id, getUserResults]", [4536, 4538], "[getUserResults]", [29431, 29433], "[fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", [1990, 1992], "[dispatch, getUserData]", [2776, 2805], "[filters, getData, pagination]", [5429, 5431], "[getUsersData]", [2323, 2343], "[currentPage, fetchQuestions, limit]", [3091, 3093], "[getUserData]", [6202, 6216], "[editQuestion, form2]", [3556, 3582], "[getUserStats, rankingData, userDetails]", [4524, 4526], [9206, 9208], [9358, 9371], "[fetchUserRankingData, userDetails]", [1400, 1402], "[getReviews]", [3500, 3501], [3500, 3500], "\\", [5011, 5012], [5011, 5011], [3978, 4004], "[modalIsOpen, documentUrl, renderPDF]", [2411, 2420], "[fetchMaterials, filters]", [1203, 1205], "[fetchGenerationHistory]", [1110, 1119], "[handleAutoRefresh, visible]", [6318, 6386], "[user, dispatch, questions, startTime, examData?.duration, examData.passingPercentage, examData.passingMarks, id, navigate, selectedOptions]", [7110, 7112], "[getExamData, id]", [7377, 7398], "[examData, questions, startTimer]", [1591, 1593], "[inspiringQuotes.length]", [920, 922], "[fetchDashboardData]", [1063, 1078], "[handleSubmit, timeRemaining]", [2363, 2371], "[fetchNotifications, isOpen, notifications.length]", [1190, 1192], "[fetchUsers]", [1373, 1382], "[fetchSyllabuses, filters]"]