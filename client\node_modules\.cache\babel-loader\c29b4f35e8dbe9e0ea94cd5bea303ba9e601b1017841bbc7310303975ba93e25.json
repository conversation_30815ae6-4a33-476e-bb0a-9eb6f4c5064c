{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\AIQuestionGeneration\\\\AutoGenerateExamModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { Modal, Form, Row, Col, Input, Select, InputNumber, Button, message, Alert, Divider } from \"antd\";\nimport { FaRobot, FaMagic } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addExam } from \"../../../apicalls/exams\";\nimport { generateExamName } from \"../../../apicalls/aiQuestions\";\nimport { getSubjectsForLevel } from \"../../../apicalls/aiQuestions\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction AutoGenerateExamModal({\n  visible,\n  onCancel,\n  onSuccess,\n  prefilledData = {}\n}) {\n  _s();\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [level, setLevel] = useState(prefilledData.level || \"\");\n  // Remove unused selectedSubjects state - cleaned up\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [autoGeneratedName, setAutoGeneratedName] = useState(\"\");\n  const [isGeneratingName, setIsGeneratingName] = useState(false);\n  const [passRate, setPassRate] = useState(0);\n  const calculatePassRate = useCallback(values => {\n    const totalMarks = (values === null || values === void 0 ? void 0 : values.totalMarks) || 0;\n    const passingMarks = (values === null || values === void 0 ? void 0 : values.passingMarks) || 0;\n    if (totalMarks > 0 && passingMarks > 0) {\n      return Math.round(passingMarks / totalMarks * 100);\n    }\n    return 0;\n  }, []);\n  const handleLevelChange = useCallback(async selectedLevel => {\n    console.log(`🎯 Level changed to: ${selectedLevel}`);\n    setLevel(selectedLevel);\n    form.setFieldsValue({\n      class: undefined,\n      category: \"\"\n    });\n\n    // Fetch available subjects based on level from syllabus or fallback to hardcoded\n    try {\n      console.log(`🔍 Fetching subjects for level: ${selectedLevel}`);\n      const response = await getSubjectsForLevel(selectedLevel);\n      console.log(`📊 Response received:`, response);\n      if (response.success && response.data && response.data.length > 0) {\n        setAvailableSubjects(response.data);\n        console.log(`✅ Loaded ${response.data.length} subjects for ${selectedLevel}:`, response.data);\n      } else {\n        console.warn(`⚠️ No subjects found for ${selectedLevel}:`, response);\n        // Try fallback to hardcoded subjects\n        let fallbackSubjects = [];\n        switch (selectedLevel) {\n          case \"primary\":\n            fallbackSubjects = primarySubjects;\n            break;\n          case \"secondary\":\n            fallbackSubjects = secondarySubjects;\n            break;\n          case \"advance\":\n            fallbackSubjects = advanceSubjects;\n            break;\n          default:\n            fallbackSubjects = [];\n        }\n        setAvailableSubjects(fallbackSubjects);\n        console.log(`🔄 Using fallback subjects for ${selectedLevel}:`, fallbackSubjects);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching subjects:', error);\n      // Fallback to hardcoded subjects on error\n      let fallbackSubjects = [];\n      switch (selectedLevel) {\n        case \"primary\":\n          fallbackSubjects = primarySubjects;\n          break;\n        case \"secondary\":\n          fallbackSubjects = secondarySubjects;\n          break;\n        case \"advance\":\n          fallbackSubjects = advanceSubjects;\n          break;\n        default:\n          fallbackSubjects = [];\n      }\n      setAvailableSubjects(fallbackSubjects);\n      console.log(`🔄 Error fallback subjects for ${selectedLevel}:`, fallbackSubjects);\n    }\n  }, [form]);\n  const handleFormValuesChange = useCallback((changedValues, allValues) => {\n    if (changedValues.totalMarks !== undefined || changedValues.passingMarks !== undefined) {\n      setPassRate(calculatePassRate(allValues));\n    }\n  }, [calculatePassRate]);\n  useEffect(() => {\n    if (visible) {\n      // Reset form when modal opens\n      form.resetFields();\n      setLevel(prefilledData.level || \"\");\n\n      // Set initial values if provided\n      if (prefilledData.level) {\n        var _prefilledData$subjec;\n        form.setFieldsValue({\n          level: prefilledData.level,\n          class: prefilledData.class,\n          category: ((_prefilledData$subjec = prefilledData.subjects) === null || _prefilledData$subjec === void 0 ? void 0 : _prefilledData$subjec[0]) || \"\"\n        });\n        handleLevelChange(prefilledData.level);\n      }\n\n      // Initialize pass rate with default values\n      setPassRate(calculatePassRate({\n        totalMarks: 100,\n        passingMarks: 50\n      }));\n    }\n  }, [visible, prefilledData, form, handleLevelChange, calculatePassRate]);\n  const handleAutoGenerateName = async () => {\n    const currentValues = form.getFieldsValue();\n    const {\n      level: formLevel,\n      class: formClass,\n      category\n    } = currentValues;\n    if (!formLevel || !formClass || !category) {\n      message.warning(\"Please select level, class, and category first\");\n      return;\n    }\n    try {\n      setIsGeneratingName(true);\n      const response = await generateExamName(formLevel, formClass, [category]);\n      if (response.success) {\n        setAutoGeneratedName(response.data.examName);\n        form.setFieldsValue({\n          name: response.data.examName\n        });\n        message.success(\"Exam name generated successfully!\");\n      } else {\n        message.error(\"Failed to generate exam name\");\n      }\n    } catch (error) {\n      message.error(\"Error generating exam name\");\n    } finally {\n      setIsGeneratingName(false);\n    }\n  };\n  const onFinish = async values => {\n    try {\n      dispatch(ShowLoading());\n\n      // Prepare exam data following the same structure as manual creation\n      const examData = {\n        name: values.name,\n        duration: values.duration,\n        level: values.level,\n        category: values.category,\n        class: values.class,\n        totalMarks: values.totalMarks,\n        passingMarks: values.passingMarks,\n        description: values.description || `Auto-generated exam for ${values.category} - ${values.level} Level Class ${values.class}`,\n        isPublic: false,\n        // Default to private\n        questions: [] // Start with empty questions array\n      };\n\n      const response = await addExam(examData);\n      if (response.success) {\n        message.success(\"Exam created successfully!\");\n        // Ensure we have valid exam data before passing it back\n        if (response.data && response.data._id) {\n          onSuccess(response.data); // Pass the created exam data back\n        } else {\n          // If no data returned, create a minimal exam object for the UI\n          const fallbackExam = {\n            _id: Date.now().toString(),\n            // Temporary ID\n            name: examData.name,\n            category: examData.category,\n            level: examData.level,\n            class: examData.class\n          };\n          onSuccess(fallbackExam);\n        }\n        form.resetFields();\n        setAutoGeneratedName(\"\");\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to create exam\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const getClassOptions = () => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"];\n      case \"advance\":\n        return [\"Form-5\", \"Form-6\"];\n      default:\n        return [];\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 12\n      },\n      children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n        style: {\n          color: \"#1890ff\",\n          fontSize: 20\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Auto-Generate Exam\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 9\n    }, this),\n    open: visible,\n    onCancel: onCancel,\n    footer: null,\n    width: 800,\n    destroyOnClose: true,\n    focusTriggerAfterClose: false,\n    maskClosable: false,\n    children: [/*#__PURE__*/_jsxDEV(Alert, {\n      message: \"Create New Exam\",\n      description: \"This will create a new exam following the same structure as manual exam creation. You can then use this exam for AI question generation.\",\n      type: \"info\",\n      showIcon: true,\n      style: {\n        marginBottom: 24\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: form,\n      layout: \"vertical\",\n      onFinish: onFinish,\n      onValuesChange: handleFormValuesChange,\n      initialValues: {\n        duration: 3600,\n        // Default 1 hour\n        totalMarks: 100,\n        passingMarks: 50\n      },\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"Exam Name\",\n            rules: [{\n              required: true,\n              message: \"Please enter exam name\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"Enter exam name or auto-generate\",\n              suffix: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(FaMagic, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 27\n                }, this),\n                onClick: handleAutoGenerateName,\n                loading: isGeneratingName,\n                size: \"small\",\n                children: \"Auto-Generate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"duration\",\n            label: \"Exam Duration (Seconds)\",\n            rules: [{\n              required: true,\n              message: \"Please enter duration\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 300 // Minimum 5 minutes\n              ,\n              max: 14400 // Maximum 4 hours\n              ,\n              style: {\n                width: \"100%\"\n              },\n              placeholder: \"Duration in seconds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"level\",\n            label: \"Level\",\n            rules: [{\n              required: true,\n              message: \"Please select level\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Level\",\n              onChange: handleLevelChange,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"Primary\",\n                children: \"Primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Secondary\",\n                children: \"Secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Advance\",\n                children: \"Advance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"class\",\n            label: \"Class\",\n            rules: [{\n              required: true,\n              message: \"Please select class\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Class\",\n              disabled: !level,\n              children: getClassOptions().map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                value: cls,\n                children: cls\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"category\",\n            label: \"Category (Subject)\",\n            rules: [{\n              required: true,\n              message: \"Please select category\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Category\",\n              disabled: !level,\n              children: availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(Option, {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"totalMarks\",\n            label: \"Total Marks\",\n            rules: [{\n              required: true,\n              message: \"Please enter total marks\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 1,\n              max: 1000,\n              style: {\n                width: \"100%\"\n              },\n              placeholder: \"Total marks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"passingMarks\",\n            label: \"Passing Marks\",\n            rules: [{\n              required: true,\n              message: \"Please enter passing marks\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 1,\n              max: 1000,\n              style: {\n                width: \"100%\"\n              },\n              placeholder: \"Passing marks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              paddingTop: 30\n            },\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              message: `Pass Rate: ${passRate}%`,\n              type: \"info\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"description\",\n            label: \"Description (Optional)\",\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n              rows: 3,\n              placeholder: \"Enter exam description (optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), autoGeneratedName && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Auto-Generated Name\",\n        description: `Generated exam name: ${autoGeneratedName}`,\n        type: \"success\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          justifyContent: \"flex-end\",\n          gap: 12\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: onCancel,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          icon: /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 58\n          }, this),\n          children: \"Create Exam\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 5\n  }, this);\n}\n_s(AutoGenerateExamModal, \"+tiUShACfGgOd7LjkkJb62nPMLc=\", false, function () {\n  return [useDispatch, Form.useForm];\n});\n_c = AutoGenerateExamModal;\nexport default AutoGenerateExamModal;\nvar _c;\n$RefreshReg$(_c, \"AutoGenerateExamModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "Modal", "Form", "Row", "Col", "Input", "Select", "InputNumber", "<PERSON><PERSON>", "message", "<PERSON><PERSON>", "Divider", "FaRobot", "FaMagic", "HideLoading", "ShowLoading", "addExam", "generateExamName", "getSubjectsForLevel", "primarySubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "Option", "AutoGenerateExamModal", "visible", "onCancel", "onSuccess", "prefilledData", "_s", "dispatch", "form", "useForm", "level", "setLevel", "availableSubjects", "setAvailableSubjects", "autoGeneratedName", "setAutoGeneratedName", "isGeneratingName", "setIsGeneratingName", "passRate", "setPassRate", "calculatePassRate", "values", "totalMarks", "passingMarks", "Math", "round", "handleLevelChange", "selectedLevel", "console", "log", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "class", "undefined", "category", "response", "success", "data", "length", "warn", "fallbackSubjects", "error", "handleFormValuesChange", "changedValues", "allValues", "resetFields", "_prefilledData$subjec", "subjects", "handleAutoGenerateName", "currentV<PERSON>ues", "getFieldsValue", "formLevel", "formClass", "warning", "examName", "name", "onFinish", "examData", "duration", "description", "isPublic", "questions", "_id", "fallbackExam", "Date", "now", "toString", "getClassOptions", "title", "style", "display", "alignItems", "gap", "children", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "open", "footer", "width", "destroyOnClose", "focusTriggerAfterClose", "maskClosable", "type", "showIcon", "marginBottom", "layout", "onValuesChange", "initialValues", "gutter", "xs", "md", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "suffix", "icon", "onClick", "loading", "size", "min", "max", "onChange", "value", "disabled", "map", "cls", "subject", "paddingTop", "TextArea", "rows", "justifyContent", "htmlType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/AIQuestionGeneration/AutoGenerateExamModal.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { \n  Modal, \n  Form, \n  Row, \n  Col, \n  Input, \n  Select, \n  InputNumber, \n  Button, \n  message,\n  Alert,\n  Divider\n} from \"antd\";\nimport { FaRobot, FaMagic } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addExam } from \"../../../apicalls/exams\";\nimport { generateExamName } from \"../../../apicalls/aiQuestions\";\nimport { getSubjectsForLevel } from \"../../../apicalls/aiQuestions\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\n\nconst { Option } = Select;\n\nfunction AutoGenerateExamModal({ \n  visible, \n  onCancel, \n  onSuccess, \n  prefilledData = {} \n}) {\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [level, setLevel] = useState(prefilledData.level || \"\");\n  // Remove unused selectedSubjects state - cleaned up\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [autoGeneratedName, setAutoGeneratedName] = useState(\"\");\n  const [isGeneratingName, setIsGeneratingName] = useState(false);\n  const [passRate, setPassRate] = useState(0);\n\n  const calculatePassRate = useCallback((values) => {\n    const totalMarks = values?.totalMarks || 0;\n    const passingMarks = values?.passingMarks || 0;\n    if (totalMarks > 0 && passingMarks > 0) {\n      return Math.round((passingMarks / totalMarks) * 100);\n    }\n    return 0;\n  }, []);\n\n  const handleLevelChange = useCallback(async (selectedLevel) => {\n    console.log(`🎯 Level changed to: ${selectedLevel}`);\n    setLevel(selectedLevel);\n    form.setFieldsValue({ class: undefined, category: \"\" });\n\n    // Fetch available subjects based on level from syllabus or fallback to hardcoded\n    try {\n      console.log(`🔍 Fetching subjects for level: ${selectedLevel}`);\n      const response = await getSubjectsForLevel(selectedLevel);\n      console.log(`📊 Response received:`, response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        setAvailableSubjects(response.data);\n        console.log(`✅ Loaded ${response.data.length} subjects for ${selectedLevel}:`, response.data);\n      } else {\n        console.warn(`⚠️ No subjects found for ${selectedLevel}:`, response);\n        // Try fallback to hardcoded subjects\n        let fallbackSubjects = [];\n        switch (selectedLevel) {\n          case \"primary\":\n            fallbackSubjects = primarySubjects;\n            break;\n          case \"secondary\":\n            fallbackSubjects = secondarySubjects;\n            break;\n          case \"advance\":\n            fallbackSubjects = advanceSubjects;\n            break;\n          default:\n            fallbackSubjects = [];\n        }\n        setAvailableSubjects(fallbackSubjects);\n        console.log(`🔄 Using fallback subjects for ${selectedLevel}:`, fallbackSubjects);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching subjects:', error);\n      // Fallback to hardcoded subjects on error\n      let fallbackSubjects = [];\n      switch (selectedLevel) {\n        case \"primary\":\n          fallbackSubjects = primarySubjects;\n          break;\n        case \"secondary\":\n          fallbackSubjects = secondarySubjects;\n          break;\n        case \"advance\":\n          fallbackSubjects = advanceSubjects;\n          break;\n        default:\n          fallbackSubjects = [];\n      }\n      setAvailableSubjects(fallbackSubjects);\n      console.log(`🔄 Error fallback subjects for ${selectedLevel}:`, fallbackSubjects);\n    }\n  }, [form]);\n\n  const handleFormValuesChange = useCallback((changedValues, allValues) => {\n    if (changedValues.totalMarks !== undefined || changedValues.passingMarks !== undefined) {\n      setPassRate(calculatePassRate(allValues));\n    }\n  }, [calculatePassRate]);\n\n  useEffect(() => {\n    if (visible) {\n      // Reset form when modal opens\n      form.resetFields();\n      setLevel(prefilledData.level || \"\");\n\n      // Set initial values if provided\n      if (prefilledData.level) {\n        form.setFieldsValue({\n          level: prefilledData.level,\n          class: prefilledData.class,\n          category: prefilledData.subjects?.[0] || \"\",\n        });\n        handleLevelChange(prefilledData.level);\n      }\n\n      // Initialize pass rate with default values\n      setPassRate(calculatePassRate({ totalMarks: 100, passingMarks: 50 }));\n    }\n  }, [visible, prefilledData, form, handleLevelChange, calculatePassRate]);\n\n  const handleAutoGenerateName = async () => {\n    const currentValues = form.getFieldsValue();\n    const { level: formLevel, class: formClass, category } = currentValues;\n    \n    if (!formLevel || !formClass || !category) {\n      message.warning(\"Please select level, class, and category first\");\n      return;\n    }\n\n    try {\n      setIsGeneratingName(true);\n      const response = await generateExamName(formLevel, formClass, [category]);\n      \n      if (response.success) {\n        setAutoGeneratedName(response.data.examName);\n        form.setFieldsValue({ name: response.data.examName });\n        message.success(\"Exam name generated successfully!\");\n      } else {\n        message.error(\"Failed to generate exam name\");\n      }\n    } catch (error) {\n      message.error(\"Error generating exam name\");\n    } finally {\n      setIsGeneratingName(false);\n    }\n  };\n\n  const onFinish = async (values) => {\n    try {\n      dispatch(ShowLoading());\n      \n      // Prepare exam data following the same structure as manual creation\n      const examData = {\n        name: values.name,\n        duration: values.duration,\n        level: values.level,\n        category: values.category,\n        class: values.class,\n        totalMarks: values.totalMarks,\n        passingMarks: values.passingMarks,\n        description: values.description || `Auto-generated exam for ${values.category} - ${values.level} Level Class ${values.class}`,\n        isPublic: false, // Default to private\n        questions: [], // Start with empty questions array\n      };\n\n      const response = await addExam(examData);\n\n      if (response.success) {\n        message.success(\"Exam created successfully!\");\n        // Ensure we have valid exam data before passing it back\n        if (response.data && response.data._id) {\n          onSuccess(response.data); // Pass the created exam data back\n        } else {\n          // If no data returned, create a minimal exam object for the UI\n          const fallbackExam = {\n            _id: Date.now().toString(), // Temporary ID\n            name: examData.name,\n            category: examData.category,\n            level: examData.level,\n            class: examData.class,\n          };\n          onSuccess(fallbackExam);\n        }\n        form.resetFields();\n        setAutoGeneratedName(\"\");\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to create exam\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const getClassOptions = () => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"];\n      case \"advance\":\n        return [\"Form-5\", \"Form-6\"];\n      default:\n        return [];\n    }\n  };\n\n  return (\n    <Modal\n      title={\n        <div style={{ display: \"flex\", alignItems: \"center\", gap: 12 }}>\n          <FaRobot style={{ color: \"#1890ff\", fontSize: 20 }} />\n          <span>Auto-Generate Exam</span>\n        </div>\n      }\n      open={visible}\n      onCancel={onCancel}\n      footer={null}\n      width={800}\n      destroyOnClose\n      focusTriggerAfterClose={false}\n      maskClosable={false}\n    >\n      <Alert\n        message=\"Create New Exam\"\n        description=\"This will create a new exam following the same structure as manual exam creation. You can then use this exam for AI question generation.\"\n        type=\"info\"\n        showIcon\n        style={{ marginBottom: 24 }}\n      />\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={onFinish}\n        onValuesChange={handleFormValuesChange}\n        initialValues={{\n          duration: 3600, // Default 1 hour\n          totalMarks: 100,\n          passingMarks: 50,\n        }}\n      >\n        <Row gutter={[16, 16]}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"name\"\n              label=\"Exam Name\"\n              rules={[{ required: true, message: \"Please enter exam name\" }]}\n            >\n              <Input \n                placeholder=\"Enter exam name or auto-generate\"\n                suffix={\n                  <Button\n                    type=\"link\"\n                    icon={<FaMagic />}\n                    onClick={handleAutoGenerateName}\n                    loading={isGeneratingName}\n                    size=\"small\"\n                  >\n                    Auto-Generate\n                  </Button>\n                }\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"duration\"\n              label=\"Exam Duration (Seconds)\"\n              rules={[{ required: true, message: \"Please enter duration\" }]}\n            >\n              <InputNumber\n                min={300} // Minimum 5 minutes\n                max={14400} // Maximum 4 hours\n                style={{ width: \"100%\" }}\n                placeholder=\"Duration in seconds\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"level\"\n              label=\"Level\"\n              rules={[{ required: true, message: \"Please select level\" }]}\n            >\n              <Select\n                placeholder=\"Select Level\"\n                onChange={handleLevelChange}\n              >\n                <Option value=\"Primary\">Primary</Option>\n                <Option value=\"Secondary\">Secondary</Option>\n                <Option value=\"Advance\">Advance</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"class\"\n              label=\"Class\"\n              rules={[{ required: true, message: \"Please select class\" }]}\n            >\n              <Select placeholder=\"Select Class\" disabled={!level}>\n                {getClassOptions().map((cls) => (\n                  <Option key={cls} value={cls}>\n                    {cls}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"category\"\n              label=\"Category (Subject)\"\n              rules={[{ required: true, message: \"Please select category\" }]}\n            >\n              <Select placeholder=\"Select Category\" disabled={!level}>\n                {availableSubjects.map((subject) => (\n                  <Option key={subject} value={subject}>\n                    {subject}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"totalMarks\"\n              label=\"Total Marks\"\n              rules={[{ required: true, message: \"Please enter total marks\" }]}\n            >\n              <InputNumber\n                min={1}\n                max={1000}\n                style={{ width: \"100%\" }}\n                placeholder=\"Total marks\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"passingMarks\"\n              label=\"Passing Marks\"\n              rules={[{ required: true, message: \"Please enter passing marks\" }]}\n            >\n              <InputNumber\n                min={1}\n                max={1000}\n                style={{ width: \"100%\" }}\n                placeholder=\"Passing marks\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <div style={{ paddingTop: 30 }}>\n              <Alert\n                message={`Pass Rate: ${passRate}%`}\n                type=\"info\"\n                size=\"small\"\n              />\n            </div>\n          </Col>\n\n          <Col xs={24}>\n            <Form.Item\n              name=\"description\"\n              label=\"Description (Optional)\"\n            >\n              <Input.TextArea\n                rows={3}\n                placeholder=\"Enter exam description (optional)\"\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        {autoGeneratedName && (\n          <Alert\n            message=\"Auto-Generated Name\"\n            description={`Generated exam name: ${autoGeneratedName}`}\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        )}\n\n        <Divider />\n\n        <div style={{ display: \"flex\", justifyContent: \"flex-end\", gap: 12 }}>\n          <Button onClick={onCancel}>\n            Cancel\n          </Button>\n          <Button type=\"primary\" htmlType=\"submit\" icon={<FaRobot />}>\n            Create Exam\n          </Button>\n        </div>\n      </Form>\n    </Modal>\n  );\n}\n\nexport default AutoGenerateExamModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SAASC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AACjD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7F,MAAM;EAAEC;AAAO,CAAC,GAAGlB,MAAM;AAEzB,SAASmB,qBAAqBA,CAAC;EAC7BC,OAAO;EACPC,QAAQ;EACRC,SAAS;EACTC,aAAa,GAAG,CAAC;AACnB,CAAC,EAAE;EAAAC,EAAA;EACD,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgC,IAAI,CAAC,GAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAACgC,aAAa,CAACK,KAAK,IAAI,EAAE,CAAC;EAC7D;EACA,MAAM,CAACE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EAE3C,MAAM+C,iBAAiB,GAAG7C,WAAW,CAAE8C,MAAM,IAAK;IAChD,MAAMC,UAAU,GAAG,CAAAD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,UAAU,KAAI,CAAC;IAC1C,MAAMC,YAAY,GAAG,CAAAF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,YAAY,KAAI,CAAC;IAC9C,IAAID,UAAU,GAAG,CAAC,IAAIC,YAAY,GAAG,CAAC,EAAE;MACtC,OAAOC,IAAI,CAACC,KAAK,CAAEF,YAAY,GAAGD,UAAU,GAAI,GAAG,CAAC;IACtD;IACA,OAAO,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,iBAAiB,GAAGnD,WAAW,CAAC,MAAOoD,aAAa,IAAK;IAC7DC,OAAO,CAACC,GAAG,CAAE,wBAAuBF,aAAc,EAAC,CAAC;IACpDhB,QAAQ,CAACgB,aAAa,CAAC;IACvBnB,IAAI,CAACsB,cAAc,CAAC;MAAEC,KAAK,EAAEC,SAAS;MAAEC,QAAQ,EAAE;IAAG,CAAC,CAAC;;IAEvD;IACA,IAAI;MACFL,OAAO,CAACC,GAAG,CAAE,mCAAkCF,aAAc,EAAC,CAAC;MAC/D,MAAMO,QAAQ,GAAG,MAAMxC,mBAAmB,CAACiC,aAAa,CAAC;MACzDC,OAAO,CAACC,GAAG,CAAE,uBAAsB,EAAEK,QAAQ,CAAC;MAE9C,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjExB,oBAAoB,CAACqB,QAAQ,CAACE,IAAI,CAAC;QACnCR,OAAO,CAACC,GAAG,CAAE,YAAWK,QAAQ,CAACE,IAAI,CAACC,MAAO,iBAAgBV,aAAc,GAAE,EAAEO,QAAQ,CAACE,IAAI,CAAC;MAC/F,CAAC,MAAM;QACLR,OAAO,CAACU,IAAI,CAAE,4BAA2BX,aAAc,GAAE,EAAEO,QAAQ,CAAC;QACpE;QACA,IAAIK,gBAAgB,GAAG,EAAE;QACzB,QAAQZ,aAAa;UACnB,KAAK,SAAS;YACZY,gBAAgB,GAAG5C,eAAe;YAClC;UACF,KAAK,WAAW;YACd4C,gBAAgB,GAAG3C,iBAAiB;YACpC;UACF,KAAK,SAAS;YACZ2C,gBAAgB,GAAG1C,eAAe;YAClC;UACF;YACE0C,gBAAgB,GAAG,EAAE;QACzB;QACA1B,oBAAoB,CAAC0B,gBAAgB,CAAC;QACtCX,OAAO,CAACC,GAAG,CAAE,kCAAiCF,aAAc,GAAE,EAAEY,gBAAgB,CAAC;MACnF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,IAAID,gBAAgB,GAAG,EAAE;MACzB,QAAQZ,aAAa;QACnB,KAAK,SAAS;UACZY,gBAAgB,GAAG5C,eAAe;UAClC;QACF,KAAK,WAAW;UACd4C,gBAAgB,GAAG3C,iBAAiB;UACpC;QACF,KAAK,SAAS;UACZ2C,gBAAgB,GAAG1C,eAAe;UAClC;QACF;UACE0C,gBAAgB,GAAG,EAAE;MACzB;MACA1B,oBAAoB,CAAC0B,gBAAgB,CAAC;MACtCX,OAAO,CAACC,GAAG,CAAE,kCAAiCF,aAAc,GAAE,EAAEY,gBAAgB,CAAC;IACnF;EACF,CAAC,EAAE,CAAC/B,IAAI,CAAC,CAAC;EAEV,MAAMiC,sBAAsB,GAAGlE,WAAW,CAAC,CAACmE,aAAa,EAAEC,SAAS,KAAK;IACvE,IAAID,aAAa,CAACpB,UAAU,KAAKU,SAAS,IAAIU,aAAa,CAACnB,YAAY,KAAKS,SAAS,EAAE;MACtFb,WAAW,CAACC,iBAAiB,CAACuB,SAAS,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAACvB,iBAAiB,CAAC,CAAC;EAEvB9C,SAAS,CAAC,MAAM;IACd,IAAI4B,OAAO,EAAE;MACX;MACAM,IAAI,CAACoC,WAAW,CAAC,CAAC;MAClBjC,QAAQ,CAACN,aAAa,CAACK,KAAK,IAAI,EAAE,CAAC;;MAEnC;MACA,IAAIL,aAAa,CAACK,KAAK,EAAE;QAAA,IAAAmC,qBAAA;QACvBrC,IAAI,CAACsB,cAAc,CAAC;UAClBpB,KAAK,EAAEL,aAAa,CAACK,KAAK;UAC1BqB,KAAK,EAAE1B,aAAa,CAAC0B,KAAK;UAC1BE,QAAQ,EAAE,EAAAY,qBAAA,GAAAxC,aAAa,CAACyC,QAAQ,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAyB,CAAC,CAAC,KAAI;QAC3C,CAAC,CAAC;QACFnB,iBAAiB,CAACrB,aAAa,CAACK,KAAK,CAAC;MACxC;;MAEA;MACAS,WAAW,CAACC,iBAAiB,CAAC;QAAEE,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAG,CAAC,CAAC,CAAC;IACvE;EACF,CAAC,EAAE,CAACrB,OAAO,EAAEG,aAAa,EAAEG,IAAI,EAAEkB,iBAAiB,EAAEN,iBAAiB,CAAC,CAAC;EAExE,MAAM2B,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,MAAMC,aAAa,GAAGxC,IAAI,CAACyC,cAAc,CAAC,CAAC;IAC3C,MAAM;MAAEvC,KAAK,EAAEwC,SAAS;MAAEnB,KAAK,EAAEoB,SAAS;MAAElB;IAAS,CAAC,GAAGe,aAAa;IAEtE,IAAI,CAACE,SAAS,IAAI,CAACC,SAAS,IAAI,CAAClB,QAAQ,EAAE;MACzChD,OAAO,CAACmE,OAAO,CAAC,gDAAgD,CAAC;MACjE;IACF;IAEA,IAAI;MACFnC,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAMiB,QAAQ,GAAG,MAAMzC,gBAAgB,CAACyD,SAAS,EAAEC,SAAS,EAAE,CAAClB,QAAQ,CAAC,CAAC;MAEzE,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpBpB,oBAAoB,CAACmB,QAAQ,CAACE,IAAI,CAACiB,QAAQ,CAAC;QAC5C7C,IAAI,CAACsB,cAAc,CAAC;UAAEwB,IAAI,EAAEpB,QAAQ,CAACE,IAAI,CAACiB;QAAS,CAAC,CAAC;QACrDpE,OAAO,CAACkD,OAAO,CAAC,mCAAmC,CAAC;MACtD,CAAC,MAAM;QACLlD,OAAO,CAACuD,KAAK,CAAC,8BAA8B,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdvD,OAAO,CAACuD,KAAK,CAAC,4BAA4B,CAAC;IAC7C,CAAC,SAAS;MACRvB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMsC,QAAQ,GAAG,MAAOlC,MAAM,IAAK;IACjC,IAAI;MACFd,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAMiE,QAAQ,GAAG;QACfF,IAAI,EAAEjC,MAAM,CAACiC,IAAI;QACjBG,QAAQ,EAAEpC,MAAM,CAACoC,QAAQ;QACzB/C,KAAK,EAAEW,MAAM,CAACX,KAAK;QACnBuB,QAAQ,EAAEZ,MAAM,CAACY,QAAQ;QACzBF,KAAK,EAAEV,MAAM,CAACU,KAAK;QACnBT,UAAU,EAAED,MAAM,CAACC,UAAU;QAC7BC,YAAY,EAAEF,MAAM,CAACE,YAAY;QACjCmC,WAAW,EAAErC,MAAM,CAACqC,WAAW,IAAK,2BAA0BrC,MAAM,CAACY,QAAS,MAAKZ,MAAM,CAACX,KAAM,gBAAeW,MAAM,CAACU,KAAM,EAAC;QAC7H4B,QAAQ,EAAE,KAAK;QAAE;QACjBC,SAAS,EAAE,EAAE,CAAE;MACjB,CAAC;;MAED,MAAM1B,QAAQ,GAAG,MAAM1C,OAAO,CAACgE,QAAQ,CAAC;MAExC,IAAItB,QAAQ,CAACC,OAAO,EAAE;QACpBlD,OAAO,CAACkD,OAAO,CAAC,4BAA4B,CAAC;QAC7C;QACA,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACyB,GAAG,EAAE;UACtCzD,SAAS,CAAC8B,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;QAC5B,CAAC,MAAM;UACL;UACA,MAAM0B,YAAY,GAAG;YACnBD,GAAG,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;YAAE;YAC5BX,IAAI,EAAEE,QAAQ,CAACF,IAAI;YACnBrB,QAAQ,EAAEuB,QAAQ,CAACvB,QAAQ;YAC3BvB,KAAK,EAAE8C,QAAQ,CAAC9C,KAAK;YACrBqB,KAAK,EAAEyB,QAAQ,CAACzB;UAClB,CAAC;UACD3B,SAAS,CAAC0D,YAAY,CAAC;QACzB;QACAtD,IAAI,CAACoC,WAAW,CAAC,CAAC;QAClB7B,oBAAoB,CAAC,EAAE,CAAC;MAC1B,CAAC,MAAM;QACL9B,OAAO,CAACuD,KAAK,CAACN,QAAQ,CAACjD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACdvD,OAAO,CAACuD,KAAK,CAAC,uBAAuB,CAAC;IACxC,CAAC,SAAS;MACRjC,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM4E,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQxD,KAAK;MACX,KAAK,SAAS;QACZ,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC5C,KAAK,WAAW;QACd,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;MACjD,KAAK,SAAS;QACZ,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAC7B;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,oBACEX,OAAA,CAACtB,KAAK;IACJ0F,KAAK,eACHpE,OAAA;MAAKqE,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAG,CAAE;MAAAC,QAAA,gBAC7DzE,OAAA,CAACX,OAAO;QAACgF,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAG;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtD/E,OAAA;QAAAyE,QAAA,EAAM;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACN;IACDC,IAAI,EAAE7E,OAAQ;IACdC,QAAQ,EAAEA,QAAS;IACnB6E,MAAM,EAAE,IAAK;IACbC,KAAK,EAAE,GAAI;IACXC,cAAc;IACdC,sBAAsB,EAAE,KAAM;IAC9BC,YAAY,EAAE,KAAM;IAAAZ,QAAA,gBAEpBzE,OAAA,CAACb,KAAK;MACJD,OAAO,EAAC,iBAAiB;MACzByE,WAAW,EAAC,0IAA0I;MACtJ2B,IAAI,EAAC,MAAM;MACXC,QAAQ;MACRlB,KAAK,EAAE;QAAEmB,YAAY,EAAE;MAAG;IAAE;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAEF/E,OAAA,CAACrB,IAAI;MACH8B,IAAI,EAAEA,IAAK;MACXgF,MAAM,EAAC,UAAU;MACjBjC,QAAQ,EAAEA,QAAS;MACnBkC,cAAc,EAAEhD,sBAAuB;MACvCiD,aAAa,EAAE;QACbjC,QAAQ,EAAE,IAAI;QAAE;QAChBnC,UAAU,EAAE,GAAG;QACfC,YAAY,EAAE;MAChB,CAAE;MAAAiD,QAAA,gBAEFzE,OAAA,CAACpB,GAAG;QAACgH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAnB,QAAA,gBACpBzE,OAAA,CAACnB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAClBzE,OAAA,CAACrB,IAAI,CAACoH,IAAI;YACRxC,IAAI,EAAC,MAAM;YACXyC,KAAK,EAAC,WAAW;YACjBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhH,OAAO,EAAE;YAAyB,CAAC,CAAE;YAAAuF,QAAA,eAE/DzE,OAAA,CAAClB,KAAK;cACJqH,WAAW,EAAC,kCAAkC;cAC9CC,MAAM,eACJpG,OAAA,CAACf,MAAM;gBACLqG,IAAI,EAAC,MAAM;gBACXe,IAAI,eAAErG,OAAA,CAACV,OAAO;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAClBuB,OAAO,EAAEtD,sBAAuB;gBAChCuD,OAAO,EAAEtF,gBAAiB;gBAC1BuF,IAAI,EAAC,OAAO;gBAAA/B,QAAA,EACb;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YACT;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN/E,OAAA,CAACnB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAClBzE,OAAA,CAACrB,IAAI,CAACoH,IAAI;YACRxC,IAAI,EAAC,UAAU;YACfyC,KAAK,EAAC,yBAAyB;YAC/BC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhH,OAAO,EAAE;YAAwB,CAAC,CAAE;YAAAuF,QAAA,eAE9DzE,OAAA,CAAChB,WAAW;cACVyH,GAAG,EAAE,GAAI,CAAC;cAAA;cACVC,GAAG,EAAE,KAAM,CAAC;cAAA;cACZrC,KAAK,EAAE;gBAAEa,KAAK,EAAE;cAAO,CAAE;cACzBiB,WAAW,EAAC;YAAqB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN/E,OAAA,CAACnB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjBzE,OAAA,CAACrB,IAAI,CAACoH,IAAI;YACRxC,IAAI,EAAC,OAAO;YACZyC,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhH,OAAO,EAAE;YAAsB,CAAC,CAAE;YAAAuF,QAAA,eAE5DzE,OAAA,CAACjB,MAAM;cACLoH,WAAW,EAAC,cAAc;cAC1BQ,QAAQ,EAAEhF,iBAAkB;cAAA8C,QAAA,gBAE5BzE,OAAA,CAACC,MAAM;gBAAC2G,KAAK,EAAC,SAAS;gBAAAnC,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC/E,OAAA,CAACC,MAAM;gBAAC2G,KAAK,EAAC,WAAW;gBAAAnC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C/E,OAAA,CAACC,MAAM;gBAAC2G,KAAK,EAAC,SAAS;gBAAAnC,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN/E,OAAA,CAACnB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjBzE,OAAA,CAACrB,IAAI,CAACoH,IAAI;YACRxC,IAAI,EAAC,OAAO;YACZyC,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhH,OAAO,EAAE;YAAsB,CAAC,CAAE;YAAAuF,QAAA,eAE5DzE,OAAA,CAACjB,MAAM;cAACoH,WAAW,EAAC,cAAc;cAACU,QAAQ,EAAE,CAAClG,KAAM;cAAA8D,QAAA,EACjDN,eAAe,CAAC,CAAC,CAAC2C,GAAG,CAAEC,GAAG,iBACzB/G,OAAA,CAACC,MAAM;gBAAW2G,KAAK,EAAEG,GAAI;gBAAAtC,QAAA,EAC1BsC;cAAG,GADOA,GAAG;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN/E,OAAA,CAACnB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjBzE,OAAA,CAACrB,IAAI,CAACoH,IAAI;YACRxC,IAAI,EAAC,UAAU;YACfyC,KAAK,EAAC,oBAAoB;YAC1BC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhH,OAAO,EAAE;YAAyB,CAAC,CAAE;YAAAuF,QAAA,eAE/DzE,OAAA,CAACjB,MAAM;cAACoH,WAAW,EAAC,iBAAiB;cAACU,QAAQ,EAAE,CAAClG,KAAM;cAAA8D,QAAA,EACpD5D,iBAAiB,CAACiG,GAAG,CAAEE,OAAO,iBAC7BhH,OAAA,CAACC,MAAM;gBAAe2G,KAAK,EAAEI,OAAQ;gBAAAvC,QAAA,EAClCuC;cAAO,GADGA,OAAO;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN/E,OAAA,CAACnB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjBzE,OAAA,CAACrB,IAAI,CAACoH,IAAI;YACRxC,IAAI,EAAC,YAAY;YACjByC,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhH,OAAO,EAAE;YAA2B,CAAC,CAAE;YAAAuF,QAAA,eAEjEzE,OAAA,CAAChB,WAAW;cACVyH,GAAG,EAAE,CAAE;cACPC,GAAG,EAAE,IAAK;cACVrC,KAAK,EAAE;gBAAEa,KAAK,EAAE;cAAO,CAAE;cACzBiB,WAAW,EAAC;YAAa;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN/E,OAAA,CAACnB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjBzE,OAAA,CAACrB,IAAI,CAACoH,IAAI;YACRxC,IAAI,EAAC,cAAc;YACnByC,KAAK,EAAC,eAAe;YACrBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEhH,OAAO,EAAE;YAA6B,CAAC,CAAE;YAAAuF,QAAA,eAEnEzE,OAAA,CAAChB,WAAW;cACVyH,GAAG,EAAE,CAAE;cACPC,GAAG,EAAE,IAAK;cACVrC,KAAK,EAAE;gBAAEa,KAAK,EAAE;cAAO,CAAE;cACzBiB,WAAW,EAAC;YAAe;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEN/E,OAAA,CAACnB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjBzE,OAAA;YAAKqE,KAAK,EAAE;cAAE4C,UAAU,EAAE;YAAG,CAAE;YAAAxC,QAAA,eAC7BzE,OAAA,CAACb,KAAK;cACJD,OAAO,EAAG,cAAaiC,QAAS,GAAG;cACnCmE,IAAI,EAAC,MAAM;cACXkB,IAAI,EAAC;YAAO;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/E,OAAA,CAACnB,GAAG;UAACgH,EAAE,EAAE,EAAG;UAAApB,QAAA,eACVzE,OAAA,CAACrB,IAAI,CAACoH,IAAI;YACRxC,IAAI,EAAC,aAAa;YAClByC,KAAK,EAAC,wBAAwB;YAAAvB,QAAA,eAE9BzE,OAAA,CAAClB,KAAK,CAACoI,QAAQ;cACbC,IAAI,EAAE,CAAE;cACRhB,WAAW,EAAC;YAAmC;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELhE,iBAAiB,iBAChBf,OAAA,CAACb,KAAK;QACJD,OAAO,EAAC,qBAAqB;QAC7ByE,WAAW,EAAG,wBAAuB5C,iBAAkB,EAAE;QACzDuE,IAAI,EAAC,SAAS;QACdC,QAAQ;QACRlB,KAAK,EAAE;UAAEmB,YAAY,EAAE;QAAG;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF,eAED/E,OAAA,CAACZ,OAAO;QAAAwF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEX/E,OAAA;QAAKqE,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAE8C,cAAc,EAAE,UAAU;UAAE5C,GAAG,EAAE;QAAG,CAAE;QAAAC,QAAA,gBACnEzE,OAAA,CAACf,MAAM;UAACqH,OAAO,EAAElG,QAAS;UAAAqE,QAAA,EAAC;QAE3B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/E,OAAA,CAACf,MAAM;UAACqG,IAAI,EAAC,SAAS;UAAC+B,QAAQ,EAAC,QAAQ;UAAChB,IAAI,eAAErG,OAAA,CAACX,OAAO;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,EAAC;QAE5D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ;AAACxE,EAAA,CA1YQL,qBAAqB;EAAA,QAMXzB,WAAW,EACbE,IAAI,CAAC+B,OAAO;AAAA;AAAA4G,EAAA,GAPpBpH,qBAAqB;AA4Y9B,eAAeA,qBAAqB;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}