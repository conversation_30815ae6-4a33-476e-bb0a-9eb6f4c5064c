const Notification = require("../models/notificationModel");
const User = require("../models/userModel");

class NotificationService {
  
  // Create a new exam notification
  static async notifyNewExam(examData) {
    try {
      // Get all active users (you can filter by class/level if needed)
      const users = await User.find({ 
        isActive: true,
        // Optionally filter by class/level
        // class: examData.class,
        // level: examData.level
      }).select('_id');
      
      const notifications = users.map(user => ({
        recipient: user._id,
        type: 'new_exam',
        title: '🎯 New Exam Available!',
        message: `A new exam "${examData.name}" has been added for ${examData.subject}. Test your knowledge now!`,
        relatedEntity: {
          entityType: 'exam',
          entityId: examData._id,
          entityData: {
            name: examData.name,
            subject: examData.subject,
            class: examData.class,
            level: examData.level
          }
        },
        actionUrl: `/quiz/${examData._id}/start`,
        priority: 'medium',
        channels: {
          inApp: true,
          email: false,
          push: false
        }
      }));
      
      await Notification.insertMany(notifications);
      console.log(`✅ Created ${notifications.length} new exam notifications`);
      
    } catch (error) {
      console.error('Error creating new exam notifications:', error);
    }
  }
  
  // Create a new study material notification
  static async notifyNewStudyMaterial(materialData) {
    try {
      const users = await User.find({ 
        isActive: true,
        // Filter by relevant criteria
      }).select('_id');
      
      const notifications = users.map(user => ({
        recipient: user._id,
        type: 'new_study_material',
        title: '📚 New Study Material Added!',
        message: `New study material "${materialData.title}" is now available. Enhance your learning!`,
        relatedEntity: {
          entityType: 'study_material',
          entityId: materialData._id,
          entityData: {
            title: materialData.title,
            subject: materialData.subject,
            type: materialData.type
          }
        },
        actionUrl: `/study/materials/${materialData._id}`,
        priority: 'low',
        channels: {
          inApp: true,
          email: false,
          push: false
        }
      }));
      
      await Notification.insertMany(notifications);
      console.log(`✅ Created ${notifications.length} new study material notifications`);
      
    } catch (error) {
      console.error('Error creating study material notifications:', error);
    }
  }
  
  // Notify when a new forum question is posted
  static async notifyNewForumQuestion(questionData, authorId) {
    try {
      // Get all users except the author
      const users = await User.find({ 
        isActive: true,
        _id: { $ne: authorId }
      }).select('_id');
      
      const notifications = users.map(user => ({
        recipient: user._id,
        sender: authorId,
        type: 'forum_question_posted',
        title: '❓ New Forum Question',
        message: `Someone asked: "${questionData.title}". Share your knowledge!`,
        relatedEntity: {
          entityType: 'forum_question',
          entityId: questionData._id,
          entityData: {
            title: questionData.title,
            subject: questionData.subject
          }
        },
        actionUrl: `/forum/question/${questionData._id}`,
        priority: 'low',
        channels: {
          inApp: true,
          email: false,
          push: false
        }
      }));
      
      await Notification.insertMany(notifications);
      console.log(`✅ Created ${notifications.length} forum question notifications`);
      
    } catch (error) {
      console.error('Error creating forum question notifications:', error);
    }
  }
  
  // Notify when someone answers your forum question
  static async notifyForumAnswerReceived(questionData, answererData, questionAuthorId) {
    try {
      const notification = await Notification.createNotification({
        recipient: questionAuthorId,
        sender: answererData._id,
        type: 'forum_answer_received',
        title: '💡 Your Question Got Answered!',
        message: `${answererData.name} answered your question: "${questionData.title}"`,
        relatedEntity: {
          entityType: 'forum_answer',
          entityId: questionData._id,
          entityData: {
            questionTitle: questionData.title,
            answererName: answererData.name
          }
        },
        actionUrl: `/forum/question/${questionData._id}`,
        priority: 'high',
        channels: {
          inApp: true,
          email: true,
          push: false
        }
      });
      
      console.log(`✅ Created forum answer notification for user ${questionAuthorId}`);
      return notification;
      
    } catch (error) {
      console.error('Error creating forum answer notification:', error);
    }
  }
  
  // Notify about level up
  static async notifyLevelUp(userId, newLevel) {
    try {
      const notification = await Notification.createNotification({
        recipient: userId,
        type: 'level_up',
        title: '🎉 Level Up!',
        message: `Congratulations! You've reached Level ${newLevel}! Keep up the great work!`,
        relatedEntity: {
          entityType: 'user',
          entityId: userId,
          entityData: {
            newLevel: newLevel
          }
        },
        actionUrl: '/profile',
        priority: 'high',
        channels: {
          inApp: true,
          email: false,
          push: true
        }
      });
      
      console.log(`✅ Created level up notification for user ${userId}`);
      return notification;
      
    } catch (error) {
      console.error('Error creating level up notification:', error);
    }
  }
  
  // Get user notifications
  static async getUserNotifications(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        unreadOnly = false,
        type = null
      } = options;
      
      const query = {
        recipient: userId,
        isArchived: false
      };
      
      if (unreadOnly) {
        query.isRead = false;
      }
      
      if (type) {
        query.type = type;
      }
      
      const notifications = await Notification.find(query)
        .populate('sender', 'name profilePicture')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);
      
      const total = await Notification.countDocuments(query);
      const unreadCount = await Notification.countDocuments({
        recipient: userId,
        isRead: false,
        isArchived: false
      });
      
      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        unreadCount
      };
      
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  }
  
  // Mark notification as read
  static async markAsRead(notificationId, userId) {
    try {
      const notification = await Notification.findOne({
        _id: notificationId,
        recipient: userId
      });
      
      if (!notification) {
        throw new Error('Notification not found');
      }
      
      return await notification.markAsRead();
      
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }
  
  // Mark all notifications as read
  static async markAllAsRead(userId) {
    try {
      const result = await Notification.updateMany(
        { recipient: userId, isRead: false },
        { isRead: true }
      );
      
      console.log(`✅ Marked ${result.modifiedCount} notifications as read for user ${userId}`);
      return result;
      
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }
}

module.exports = NotificationService;
