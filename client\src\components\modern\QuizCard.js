import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Tb<PERSON><PERSON>,
  TbQuestionMark,
  TbPlayerPlay,
  TbTarget,
  TbBrain,
  TbCheck,
  TbX,
  TbEye,
  TbPhoto,
  TbEdit,
  TbUsers,
  TbAward,
} from 'react-icons/tb';
import {
  extractQuizData,
  getQuizStatus,
  safeString
} from '../../utils/quizDataUtils';
import { getExamStats } from '../../apicalls/exams';

const QuizCard = ({
  quiz,
  onStart,
  onView,
  showResults = false,
  userResult = null,
  className = '',
  ...props
}) => {
  const [showPreview, setShowPreview] = useState(false);
  const [examStats, setExamStats] = useState(null);

  // Extract safe quiz data to prevent object rendering errors
  const quizData = extractQuizData(quiz);
  const quizStatus = getQuizStatus(userResult, quizData.passingMarks);

  // Fetch exam statistics
  useEffect(() => {
    const fetchExamStats = async () => {
      if (quiz?._id) {
        try {
          const response = await getExamStats(quiz._id);
          if (response.success) {
            setExamStats(response.data);
          }
        } catch (error) {
          console.error('Failed to fetch exam stats:', error);
        }
      }
    };

    fetchExamStats();
  }, [quiz?._id]);



  const getDifficultyColor = (difficulty) => {
    switch (safeString(difficulty).toLowerCase()) {
      case 'easy':
        return 'bg-green-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-white';
      case 'hard':
        return 'bg-red-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  // Get sample questions for preview
  const getSampleQuestions = () => {
    if (!Array.isArray(quizData.questions)) return [];

    // Get first 2-3 questions for preview
    return quizData.questions.slice(0, 3).map(question => {
      // Handle both object and string formats
      if (typeof question === 'object') {
        return question;
      }
      // If it's a string (question ID), we can't preview it
      return null;
    }).filter(Boolean);
  };

  const sampleQuestions = getSampleQuestions();

  // Render question preview
  const renderQuestionPreview = (question, index) => {
    const questionType = safeString(question.type || question.answerType);
    const questionName = safeString(question.name || question.question, 'Question');

    return (
      <div key={index} className="mb-3 p-3 bg-gray-50 rounded-lg border">
        <div className="text-sm font-medium text-gray-700 mb-2">
          Q{index + 1}: {questionName.substring(0, 60)}...
        </div>

        {/* Show image if available */}
        {safeString(question.image || question.imageUrl) && (
          <div className="mb-2">
            <div className="flex items-center gap-2 mb-2">
              <TbPhoto className="w-3 h-3 text-gray-500" />
              <span className="text-xs text-gray-500">Question Image:</span>
            </div>
            <div className="relative">
              <img
                src={safeString(question.image || question.imageUrl)}
                alt="Question preview"
                className="w-full h-20 object-cover rounded border border-gray-200"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'block';
                }}
                onLoad={(e) => {
                  e.target.nextSibling.style.display = 'none';
                }}
              />
              <div
                className="w-full h-20 bg-gray-100 rounded border border-gray-200 flex items-center justify-center text-xs text-gray-500"
                style={{ display: 'none' }}
              >
                <div className="text-center">
                  <TbPhoto className="w-4 h-4 mx-auto mb-1" />
                  <div>Image unavailable</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Show question type specific preview */}
        {(questionType === 'mcq' || questionType === 'Options' || question.options) && (
          <div className="space-y-1">
            {Object.entries(question.options || {}).slice(0, 2).map(([key, value]) => {
              // Safely convert value to string - prevent object rendering
              const displayValue = safeString(value, 'Option');
              const optionKey = safeString(key);
              return (
                <div key={optionKey} className="text-xs text-gray-600 flex items-center gap-2">
                  <span className="w-4 h-4 border border-gray-300 rounded text-center text-xs">{optionKey}</span>
                  <span>{displayValue.substring(0, 30)}...</span>
                </div>
              );
            })}
            {Object.keys(question.options || {}).length > 2 && (
              <div className="text-xs text-gray-400">...and more options</div>
            )}
          </div>
        )}

        {/* Fill in the blank preview */}
        {(questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text') && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs text-gray-500 mb-2">
              <TbEdit className="w-3 h-3" />
              <span>Fill in the blank question</span>
            </div>
            <div className="relative">
              <input
                type="text"
                placeholder="Type your answer here..."
                className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                disabled
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <TbEdit className="w-3 h-3 text-gray-400" />
              </div>
            </div>
            {question.correctAnswer && (
              <div className="text-xs text-gray-400 italic">
                Expected answer: {String(question.correctAnswer || '').substring(0, 20)}...
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className={`h-full ${className}`}
    >
      <div
        className={`h-full border-2 shadow-lg hover:shadow-xl transition-all duration-300 relative rounded-xl ${quizStatus.cardBg} ${quizStatus.borderColor} ${quizStatus.textColor} ${
          userResult && quizStatus.status === 'passed'
            ? 'shadow-emerald-200/50'
            : userResult && quizStatus.status === 'failed'
              ? 'shadow-rose-200/50'
              : ''
        } overflow-hidden`}
        {...props}
      >
        <div className="absolute top-3 right-3 z-10">
          {userResult ? (
            <div className="flex flex-col gap-1">
              <div className={`px-2 py-1 rounded-md text-xs font-bold text-white ${quizStatus.statusColor}`}>
                {quizStatus.status === 'passed' ? (
                  <>
                    <TbCheck className="w-3 h-3 inline mr-1" />
                    PASSED
                  </>
                ) : (
                  <>
                    <TbX className="w-3 h-3 inline mr-1" />
                    FAILED
                  </>
                )}
              </div>
              <div className="px-2 py-1 rounded-md text-xs font-medium bg-white text-gray-700 text-center shadow-sm">
                {userResult.percentage}% • {userResult.xpEarned || 0} XP
              </div>
            </div>
          ) : (
            <div className="px-2 py-1 rounded-md text-xs font-bold bg-gray-500 text-white">
              <TbClock className="w-3 h-3 inline mr-1" />
              NOT ATTEMPTED
            </div>
          )}
        </div>

        <div
          className="p-4 text-white"
          style={{
            backgroundColor: !userResult
              ? '#2563eb' // blue-600
              : quizStatus.status === 'passed'
                ? '#059669' // emerald-600
                : '#e11d48' // rose-600
          }}
        >
          {/* Color-coded header based on quiz status */}
          <div className="text-center mb-3">
            <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-3">
              <TbBrain className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-lg font-bold line-clamp-2 leading-tight mb-2">{quizData.name}</h3>
            <div className="flex items-center justify-center gap-2">
              <span
                className="text-xs px-2 py-1 rounded text-white"
                style={{
                  backgroundColor: !userResult
                    ? '#3b82f6' // blue-500
                    : quizStatus.status === 'passed'
                      ? '#10b981' // emerald-500
                      : '#f43f5e' // rose-500
                }}
              >Class {quizData.class}</span>
              {quizData.subject && (
                <span className="text-xs bg-yellow-400 text-yellow-900 px-2 py-1 rounded font-semibold">{quizData.subject}</span>
              )}
            </div>
          </div>

          <div className="flex items-center justify-center gap-3 text-xs">
            <span className="flex items-center gap-1">
              <TbQuestionMark className="w-3 h-3" />
              {quizData.totalQuestions}
            </span>
            <span className="flex items-center gap-1">
              <TbClock className="w-3 h-3" />
              {Math.round(quizData.duration / 60)}m
            </span>
            <span className="flex items-center gap-1">
              <TbTarget className="w-3 h-3" />
              {quizData.passingMarks}%
            </span>
          </div>
        </div>

        <div className="p-4 flex-1 flex flex-col">
          <p className="text-sm text-gray-600 mb-4 line-clamp-2">
            {quizData.description}
          </p>
          <div className="flex flex-wrap gap-2 mb-4">
            {quizData.topic && (
              <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">{quizData.topic}</span>
            )}
            {quizData.difficulty && (
              <span className={`text-xs px-2 py-1 rounded ${getDifficultyColor(quizData.difficulty)}`}>
                {quizData.difficulty}
              </span>
            )}
            {quizData.category && (
              <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">{quizData.category}</span>
            )}
          </div>

          {/* Question Preview Section */}
          {sampleQuestions.length > 0 && (
            <div className="mb-4">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowPreview(!showPreview);
                }}
                className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 font-medium mb-2"
              >
                <TbEye className="w-4 h-4" />
                {showPreview ? 'Hide Preview' : 'Preview Questions'}
              </button>

              {showPreview && (
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {sampleQuestions.map((question, index) => renderQuestionPreview(question, index))}
                  {quizData.totalQuestions > sampleQuestions.length && (
                    <div className="text-xs text-gray-400 text-center py-2">
                      ...and {quizData.totalQuestions - sampleQuestions.length} more questions
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {userResult && (
            <div className={`rounded-xl p-4 mb-4 border-2 shadow-sm ${
              quizStatus.status === 'passed'
                ? 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200'
                : 'bg-gradient-to-br from-red-50 to-rose-50 border-red-200'
            }`}>
              {/* Header with status */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  {quizStatus.status === 'passed' ? (
                    <TbCheck className="w-4 h-4 text-green-600" />
                  ) : (
                    <TbX className="w-4 h-4 text-red-600" />
                  )}
                  <span className="text-sm font-semibold text-gray-700">Last Attempt</span>
                </div>
                <div className={`px-3 py-1 rounded-full text-sm font-bold ${
                  quizStatus.status === 'passed'
                    ? 'bg-green-600 text-white'
                    : 'bg-red-600 text-white'
                }`}>
                  {userResult.percentage}%
                </div>
              </div>

              {/* Stats grid */}
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center gap-2 bg-white/60 rounded-lg px-3 py-2">
                  <TbTarget className="w-4 h-4 text-blue-500" />
                  <div>
                    <div className="text-xs text-gray-500">Correct</div>
                    <div className="text-sm font-semibold text-gray-700">
                      {Array.isArray(userResult.correctAnswers) ? userResult.correctAnswers.length : (userResult.correctAnswers || 0)}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2 bg-white/60 rounded-lg px-3 py-2">
                  <TbAward className="w-4 h-4 text-yellow-500" />
                  <div>
                    <div className="text-xs text-gray-500">XP Earned</div>
                    <div className="text-sm font-semibold text-gray-700">
                      {userResult.xpEarned || userResult.points || userResult.xpGained || 0}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="mt-auto pt-4 border-t border-gray-100">
            {/* Pass Count Display */}
            {examStats && (
              <div className="mb-3 flex items-center justify-center gap-2 text-sm text-gray-600 bg-gray-50 rounded-lg py-2 px-3">
                <TbUsers className="w-4 h-4 text-blue-500" />
                <span className="font-medium">
                  {examStats.uniquePassedUsers} {examStats.uniquePassedUsers === 1 ? 'user' : 'users'} passed
                </span>
              </div>
            )}

            <div className="flex gap-2">
              <button
                onClick={() => onStart && quiz?._id && onStart(quiz)}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <TbPlayerPlay className="w-4 h-4" />
                {showResults && userResult ? 'Retake Quiz' : 'Start Quiz'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export const QuizGrid = ({ quizzes, onQuizStart, showResults = false, userResults = {}, className = '' }) => {
  return (
    <div className={`quiz-grid-container ${className}`}>
      {quizzes.map((quiz, index) => (
        <motion.div
          key={quiz._id || index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: Math.min(index * 0.1, 0.8) }}
          className="h-full"
        >
          <QuizCard
            quiz={quiz}
            onStart={() => onQuizStart(quiz)}
            showResults={showResults}
            userResult={userResults[quiz._id]}
            className="h-full"
          />
        </motion.div>
      ))}
    </div>
  );
};

export default QuizCard;