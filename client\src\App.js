import React, { Suspense } from "react";
import "./stylesheets/theme.css";
import "./stylesheets/alignments.css";
import "./stylesheets/textelements.css";
import "./stylesheets/form-elements.css";
import "./stylesheets/custom-components.css";
import "./stylesheets/layout.css";
import "./styles/modern.css";
import "./styles/animations.css";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Login from "./pages/common/Login";
import Register from "./pages/common/Register";
import ProtectedRoute from "./components/ProtectedRoute";
import Quiz from "./pages/user/Quiz";
import QuizStart from "./pages/user/Quiz/QuizStart";
import QuizPlay from "./pages/user/Quiz/QuizPlay";
import QuizResult from "./pages/user/Quiz/QuizResult";
import ModernQuizPage from "./pages/user/Quiz/ModernQuizPage";
import Exams from "./pages/admin/Exams";
import AddEditExam from "./pages/admin/Exams/AddEditExam";
import Users from "./pages/admin/Users";
import AdminDashboard from "./pages/admin/Dashboard";
import Loader from "./components/Loader";
import { useSelector } from "react-redux";
import WriteExam from "./pages/user/WriteExam";
import UserReports from "./pages/user/UserReports";
import AdminReports from "./pages/admin/AdminReports";
import StudyMaterial from "./pages/user/StudyMaterial";
import Ranking from "./pages/user/Ranking";
import RankingErrorBoundary from "./components/RankingErrorBoundary";
import Profile from "./pages/common/Profile";
import AboutUs from "./pages/user/AboutUs";
import Forum from "./pages/common/Forum";
import Home from "./pages/common/Home";
import Test from "./pages/user/Test";
import Chat from "./pages/user/Chat"
import Plans from "./pages/user/Plans/Plans";
import Hub from "./pages/user/Hub";
import AdminStudyMaterials from "./pages/admin/StudyMaterials";
import AIQuestionGeneration from "./pages/admin/AIQuestionGeneration";
import AdminNotifications from "./pages/admin/Notifications/AdminNotifications";
import { ThemeProvider } from "./contexts/ThemeContext";
import { ErrorBoundary } from "./components/modern";
import RankingDemo from "./components/modern/RankingDemo";
import AdminProtectedRoute from "./components/AdminProtectedRoute";

// Global error handler for CSS style errors
window.addEventListener('error', (event) => {
  if (event.message && event.message.includes('Indexed property setter is not supported')) {
    console.warn('CSS Style Error caught and handled:', event.message);
    event.preventDefault();
    return false;
  }
});

// Handle unhandled promise rejections that might be related to style errors
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message && event.reason.message.includes('Indexed property setter is not supported')) {
    console.warn('CSS Style Promise Rejection caught and handled:', event.reason.message);
    event.preventDefault();
  }
});
// const LazyComponent = lazy(() => import('./pages/user/Test'));

function App() {
  const { loading } = useSelector((state) => state.loader);
  return (
    <ErrorBoundary>
      <ThemeProvider>
        {loading && <Loader />}
        <BrowserRouter>
        <Routes>
          {/* Common Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/" element={<Home />} />
          <Route path="/ranking-demo" element={<RankingDemo />} />
          <Route path="/test" element={
            <Suspense fallback={<div>Loading(App.js)...</div>}>
              <Test />
            </Suspense>
          } />
          <Route
            path="/forum"
            element={
              <ProtectedRoute>
                <Forum />
              </ProtectedRoute>
            }
          />

          {/* User Routes */}
          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/profile"
            element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            }
          />

          <Route
            path="/user/chat"
            element={
              <ProtectedRoute>
                <Chat />
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/plans"
            element={
              <ProtectedRoute>
                <Plans />
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/hub"
            element={
              <ProtectedRoute>
                <Hub />
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/quiz"
            element={
              <ProtectedRoute>
                <Quiz />
              </ProtectedRoute>
            }
          />
          <Route
            path="/modern-quiz"
            element={
              <ProtectedRoute>
                <ModernQuizPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/write-exam/:id"
            element={
              <ProtectedRoute>
                <WriteExam />
              </ProtectedRoute>
            }
          />

          {/* New Quiz Routes */}
          <Route
            path="/quiz/:id/start"
            element={
              <ProtectedRoute>
                <QuizStart />
              </ProtectedRoute>
            }
          />
          <Route
            path="/quiz/:id/play"
            element={
              <ProtectedRoute>
                <QuizPlay />
              </ProtectedRoute>
            }
          />
          <Route
            path="/quiz/:id/result"
            element={
              <ProtectedRoute>
                <QuizResult />
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/reports"
            element={
              <ProtectedRoute>
                <UserReports />
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/study-material"
            element={
              <ProtectedRoute>
                <StudyMaterial />
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/ranking"
            element={
              <ProtectedRoute>
                <RankingErrorBoundary>
                  <Ranking />
                </RankingErrorBoundary>
              </ProtectedRoute>
            }
          />
          <Route
            path="/user/about-us"
            element={
              <ProtectedRoute>
                <AboutUs />
              </ProtectedRoute>
            }
          />

          {/* Admin Routes */}
          <Route
            path="/admin/dashboard"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminDashboard />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/users"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <Users />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/exams"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <Exams />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/exams/add"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AddEditExam />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/exams/edit/:id"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AddEditExam />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/study-materials"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminStudyMaterials />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/ai-questions"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AIQuestionGeneration />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/reports"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminReports />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/notifications"
            element={
              <ProtectedRoute>
                <AdminProtectedRoute>
                  <AdminNotifications />
                </AdminProtectedRoute>
              </ProtectedRoute>
            }
          />
        </Routes>
      </BrowserRouter>
    </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;