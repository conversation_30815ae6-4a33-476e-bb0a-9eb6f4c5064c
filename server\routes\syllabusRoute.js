const router = require("express").Router();
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const authMiddleware = require("../middlewares/authMiddleware");
const Syllabus = require("../models/syllabusModel");
const SyllabusService = require("../services/syllabusService");

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, "../uploads/syllabus");
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1E9);
    const fileName = `syllabus-${uniqueSuffix}${path.extname(file.originalname)}`;
    cb(null, fileName);
  }
});

const fileFilter = (req, file, cb) => {
  if (file.mimetype === "application/pdf") {
    cb(null, true);
  } else {
    cb(new Error("Only PDF files are allowed"), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
});

// Upload syllabus PDF
router.post("/upload", authMiddleware, upload.single("syllabusFile"), async (req, res) => {
  try {
    const userId = req.body.userId;
    const { title, description, level, class: className, subject, academicYear, tags } = req.body;

    // Check if user is admin
    const User = require("../models/userModel");
    const user = await User.findById(userId);
    if (!user || !user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    if (!req.file) {
      return res.status(400).send({
        message: "No file uploaded",
        success: false,
      });
    }

    // Validate required fields
    if (!title || !level || !className || !subject) {
      return res.status(400).send({
        message: "Missing required fields: title, level, class, subject",
        success: false,
      });
    }

    // Check if syllabus already exists for this level/class/subject
    const existingSyllabus = await Syllabus.findOne({
      level,
      class: className,
      subject,
      isActive: true,
    });

    if (existingSyllabus) {
      // Create new version
      existingSyllabus.previousVersions.push({
        version: existingSyllabus.version,
        filePath: existingSyllabus.filePath,
        uploadDate: existingSyllabus.createdAt,
        changes: "New version uploaded",
      });
      existingSyllabus.isActive = false;
      await existingSyllabus.save();
    }

    // Create syllabus record
    const syllabusData = {
      title,
      description,
      level,
      class: className,
      subject,
      fileName: req.file.filename,
      originalFileName: req.file.originalname,
      filePath: req.file.path,
      fileSize: req.file.size,
      mimeType: req.file.mimetype,
      uploadedBy: userId,
      academicYear,
      tags: tags ? tags.split(",").map(tag => tag.trim()) : [],
      processingStatus: "pending",
      extractedText: "", // Will be filled by processing service
    };

    const syllabus = new Syllabus(syllabusData);
    await syllabus.save();

    // Start background processing
    SyllabusService.processUploadedSyllabus(syllabus._id)
      .catch(error => {
        console.error("Error processing syllabus:", error);
      });

    res.send({
      message: "Syllabus uploaded successfully. Processing will begin shortly.",
      success: true,
      data: {
        syllabusId: syllabus._id,
        fileName: syllabus.fileName,
        processingStatus: syllabus.processingStatus,
      },
    });

  } catch (error) {
    console.error("Error uploading syllabus:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get all syllabuses
router.get("/", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { level, subject, class: className, isActive = true } = req.query;

    // Check if user is admin
    const User = require("../models/userModel");
    const user = await User.findById(userId);
    if (!user || !user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    const query = {};
    if (level) query.level = level;
    if (subject) query.subject = subject;
    if (className) query.class = className;
    if (isActive !== undefined) query.isActive = isActive === "true";

    const syllabuses = await Syllabus.find(query)
      .populate("uploadedBy", "name email")
      .populate("approvedBy", "name email")
      .sort({ createdAt: -1 });

    res.send({
      message: "Syllabuses retrieved successfully",
      success: true,
      data: syllabuses,
    });

  } catch (error) {
    console.error("Error fetching syllabuses:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get syllabus by ID
router.get("/:id", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { id } = req.params;

    // Check if user is admin
    const User = require("../models/userModel");
    const user = await User.findById(userId);
    if (!user || !user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    const syllabus = await Syllabus.findById(id)
      .populate("uploadedBy", "name email")
      .populate("approvedBy", "name email");

    if (!syllabus) {
      return res.status(404).send({
        message: "Syllabus not found",
        success: false,
      });
    }

    res.send({
      message: "Syllabus retrieved successfully",
      success: true,
      data: syllabus,
    });

  } catch (error) {
    console.error("Error fetching syllabus:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Update syllabus
router.put("/:id", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { id } = req.params;
    const updates = req.body;

    // Check if user is admin
    const User = require("../models/userModel");
    const user = await User.findById(userId);
    if (!user || !user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    const syllabus = await Syllabus.findByIdAndUpdate(
      id,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!syllabus) {
      return res.status(404).send({
        message: "Syllabus not found",
        success: false,
      });
    }

    res.send({
      message: "Syllabus updated successfully",
      success: true,
      data: syllabus,
    });

  } catch (error) {
    console.error("Error updating syllabus:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Delete syllabus
router.delete("/:id", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { id } = req.params;

    // Check if user is admin
    const User = require("../models/userModel");
    const user = await User.findById(userId);
    if (!user || !user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    const syllabus = await Syllabus.findById(id);
    if (!syllabus) {
      return res.status(404).send({
        message: "Syllabus not found",
        success: false,
      });
    }

    // Delete file from filesystem
    if (fs.existsSync(syllabus.filePath)) {
      fs.unlinkSync(syllabus.filePath);
    }

    await Syllabus.findByIdAndDelete(id);

    res.send({
      message: "Syllabus deleted successfully",
      success: true,
    });

  } catch (error) {
    console.error("Error deleting syllabus:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get available subjects for a level
router.get("/subjects/:level", authMiddleware, async (req, res) => {
  try {
    const { level } = req.params;
    const { class: className } = req.query;

    const subjects = await Syllabus.getAvailableSubjects(level, className);

    res.send({
      message: "Available subjects retrieved successfully",
      success: true,
      data: subjects,
    });

  } catch (error) {
    console.error("Error fetching subjects:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get syllabus content for AI generation
router.get("/ai-content/:level/:class/:subject", authMiddleware, async (req, res) => {
  try {
    const { level, class: className, subject } = req.params;

    const syllabus = await Syllabus.findForAIGeneration(level, className, subject);

    if (!syllabus) {
      return res.status(404).send({
        message: `No syllabus found for ${level} ${subject} Class ${className}`,
        success: false,
      });
    }

    const aiContent = {
      extractedText: syllabus.extractedText,
      topics: syllabus.getTopicsForAI(),
      learningObjectives: syllabus.learningObjectives,
      competencies: syllabus.competencies,
    };

    // Update usage statistics
    await syllabus.updateUsageStats();

    res.send({
      message: "Syllabus content retrieved for AI generation",
      success: true,
      data: aiContent,
    });

  } catch (error) {
    console.error("Error fetching syllabus for AI:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

module.exports = router;
