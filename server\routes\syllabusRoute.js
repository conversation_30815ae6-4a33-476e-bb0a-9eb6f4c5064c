const router = require("express").Router();
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const authMiddleware = require("../middlewares/authMiddleware");
const enhancedAuthMiddleware = require("../middlewares/enhancedAuthMiddleware");
const Syllabus = require("../models/syllabusModel");
const SyllabusService = require("../services/syllabusService");

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, "../uploads/syllabus");
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1E9);
    const fileName = `syllabus-${uniqueSuffix}${path.extname(file.originalname)}`;
    cb(null, fileName);
  }
});

const fileFilter = (req, file, cb) => {
  if (file.mimetype === "application/pdf") {
    cb(null, true);
  } else {
    cb(new Error("Only PDF files are allowed"), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
});

// Upload syllabus PDF
router.post("/upload", enhancedAuthMiddleware, upload.single("syllabusFile"), async (req, res) => {
  try {
    const userId = req.body.userId;
    const { title, description, level, classes, subject, academicYear, tags } = req.body;

    if (!req.file) {
      return res.status(400).send({
        message: "No file uploaded",
        success: false,
      });
    }

    // Parse classes from string to array
    let classesArray;
    try {
      classesArray = typeof classes === 'string' ? classes.split(',').map(c => c.trim()) : classes;
      if (!classesArray || classesArray.length === 0) {
        return res.status(400).send({
          message: "At least one class must be specified",
          success: false,
        });
      }
    } catch (error) {
      return res.status(400).send({
        message: "Invalid classes format. Use comma-separated values (e.g., '5,6,7')",
        success: false,
      });
    }

    // Validate required fields
    if (!title || !level || !classesArray || !subject) {
      return res.status(400).send({
        message: "Missing required fields: title, level, classes, subject",
        success: false,
      });
    }

    // Check if syllabus already exists for this level/classes/subject
    const existingSyllabus = await Syllabus.findOne({
      level,
      classes: { $in: classesArray },
      subject,
      isActive: true,
    });

    if (existingSyllabus) {
      // Create new version
      existingSyllabus.previousVersions.push({
        version: existingSyllabus.version,
        filePath: existingSyllabus.filePath,
        uploadDate: existingSyllabus.createdAt,
        changes: "New version uploaded",
      });
      existingSyllabus.isActive = false;
      await existingSyllabus.save();
    }

    // Create syllabus record
    const syllabusData = {
      title,
      description,
      level,
      classes: classesArray,
      subject,
      fileName: req.file.filename,
      originalFileName: req.file.originalname,
      filePath: req.file.path,
      fileSize: req.file.size,
      mimeType: req.file.mimetype,
      uploadedBy: userId,
      academicYear,
      tags: tags ? tags.split(",").map(tag => tag.trim()) : [],
      processingStatus: "pending",
      extractedText: "", // Will be filled by processing service
    };

    const syllabus = new Syllabus(syllabusData);
    await syllabus.save();

    // Start background processing
    SyllabusService.processUploadedSyllabus(syllabus._id)
      .catch(error => {
        console.error("Error processing syllabus:", error);
      });

    res.send({
      message: "Syllabus uploaded successfully. Processing will begin shortly.",
      success: true,
      data: {
        syllabusId: syllabus._id,
        fileName: syllabus.fileName,
        processingStatus: syllabus.processingStatus,
      },
    });

  } catch (error) {
    console.error("Error uploading syllabus:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get all syllabuses
router.get("/", enhancedAuthMiddleware, async (req, res) => {
  try {
    console.log('📚 GET /api/syllabus - Request received');
    console.log('User ID:', req.body.userId);
    console.log('Query params:', req.query);

    const userId = req.body.userId;
    const { level, subject, class: className, isActive } = req.query;

    const query = { isActive: true }; // Always default to active syllabuses
    if (level) query.level = level;
    if (subject) query.subject = subject;
    if (className) query.classes = { $in: [className] };

    // Only override if explicitly set to false
    if (isActive === "false") {
      query.isActive = false;
    }

    const syllabuses = await Syllabus.find(query)
      .populate("uploadedBy", "name email")
      .populate("approvedBy", "name email")
      .sort({ createdAt: -1 });

    console.log('📚 Found syllabuses:', syllabuses.length);
    console.log('Query used:', query);

    res.send({
      message: "Syllabuses retrieved successfully",
      success: true,
      data: syllabuses,
    });

  } catch (error) {
    console.error("Error fetching syllabuses:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get syllabus by ID
router.get("/:id", enhancedAuthMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { id } = req.params;

    const syllabus = await Syllabus.findById(id)
      .populate("uploadedBy", "name email")
      .populate("approvedBy", "name email");

    if (!syllabus) {
      return res.status(404).send({
        message: "Syllabus not found",
        success: false,
      });
    }

    res.send({
      message: "Syllabus retrieved successfully",
      success: true,
      data: syllabus,
    });

  } catch (error) {
    console.error("Error fetching syllabus:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Update syllabus
router.put("/:id", enhancedAuthMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { id } = req.params;
    const updates = req.body;

    const syllabus = await Syllabus.findByIdAndUpdate(
      id,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!syllabus) {
      return res.status(404).send({
        message: "Syllabus not found",
        success: false,
      });
    }

    res.send({
      message: "Syllabus updated successfully",
      success: true,
      data: syllabus,
    });

  } catch (error) {
    console.error("Error updating syllabus:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Delete syllabus
router.delete("/:id", enhancedAuthMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { id } = req.params;

    const syllabus = await Syllabus.findById(id);
    if (!syllabus) {
      return res.status(404).send({
        message: "Syllabus not found",
        success: false,
      });
    }

    // Delete file from filesystem
    if (fs.existsSync(syllabus.filePath)) {
      fs.unlinkSync(syllabus.filePath);
    }

    await Syllabus.findByIdAndDelete(id);

    res.send({
      message: "Syllabus deleted successfully",
      success: true,
    });

  } catch (error) {
    console.error("Error deleting syllabus:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get available subjects for a level
router.get("/subjects/:level", enhancedAuthMiddleware, async (req, res) => {
  try {
    console.log('📖 GET /api/syllabus/subjects/:level - Request received');
    console.log('Level:', req.params.level);
    console.log('Class filter:', req.query.class);

    const { level } = req.params;
    const { class: className } = req.query;

    const subjects = await Syllabus.getAvailableSubjects(level, className);
    console.log('📖 Found subjects:', subjects);

    res.send({
      message: "Available subjects retrieved successfully",
      success: true,
      data: subjects,
    });

  } catch (error) {
    console.error("Error fetching subjects:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get syllabus content for AI generation
router.get("/ai-content/:level/:class/:subject", enhancedAuthMiddleware, async (req, res) => {
  try {
    const { level, class: className, subject } = req.params;

    const syllabus = await Syllabus.findForAIGeneration(level, className, subject);

    if (!syllabus) {
      return res.status(404).send({
        message: `No syllabus found for ${level} ${subject} Class ${className}`,
        success: false,
      });
    }

    const aiContent = {
      extractedText: syllabus.extractedText,
      topics: syllabus.getTopicsForAI(),
      learningObjectives: syllabus.learningObjectives,
      competencies: syllabus.competencies,
    };

    // Update usage statistics
    await syllabus.updateUsageStats();

    res.send({
      message: "Syllabus content retrieved for AI generation",
      success: true,
      data: aiContent,
    });

  } catch (error) {
    console.error("Error fetching syllabus for AI:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

module.exports = router;
