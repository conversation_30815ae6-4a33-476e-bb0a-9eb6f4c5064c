# 📚 Syllabus PDF Management System - User Guide

## 🎯 Overview

The Syllabus PDF Management System allows you to upload Tanzania syllabus PDFs that the AI question generation system will use to create more accurate and curriculum-aligned questions.

## 🚀 How to Access

1. **Login as Admin**: Make sure you're logged in with admin privileges
2. **Navigate to Syllabus Management**: 
   - Go to `http://localhost:3000/admin/syllabus`
   - Or use the admin navigation: Admin Panel → Syllabus Management

## 📤 Uploading Syllabus PDFs

### Step 1: Click "Upload Syllabus"
- Click the blue "Upload Syllabus" button in the top-right corner

### Step 2: Fill in Metadata
- **Title**: Descriptive name (e.g., "Mathematics Primary Class 5 Syllabus 2024")
- **Description**: Brief description of the content (optional)
- **Level**: Select from Primary, Secondary, or Advance
- **Class**: Enter the class number (e.g., "5")
- **Subject**: Enter the subject name (e.g., "Mathematics")
- **Academic Year**: Year of the syllabus (optional)
- **Tags**: Comma-separated tags for organization (optional)

### Step 3: Upload PDF File
- Drag and drop your PDF file or click to browse
- Maximum file size: 50MB
- Only PDF files are accepted

### Step 4: Submit
- Click "Upload Syllabus"
- The system will automatically process the PDF and extract content

## 🔄 Processing Workflow

1. **Upload** → PDF file is saved to the server
2. **Text Extraction** → System extracts text from the PDF
3. **AI Analysis** → OpenAI analyzes content to identify:
   - Topics and subtopics
   - Learning objectives
   - Key competencies
   - Assessment criteria
4. **Quality Scoring** → System calculates quality score based on content richness
5. **Ready for Use** → AI question generation can now use this syllabus

## 📊 Managing Syllabuses

### Dashboard Features
- **Statistics Cards**: View total, completed, processing, and failed syllabuses
- **Filters**: Filter by level, status, or subject
- **Search**: Search syllabuses by subject name

### Syllabus Status
- 🟠 **Pending**: Waiting to be processed
- 🔵 **Processing**: Currently extracting and analyzing content
- 🟢 **Completed**: Ready for AI question generation
- 🔴 **Failed**: Processing encountered an error

### Actions Available
- 👁️ **View**: See detailed information about the syllabus
- ✅ **Approve**: Approve processed syllabuses for use
- 📥 **Download**: Download the original PDF file
- 🗑️ **Delete**: Remove syllabus from the system

## 🤖 AI Question Generation Integration

### How It Works
1. When generating AI questions, the system automatically looks for uploaded syllabuses
2. It matches by **Level + Class + Subject** (e.g., Primary + 5 + Mathematics)
3. If a matching syllabus is found, the AI uses the actual curriculum content
4. If no syllabus is found, it falls back to hardcoded syllabus data

### Benefits
- ✅ **More Accurate Questions**: Based on official Tanzania curriculum
- ✅ **Better Alignment**: Questions match actual learning objectives
- ✅ **Current Content**: Use latest syllabus versions
- ✅ **Comprehensive Coverage**: AI has access to full curriculum details

## 📋 Best Practices

### File Organization
- Use clear, descriptive titles
- Include academic year in the title
- Add relevant tags for easy searching
- Keep one syllabus per subject/class combination

### Quality Assurance
- Review extracted content after processing
- Approve only high-quality syllabuses
- Replace outdated syllabuses with newer versions
- Monitor usage statistics to see which syllabuses are most used

### Troubleshooting
- **Processing Failed**: Check if PDF is readable (not scanned image)
- **Low Quality Score**: PDF might have poor text extraction
- **No Content Extracted**: PDF might be image-based or corrupted
- **Access Denied**: Ensure you're logged in as admin

## 🔧 Technical Details

### Supported Formats
- **File Type**: PDF only
- **File Size**: Maximum 50MB
- **Content**: Text-based PDFs (not scanned images)

### Processing Technology
- **Text Extraction**: pdf-parse library
- **AI Analysis**: OpenAI GPT-4
- **Storage**: Local filesystem with database metadata
- **Authentication**: Enhanced auth middleware (same as AI questions)

### Database Schema
- Comprehensive metadata storage
- Version control support
- Usage analytics tracking
- Quality scoring system

## 🎯 Usage Examples

### Example 1: Primary Mathematics
```
Title: Mathematics Primary Class 5 Syllabus 2024
Level: Primary
Class: 5
Subject: Mathematics
Academic Year: 2024
Tags: official, updated, curriculum
```

### Example 2: Secondary Science
```
Title: Physics Secondary Form 3 Syllabus
Level: Secondary  
Class: 3
Subject: Physics
Academic Year: 2024
Tags: science, physics, form3
```

## 📈 Monitoring and Analytics

### Quality Metrics
- **Quality Score**: 0-100 based on content richness
- **Processing Status**: Track success/failure rates
- **Usage Statistics**: See which syllabuses generate most questions

### Performance Indicators
- **High Quality** (80-100): Excellent content extraction
- **Good Quality** (60-79): Adequate for question generation
- **Low Quality** (0-59): May need manual review or replacement

## 🔮 Future Enhancements

- Bulk upload functionality
- OCR support for scanned PDFs
- Automatic syllabus updates
- Advanced search and filtering
- Export/import capabilities
- Integration with Tanzania Institute of Education (TIE)

---

## 🆘 Support

If you encounter any issues:
1. Check the processing status and error messages
2. Ensure PDF files are text-based (not scanned images)
3. Verify admin login credentials
4. Contact system administrator for technical support

**System Status**: ✅ Fully Operational
**Last Updated**: December 2024
