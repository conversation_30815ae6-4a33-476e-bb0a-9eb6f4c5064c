{"ast": null, "code": "import axiosInstance from \"./index\";\n\n// Get user notifications\nexport const getUserNotifications = async (params = {}) => {\n  try {\n    const {\n      page = 1,\n      limit = 20,\n      unreadOnly = false,\n      type = null\n    } = params;\n    const queryParams = new URLSearchParams({\n      page: page.toString(),\n      limit: limit.toString(),\n      unreadOnly: unreadOnly.toString(),\n      ...(type && {\n        type\n      })\n    });\n    const response = await axiosInstance.get(`/api/notifications?${queryParams}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get unread notification count\nexport const getUnreadNotificationCount = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/notifications/unread-count\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Mark notification as read\nexport const markNotificationAsRead = async notificationId => {\n  try {\n    const response = await axiosInstance.put(`/api/notifications/${notificationId}/read`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Mark all notifications as read\nexport const markAllNotificationsAsRead = async () => {\n  try {\n    const response = await axiosInstance.put(\"/api/notifications/mark-all-read\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Send heartbeat (update activity)\nexport const sendHeartbeat = async () => {\n  try {\n    const response = await axiosInstance.post(\"/api/notifications/heartbeat\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Set user online\nexport const setUserOnline = async () => {\n  try {\n    const response = await axiosInstance.post(\"/api/notifications/online\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Set user offline\nexport const setUserOffline = async () => {\n  try {\n    const response = await axiosInstance.post(\"/api/notifications/offline\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get online users\nexport const getOnlineUsers = async (limit = 50) => {\n  try {\n    const response = await axiosInstance.get(`/api/notifications/online-users?limit=${limit}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get online count\nexport const getOnlineCount = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/notifications/online-count\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Check user online status\nexport const getUserOnlineStatus = async userId => {\n  try {\n    const response = await axiosInstance.get(`/api/notifications/status/${userId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Admin notification functions\nexport const sendAdminNotification = async notificationData => {\n  try {\n    const response = await axiosInstance.post(\"/api/notifications/admin/send\", notificationData);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const getAdminNotifications = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/notifications/admin/sent\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const deleteAdminNotification = async notificationId => {\n  try {\n    const response = await axiosInstance.delete(`/api/notifications/admin/${notificationId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};", "map": {"version": 3, "names": ["axiosInstance", "getUserNotifications", "params", "page", "limit", "unreadOnly", "type", "queryParams", "URLSearchParams", "toString", "response", "get", "data", "error", "getUnreadNotificationCount", "markNotificationAsRead", "notificationId", "put", "markAllNotificationsAsRead", "sendHeartbeat", "post", "setUserOnline", "setUserOffline", "getOnlineUsers", "getOnlineCount", "getUserOnlineStatus", "userId", "sendAdminNotification", "notificationData", "getAdminNotifications", "deleteAdminNotification", "delete"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/notifications.js"], "sourcesContent": ["import axiosInstance from \"./index\";\n\n// Get user notifications\nexport const getUserNotifications = async (params = {}) => {\n  try {\n    const { page = 1, limit = 20, unreadOnly = false, type = null } = params;\n    const queryParams = new URLSearchParams({\n      page: page.toString(),\n      limit: limit.toString(),\n      unreadOnly: unreadOnly.toString(),\n      ...(type && { type })\n    });\n    \n    const response = await axiosInstance.get(`/api/notifications?${queryParams}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get unread notification count\nexport const getUnreadNotificationCount = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/notifications/unread-count\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Mark notification as read\nexport const markNotificationAsRead = async (notificationId) => {\n  try {\n    const response = await axiosInstance.put(`/api/notifications/${notificationId}/read`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Mark all notifications as read\nexport const markAllNotificationsAsRead = async () => {\n  try {\n    const response = await axiosInstance.put(\"/api/notifications/mark-all-read\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Send heartbeat (update activity)\nexport const sendHeartbeat = async () => {\n  try {\n    const response = await axiosInstance.post(\"/api/notifications/heartbeat\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Set user online\nexport const setUserOnline = async () => {\n  try {\n    const response = await axiosInstance.post(\"/api/notifications/online\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Set user offline\nexport const setUserOffline = async () => {\n  try {\n    const response = await axiosInstance.post(\"/api/notifications/offline\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get online users\nexport const getOnlineUsers = async (limit = 50) => {\n  try {\n    const response = await axiosInstance.get(`/api/notifications/online-users?limit=${limit}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get online count\nexport const getOnlineCount = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/notifications/online-count\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Check user online status\nexport const getUserOnlineStatus = async (userId) => {\n  try {\n    const response = await axiosInstance.get(`/api/notifications/status/${userId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Admin notification functions\nexport const sendAdminNotification = async (notificationData) => {\n  try {\n    const response = await axiosInstance.post(\"/api/notifications/admin/send\", notificationData);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\nexport const getAdminNotifications = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/notifications/admin/sent\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\nexport const deleteAdminNotification = async (notificationId) => {\n  try {\n    const response = await axiosInstance.delete(`/api/notifications/admin/${notificationId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,SAAS;;AAEnC;AACA,OAAO,MAAMC,oBAAoB,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;EACzD,IAAI;IACF,MAAM;MAAEC,IAAI,GAAG,CAAC;MAAEC,KAAK,GAAG,EAAE;MAAEC,UAAU,GAAG,KAAK;MAAEC,IAAI,GAAG;IAAK,CAAC,GAAGJ,MAAM;IACxE,MAAMK,WAAW,GAAG,IAAIC,eAAe,CAAC;MACtCL,IAAI,EAAEA,IAAI,CAACM,QAAQ,CAAC,CAAC;MACrBL,KAAK,EAAEA,KAAK,CAACK,QAAQ,CAAC,CAAC;MACvBJ,UAAU,EAAEA,UAAU,CAACI,QAAQ,CAAC,CAAC;MACjC,IAAIH,IAAI,IAAI;QAAEA;MAAK,CAAC;IACtB,CAAC,CAAC;IAEF,MAAMI,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAE,sBAAqBJ,WAAY,EAAC,CAAC;IAC7E,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAME,0BAA0B,GAAG,MAAAA,CAAA,KAAY;EACpD,IAAI;IACF,MAAMJ,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,iCAAiC,CAAC;IAC3E,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,sBAAsB,GAAG,MAAOC,cAAc,IAAK;EAC9D,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMV,aAAa,CAACiB,GAAG,CAAE,sBAAqBD,cAAe,OAAM,CAAC;IACrF,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,0BAA0B,GAAG,MAAAA,CAAA,KAAY;EACpD,IAAI;IACF,MAAMR,QAAQ,GAAG,MAAMV,aAAa,CAACiB,GAAG,CAAC,kCAAkC,CAAC;IAC5E,OAAOP,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACF,MAAMT,QAAQ,GAAG,MAAMV,aAAa,CAACoB,IAAI,CAAC,8BAA8B,CAAC;IACzE,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMS,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMV,aAAa,CAACoB,IAAI,CAAC,2BAA2B,CAAC;IACtE,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMZ,QAAQ,GAAG,MAAMV,aAAa,CAACoB,IAAI,CAAC,4BAA4B,CAAC;IACvE,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMW,cAAc,GAAG,MAAAA,CAAOnB,KAAK,GAAG,EAAE,KAAK;EAClD,IAAI;IACF,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAE,yCAAwCP,KAAM,EAAC,CAAC;IAC1F,OAAOM,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMd,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,iCAAiC,CAAC;IAC3E,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMa,mBAAmB,GAAG,MAAOC,MAAM,IAAK;EACnD,IAAI;IACF,MAAMhB,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAE,6BAA4Be,MAAO,EAAC,CAAC;IAC/E,OAAOhB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMe,qBAAqB,GAAG,MAAOC,gBAAgB,IAAK;EAC/D,IAAI;IACF,MAAMlB,QAAQ,GAAG,MAAMV,aAAa,CAACoB,IAAI,CAAC,+BAA+B,EAAEQ,gBAAgB,CAAC;IAC5F,OAAOlB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;AAED,OAAO,MAAMiB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EAC/C,IAAI;IACF,MAAMnB,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,+BAA+B,CAAC;IACzE,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;AAED,OAAO,MAAMkB,uBAAuB,GAAG,MAAOd,cAAc,IAAK;EAC/D,IAAI;IACF,MAAMN,QAAQ,GAAG,MAAMV,aAAa,CAAC+B,MAAM,CAAE,4BAA2Bf,cAAe,EAAC,CAAC;IACzF,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}