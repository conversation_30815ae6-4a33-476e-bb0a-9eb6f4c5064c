{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\SyllabusManagement\\\\SyllabusManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Modal, Form, Input, Select, Upload, message, Tag, Space, Tooltip, Progress, Popconfirm, Row, Col, Statistic } from 'antd';\nimport { TbUpload, TbEye, TbEdit, TbTrash, TbDownload, TbFileText, TbBook, TbUsers, TbClock, TbCheck, TbX, TbRefresh, TbPlus } from 'react-icons/tb';\nimport { useDispatch } from 'react-redux';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { getAllSyllabuses, uploadSyllabus, deleteSyllabus, updateSyllabus, getSyllabusById, approveSyllabus } from '../../../apicalls/syllabus';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst SyllabusManagement = () => {\n  _s();\n  const [syllabuses, setSyllabuses] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [uploadModalVisible, setUploadModalVisible] = useState(false);\n  const [viewModalVisible, setViewModalVisible] = useState(false);\n  const [selectedSyllabus, setSelectedSyllabus] = useState(null);\n  const [filters, setFilters] = useState({});\n  const [stats, setStats] = useState({\n    total: 0,\n    pending: 0,\n    completed: 0,\n    failed: 0\n  });\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  useEffect(() => {\n    fetchSyllabuses();\n  }, [filters]);\n  const fetchSyllabuses = async () => {\n    try {\n      setLoading(true);\n      const response = await getAllSyllabuses(filters);\n      if (response.success) {\n        setSyllabuses(response.data);\n        calculateStats(response.data);\n      } else {\n        message.error('Failed to fetch syllabuses');\n      }\n    } catch (error) {\n      message.error('Error fetching syllabuses');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const calculateStats = data => {\n    const stats = {\n      total: data.length,\n      pending: data.filter(s => s.processingStatus === 'pending').length,\n      completed: data.filter(s => s.processingStatus === 'completed').length,\n      failed: data.filter(s => s.processingStatus === 'failed').length\n    };\n    setStats(stats);\n  };\n  const handleUpload = async values => {\n    try {\n      dispatch(ShowLoading());\n      const formData = new FormData();\n      formData.append('syllabusFile', values.file.file);\n      formData.append('title', values.title);\n      formData.append('description', values.description || '');\n      formData.append('level', values.level);\n      formData.append('class', values.class);\n      formData.append('subject', values.subject);\n      formData.append('academicYear', values.academicYear || '');\n      formData.append('tags', values.tags || '');\n      const response = await uploadSyllabus(formData);\n      if (response.success) {\n        message.success('Syllabus uploaded successfully! Processing will begin shortly.');\n        setUploadModalVisible(false);\n        form.resetFields();\n        fetchSyllabuses();\n      } else {\n        message.error(response.message || 'Failed to upload syllabus');\n      }\n    } catch (error) {\n      message.error('Error uploading syllabus');\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleDelete = async id => {\n    try {\n      dispatch(ShowLoading());\n      const response = await deleteSyllabus(id);\n      if (response.success) {\n        message.success('Syllabus deleted successfully');\n        fetchSyllabuses();\n      } else {\n        message.error(response.message || 'Failed to delete syllabus');\n      }\n    } catch (error) {\n      message.error('Error deleting syllabus');\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleView = async id => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getSyllabusById(id);\n      if (response.success) {\n        setSelectedSyllabus(response.data);\n        setViewModalVisible(true);\n      } else {\n        message.error('Failed to fetch syllabus details');\n      }\n    } catch (error) {\n      message.error('Error fetching syllabus details');\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleApprove = async id => {\n    try {\n      dispatch(ShowLoading());\n      const response = await approveSyllabus(id, {\n        isActive: true\n      });\n      if (response.success) {\n        message.success('Syllabus approved successfully');\n        fetchSyllabuses();\n      } else {\n        message.error('Failed to approve syllabus');\n      }\n    } catch (error) {\n      message.error('Error approving syllabus');\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'green';\n      case 'processing':\n        return 'blue';\n      case 'pending':\n        return 'orange';\n      case 'failed':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(TbCheck, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 32\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(TbClock, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 33\n        }, this);\n      case 'pending':\n        return /*#__PURE__*/_jsxDEV(TbClock, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 30\n        }, this);\n      case 'failed':\n        return /*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 29\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(TbClock, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  const columns = [{\n    title: 'Title',\n    dataIndex: 'title',\n    key: 'title',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium\",\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500\",\n        children: [record.level, \" \\u2022 Class \", record.class, \" \\u2022 \", record.subject]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Status',\n    dataIndex: 'processingStatus',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      icon: getStatusIcon(status),\n      children: status.toUpperCase()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Quality Score',\n    dataIndex: 'qualityScore',\n    key: 'qualityScore',\n    render: score => score ? /*#__PURE__*/_jsxDEV(Progress, {\n      percent: score,\n      size: \"small\",\n      status: score >= 80 ? 'success' : score >= 60 ? 'normal' : 'exception'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 11\n    }, this) : '-'\n  }, {\n    title: 'Uploaded',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    render: date => new Date(date).toLocaleDateString()\n  }, {\n    title: 'Actions',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"View Details\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(TbEye, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleView(record._id)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this), record.processingStatus === 'completed' && !record.approvedBy && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Approve\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(TbCheck, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 23\n          }, this),\n          onClick: () => handleApprove(record._id),\n          style: {\n            color: 'green'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Download\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 21\n          }, this),\n          onClick: () => window.open(`/api/syllabus/${record._id}/download`)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"Are you sure you want to delete this syllabus?\",\n        onConfirm: () => handleDelete(record._id),\n        okText: \"Yes\",\n        cancelText: \"No\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Delete\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: /*#__PURE__*/_jsxDEV(TbTrash, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 23\n            }, this),\n            danger: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"syllabus-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(TbBook, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), \"Syllabus Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-description\",\n          children: \"Upload and manage syllabus PDFs for AI question generation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(TbPlus, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 17\n        }, this),\n        onClick: () => setUploadModalVisible(true),\n        size: \"large\",\n        children: \"Upload Syllabus\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      className: \"stats-row\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Total Syllabuses\",\n            value: stats.total,\n            prefix: /*#__PURE__*/_jsxDEV(TbFileText, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Completed\",\n            value: stats.completed,\n            prefix: /*#__PURE__*/_jsxDEV(TbCheck, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Processing\",\n            value: stats.pending,\n            prefix: /*#__PURE__*/_jsxDEV(TbClock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Failed\",\n            value: stats.failed,\n            prefix: /*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"filters-card\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"Filter by Level\",\n            allowClear: true,\n            onChange: value => setFilters({\n              ...filters,\n              level: value\n            }),\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"primary\",\n              children: \"Primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"secondary\",\n              children: \"Secondary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"advance\",\n              children: \"Advance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"Filter by Status\",\n            allowClear: true,\n            onChange: value => setFilters({\n              ...filters,\n              processingStatus: value\n            }),\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"pending\",\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"processing\",\n              children: \"Processing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"completed\",\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"failed\",\n              children: \"Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"Search by subject\",\n            allowClear: true,\n            onChange: e => setFilters({\n              ...filters,\n              subject: e.target.value\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(TbRefresh, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 21\n            }, this),\n            onClick: fetchSyllabuses,\n            children: \"Refresh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: syllabuses,\n        rowKey: \"_id\",\n        loading: loading,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} syllabuses`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Upload New Syllabus\",\n      open: uploadModalVisible,\n      onCancel: () => {\n        setUploadModalVisible(false);\n        form.resetFields();\n      },\n      footer: null,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleUpload,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"title\",\n          label: \"Title\",\n          rules: [{\n            required: true,\n            message: 'Please enter syllabus title'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"e.g., Mathematics Primary Class 5 Syllabus 2024\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"Description\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"Brief description of the syllabus content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"level\",\n              label: \"Level\",\n              rules: [{\n                required: true,\n                message: 'Please select level'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"Select level\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"primary\",\n                  children: \"Primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"secondary\",\n                  children: \"Secondary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"advance\",\n                  children: \"Advance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"class\",\n              label: \"Class\",\n              rules: [{\n                required: true,\n                message: 'Please enter class'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"e.g., 5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"subject\",\n              label: \"Subject\",\n              rules: [{\n                required: true,\n                message: 'Please enter subject'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"e.g., Mathematics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"academicYear\",\n              label: \"Academic Year\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"e.g., 2024\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"tags\",\n              label: \"Tags (comma-separated)\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"e.g., official, updated, curriculum\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"file\",\n          label: \"Syllabus PDF File\",\n          rules: [{\n            required: true,\n            message: 'Please upload a PDF file'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Upload.Dragger, {\n            accept: \".pdf\",\n            maxCount: 1,\n            beforeUpload: () => false,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-drag-icon\",\n              children: /*#__PURE__*/_jsxDEV(TbUpload, {\n                style: {\n                  fontSize: '48px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-text\",\n              children: \"Click or drag PDF file to this area to upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"ant-upload-hint\",\n              children: \"Support for single PDF file upload. Maximum file size: 50MB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: \"Upload Syllabus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => {\n                setUploadModalVisible(false);\n                form.resetFields();\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Syllabus Details\",\n      open: viewModalVisible,\n      onCancel: () => setViewModalVisible(false),\n      footer: null,\n      width: 800,\n      children: selectedSyllabus && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"syllabus-details\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Title:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 20\n              }, this), \" \", selectedSyllabus.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Level:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 20\n              }, this), \" \", selectedSyllabus.level]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Class:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 20\n              }, this), \" \", selectedSyllabus.class]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Subject:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 20\n              }, this), \" \", selectedSyllabus.subject]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: getStatusColor(selectedSyllabus.processingStatus),\n                children: selectedSyllabus.processingStatus\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Quality Score:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 20\n              }, this), \" \", selectedSyllabus.qualityScore || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"File Size:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 20\n              }, this), \" \", (selectedSyllabus.fileSize / 1024 / 1024).toFixed(2), \" MB\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Uploaded:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 20\n              }, this), \" \", new Date(selectedSyllabus.createdAt).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this), selectedSyllabus.description && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Description:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: selectedSyllabus.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 15\n        }, this), selectedSyllabus.extractedTopics && selectedSyllabus.extractedTopics.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Extracted Topics:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"topics-list\",\n            children: [selectedSyllabus.extractedTopics.slice(0, 10).map((topic, index) => /*#__PURE__*/_jsxDEV(Tag, {\n              className: \"topic-tag\",\n              children: topic.topicName\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 21\n            }, this)), selectedSyllabus.extractedTopics.length > 10 && /*#__PURE__*/_jsxDEV(Tag, {\n              children: [\"+\", selectedSyllabus.extractedTopics.length - 10, \" more\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 15\n        }, this), selectedSyllabus.processingError && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Processing Error:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: 'red'\n            },\n            children: selectedSyllabus.processingError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 302,\n    columnNumber: 5\n  }, this);\n};\n_s(SyllabusManagement, \"Gq63SGvCXoZJw/TQxqcT9adMvDg=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = SyllabusManagement;\nexport default SyllabusManagement;\nvar _c;\n$RefreshReg$(_c, \"SyllabusManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Upload", "message", "Tag", "Space", "<PERSON><PERSON><PERSON>", "Progress", "Popconfirm", "Row", "Col", "Statistic", "TbUpload", "TbEye", "TbEdit", "TbTrash", "TbDownload", "TbFileText", "TbBook", "TbUsers", "TbClock", "TbCheck", "TbX", "TbRefresh", "TbPlus", "useDispatch", "HideLoading", "ShowLoading", "getAllSyllabuses", "uploadSyllabus", "deleteSyllabus", "updateSyllabus", "getSyllabusById", "approveSyllabus", "jsxDEV", "_jsxDEV", "Option", "TextArea", "SyllabusManagement", "_s", "syllabuses", "setSyllabuses", "loading", "setLoading", "uploadModalVisible", "setUploadModalVisible", "viewModalVisible", "setViewModalVisible", "selectedSyllabus", "setSelectedSyllabus", "filters", "setFilters", "stats", "setStats", "total", "pending", "completed", "failed", "form", "useForm", "dispatch", "fetchSyllabuses", "response", "success", "data", "calculateStats", "error", "length", "filter", "s", "processingStatus", "handleUpload", "values", "formData", "FormData", "append", "file", "title", "description", "level", "class", "subject", "academicYear", "tags", "resetFields", "handleDelete", "id", "handleView", "handleApprove", "isActive", "getStatusColor", "status", "getStatusIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "columns", "dataIndex", "key", "render", "text", "record", "children", "className", "color", "icon", "toUpperCase", "score", "percent", "size", "date", "Date", "toLocaleDateString", "_", "type", "onClick", "_id", "approvedBy", "style", "window", "open", "onConfirm", "okText", "cancelText", "danger", "gutter", "span", "value", "prefix", "valueStyle", "placeholder", "allowClear", "onChange", "width", "e", "target", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onCancel", "footer", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "rules", "required", "rows", "<PERSON><PERSON>", "accept", "maxCount", "beforeUpload", "fontSize", "htmlType", "qualityScore", "fileSize", "toFixed", "createdAt", "toLocaleString", "extractedTopics", "slice", "map", "topic", "index", "topicName", "processingError", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/SyllabusManagement/SyllabusManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Table, \n  Button, \n  Modal, \n  Form, \n  Input, \n  Select, \n  Upload, \n  message, \n  Tag, \n  Space, \n  Tooltip,\n  Progress,\n  Popconfirm,\n  Row,\n  Col,\n  Statistic\n} from 'antd';\nimport { \n  TbUpload, \n  TbEye, \n  TbEdit, \n  TbTrash, \n  TbDownload,\n  TbFileText,\n  TbBook,\n  TbUsers,\n  TbClock,\n  TbCheck,\n  TbX,\n  TbRefresh,\n  TbPlus\n} from 'react-icons/tb';\nimport { useDispatch } from 'react-redux';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { \n  getAllSyllabuses, \n  uploadSyllabus, \n  deleteSyllabus,\n  updateSyllabus,\n  getSyllabusById,\n  approveSyllabus\n} from '../../../apicalls/syllabus';\n\n\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst SyllabusManagement = () => {\n  const [syllabuses, setSyllabuses] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [uploadModalVisible, setUploadModalVisible] = useState(false);\n  const [viewModalVisible, setViewModalVisible] = useState(false);\n  const [selectedSyllabus, setSelectedSyllabus] = useState(null);\n  const [filters, setFilters] = useState({});\n  const [stats, setStats] = useState({\n    total: 0,\n    pending: 0,\n    completed: 0,\n    failed: 0\n  });\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    fetchSyllabuses();\n  }, [filters]);\n\n  const fetchSyllabuses = async () => {\n    try {\n      setLoading(true);\n      const response = await getAllSyllabuses(filters);\n      if (response.success) {\n        setSyllabuses(response.data);\n        calculateStats(response.data);\n      } else {\n        message.error('Failed to fetch syllabuses');\n      }\n    } catch (error) {\n      message.error('Error fetching syllabuses');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateStats = (data) => {\n    const stats = {\n      total: data.length,\n      pending: data.filter(s => s.processingStatus === 'pending').length,\n      completed: data.filter(s => s.processingStatus === 'completed').length,\n      failed: data.filter(s => s.processingStatus === 'failed').length,\n    };\n    setStats(stats);\n  };\n\n  const handleUpload = async (values) => {\n    try {\n      dispatch(ShowLoading());\n      \n      const formData = new FormData();\n      formData.append('syllabusFile', values.file.file);\n      formData.append('title', values.title);\n      formData.append('description', values.description || '');\n      formData.append('level', values.level);\n      formData.append('class', values.class);\n      formData.append('subject', values.subject);\n      formData.append('academicYear', values.academicYear || '');\n      formData.append('tags', values.tags || '');\n\n      const response = await uploadSyllabus(formData);\n      \n      if (response.success) {\n        message.success('Syllabus uploaded successfully! Processing will begin shortly.');\n        setUploadModalVisible(false);\n        form.resetFields();\n        fetchSyllabuses();\n      } else {\n        message.error(response.message || 'Failed to upload syllabus');\n      }\n    } catch (error) {\n      message.error('Error uploading syllabus');\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await deleteSyllabus(id);\n      \n      if (response.success) {\n        message.success('Syllabus deleted successfully');\n        fetchSyllabuses();\n      } else {\n        message.error(response.message || 'Failed to delete syllabus');\n      }\n    } catch (error) {\n      message.error('Error deleting syllabus');\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const handleView = async (id) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getSyllabusById(id);\n      \n      if (response.success) {\n        setSelectedSyllabus(response.data);\n        setViewModalVisible(true);\n      } else {\n        message.error('Failed to fetch syllabus details');\n      }\n    } catch (error) {\n      message.error('Error fetching syllabus details');\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const handleApprove = async (id) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await approveSyllabus(id, { isActive: true });\n      \n      if (response.success) {\n        message.success('Syllabus approved successfully');\n        fetchSyllabuses();\n      } else {\n        message.error('Failed to approve syllabus');\n      }\n    } catch (error) {\n      message.error('Error approving syllabus');\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed': return 'green';\n      case 'processing': return 'blue';\n      case 'pending': return 'orange';\n      case 'failed': return 'red';\n      default: return 'default';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'completed': return <TbCheck />;\n      case 'processing': return <TbClock />;\n      case 'pending': return <TbClock />;\n      case 'failed': return <TbX />;\n      default: return <TbClock />;\n    }\n  };\n\n  const columns = [\n    {\n      title: 'Title',\n      dataIndex: 'title',\n      key: 'title',\n      render: (text, record) => (\n        <div>\n          <div className=\"font-medium\">{text}</div>\n          <div className=\"text-sm text-gray-500\">\n            {record.level} • Class {record.class} • {record.subject}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: 'Status',\n      dataIndex: 'processingStatus',\n      key: 'status',\n      render: (status) => (\n        <Tag \n          color={getStatusColor(status)} \n          icon={getStatusIcon(status)}\n        >\n          {status.toUpperCase()}\n        </Tag>\n      ),\n    },\n    {\n      title: 'Quality Score',\n      dataIndex: 'qualityScore',\n      key: 'qualityScore',\n      render: (score) => (\n        score ? (\n          <Progress \n            percent={score} \n            size=\"small\" \n            status={score >= 80 ? 'success' : score >= 60 ? 'normal' : 'exception'}\n          />\n        ) : '-'\n      ),\n    },\n    {\n      title: 'Uploaded',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      render: (date) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"View Details\">\n            <Button \n              type=\"text\" \n              icon={<TbEye />} \n              onClick={() => handleView(record._id)}\n            />\n          </Tooltip>\n          \n          {record.processingStatus === 'completed' && !record.approvedBy && (\n            <Tooltip title=\"Approve\">\n              <Button \n                type=\"text\" \n                icon={<TbCheck />} \n                onClick={() => handleApprove(record._id)}\n                style={{ color: 'green' }}\n              />\n            </Tooltip>\n          )}\n          \n          <Tooltip title=\"Download\">\n            <Button \n              type=\"text\" \n              icon={<TbDownload />} \n              onClick={() => window.open(`/api/syllabus/${record._id}/download`)}\n            />\n          </Tooltip>\n          \n          <Popconfirm\n            title=\"Are you sure you want to delete this syllabus?\"\n            onConfirm={() => handleDelete(record._id)}\n            okText=\"Yes\"\n            cancelText=\"No\"\n          >\n            <Tooltip title=\"Delete\">\n              <Button \n                type=\"text\" \n                icon={<TbTrash />} \n                danger\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"syllabus-management\">\n      <div className=\"page-header\">\n        <div>\n          <h1 className=\"page-title\">\n            <TbBook className=\"title-icon\" />\n            Syllabus Management\n          </h1>\n          <p className=\"page-description\">\n            Upload and manage syllabus PDFs for AI question generation\n          </p>\n        </div>\n        <Button \n          type=\"primary\" \n          icon={<TbPlus />}\n          onClick={() => setUploadModalVisible(true)}\n          size=\"large\"\n        >\n          Upload Syllabus\n        </Button>\n      </div>\n\n      {/* Statistics Cards */}\n      <Row gutter={16} className=\"stats-row\">\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"Total Syllabuses\"\n              value={stats.total}\n              prefix={<TbFileText />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"Completed\"\n              value={stats.completed}\n              prefix={<TbCheck />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"Processing\"\n              value={stats.pending}\n              prefix={<TbClock />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"Failed\"\n              value={stats.failed}\n              prefix={<TbX />}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Filters */}\n      <Card className=\"filters-card\">\n        <Row gutter={16}>\n          <Col span={6}>\n            <Select\n              placeholder=\"Filter by Level\"\n              allowClear\n              onChange={(value) => setFilters({ ...filters, level: value })}\n              style={{ width: '100%' }}\n            >\n              <Option value=\"primary\">Primary</Option>\n              <Option value=\"secondary\">Secondary</Option>\n              <Option value=\"advance\">Advance</Option>\n            </Select>\n          </Col>\n          <Col span={6}>\n            <Select\n              placeholder=\"Filter by Status\"\n              allowClear\n              onChange={(value) => setFilters({ ...filters, processingStatus: value })}\n              style={{ width: '100%' }}\n            >\n              <Option value=\"pending\">Pending</Option>\n              <Option value=\"processing\">Processing</Option>\n              <Option value=\"completed\">Completed</Option>\n              <Option value=\"failed\">Failed</Option>\n            </Select>\n          </Col>\n          <Col span={6}>\n            <Input\n              placeholder=\"Search by subject\"\n              allowClear\n              onChange={(e) => setFilters({ ...filters, subject: e.target.value })}\n            />\n          </Col>\n          <Col span={6}>\n            <Button \n              icon={<TbRefresh />}\n              onClick={fetchSyllabuses}\n            >\n              Refresh\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* Syllabuses Table */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={syllabuses}\n          rowKey=\"_id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => \n              `${range[0]}-${range[1]} of ${total} syllabuses`,\n          }}\n        />\n      </Card>\n\n      {/* Upload Modal */}\n      <Modal\n        title=\"Upload New Syllabus\"\n        open={uploadModalVisible}\n        onCancel={() => {\n          setUploadModalVisible(false);\n          form.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpload}\n        >\n          <Form.Item\n            name=\"title\"\n            label=\"Title\"\n            rules={[{ required: true, message: 'Please enter syllabus title' }]}\n          >\n            <Input placeholder=\"e.g., Mathematics Primary Class 5 Syllabus 2024\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"Description\"\n          >\n            <TextArea \n              rows={3} \n              placeholder=\"Brief description of the syllabus content\"\n            />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"level\"\n                label=\"Level\"\n                rules={[{ required: true, message: 'Please select level' }]}\n              >\n                <Select placeholder=\"Select level\">\n                  <Option value=\"primary\">Primary</Option>\n                  <Option value=\"secondary\">Secondary</Option>\n                  <Option value=\"advance\">Advance</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"class\"\n                label=\"Class\"\n                rules={[{ required: true, message: 'Please enter class' }]}\n              >\n                <Input placeholder=\"e.g., 5\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"subject\"\n                label=\"Subject\"\n                rules={[{ required: true, message: 'Please enter subject' }]}\n              >\n                <Input placeholder=\"e.g., Mathematics\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"academicYear\"\n                label=\"Academic Year\"\n              >\n                <Input placeholder=\"e.g., 2024\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"tags\"\n                label=\"Tags (comma-separated)\"\n              >\n                <Input placeholder=\"e.g., official, updated, curriculum\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"file\"\n            label=\"Syllabus PDF File\"\n            rules={[{ required: true, message: 'Please upload a PDF file' }]}\n          >\n            <Upload.Dragger\n              accept=\".pdf\"\n              maxCount={1}\n              beforeUpload={() => false}\n            >\n              <p className=\"ant-upload-drag-icon\">\n                <TbUpload style={{ fontSize: '48px' }} />\n              </p>\n              <p className=\"ant-upload-text\">\n                Click or drag PDF file to this area to upload\n              </p>\n              <p className=\"ant-upload-hint\">\n                Support for single PDF file upload. Maximum file size: 50MB\n              </p>\n            </Upload.Dragger>\n          </Form.Item>\n\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\">\n                Upload Syllabus\n              </Button>\n              <Button onClick={() => {\n                setUploadModalVisible(false);\n                form.resetFields();\n              }}>\n                Cancel\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* View Modal */}\n      <Modal\n        title=\"Syllabus Details\"\n        open={viewModalVisible}\n        onCancel={() => setViewModalVisible(false)}\n        footer={null}\n        width={800}\n      >\n        {selectedSyllabus && (\n          <div className=\"syllabus-details\">\n            <Row gutter={16}>\n              <Col span={12}>\n                <p><strong>Title:</strong> {selectedSyllabus.title}</p>\n                <p><strong>Level:</strong> {selectedSyllabus.level}</p>\n                <p><strong>Class:</strong> {selectedSyllabus.class}</p>\n                <p><strong>Subject:</strong> {selectedSyllabus.subject}</p>\n              </Col>\n              <Col span={12}>\n                <p><strong>Status:</strong> \n                  <Tag color={getStatusColor(selectedSyllabus.processingStatus)}>\n                    {selectedSyllabus.processingStatus}\n                  </Tag>\n                </p>\n                <p><strong>Quality Score:</strong> {selectedSyllabus.qualityScore || 'N/A'}</p>\n                <p><strong>File Size:</strong> {(selectedSyllabus.fileSize / 1024 / 1024).toFixed(2)} MB</p>\n                <p><strong>Uploaded:</strong> {new Date(selectedSyllabus.createdAt).toLocaleString()}</p>\n              </Col>\n            </Row>\n            \n            {selectedSyllabus.description && (\n              <div>\n                <h4>Description:</h4>\n                <p>{selectedSyllabus.description}</p>\n              </div>\n            )}\n            \n            {selectedSyllabus.extractedTopics && selectedSyllabus.extractedTopics.length > 0 && (\n              <div>\n                <h4>Extracted Topics:</h4>\n                <div className=\"topics-list\">\n                  {selectedSyllabus.extractedTopics.slice(0, 10).map((topic, index) => (\n                    <Tag key={index} className=\"topic-tag\">\n                      {topic.topicName}\n                    </Tag>\n                  ))}\n                  {selectedSyllabus.extractedTopics.length > 10 && (\n                    <Tag>+{selectedSyllabus.extractedTopics.length - 10} more</Tag>\n                  )}\n                </div>\n              </div>\n            )}\n            \n            {selectedSyllabus.processingError && (\n              <div>\n                <h4>Processing Error:</h4>\n                <p style={{ color: 'red' }}>{selectedSyllabus.processingError}</p>\n              </div>\n            )}\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default SyllabusManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,KAAK,EACLC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,QACJ,MAAM;AACb,SACEC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,GAAG,EACHC,SAAS,EACTC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SACEC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,eAAe,QACV,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGpC,MAAM;EAAEC;AAAO,CAAC,GAAGnC,MAAM;AACzB,MAAM;EAAEoC;AAAS,CAAC,GAAGrC,KAAK;AAE1B,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAC;IACjC6D,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,CAAC,GAAG3D,IAAI,CAAC4D,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAE9B/B,SAAS,CAAC,MAAM;IACdmE,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACX,OAAO,CAAC,CAAC;EAEb,MAAMW,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmB,QAAQ,GAAG,MAAMlC,gBAAgB,CAACsB,OAAO,CAAC;MAChD,IAAIY,QAAQ,CAACC,OAAO,EAAE;QACpBtB,aAAa,CAACqB,QAAQ,CAACE,IAAI,CAAC;QAC5BC,cAAc,CAACH,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,MAAM;QACL7D,OAAO,CAAC+D,KAAK,CAAC,4BAA4B,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/D,OAAO,CAAC+D,KAAK,CAAC,2BAA2B,CAAC;IAC5C,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,cAAc,GAAID,IAAI,IAAK;IAC/B,MAAMZ,KAAK,GAAG;MACZE,KAAK,EAAEU,IAAI,CAACG,MAAM;MAClBZ,OAAO,EAAES,IAAI,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,KAAK,SAAS,CAAC,CAACH,MAAM;MAClEX,SAAS,EAAEQ,IAAI,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,KAAK,WAAW,CAAC,CAACH,MAAM;MACtEV,MAAM,EAAEO,IAAI,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,KAAK,QAAQ,CAAC,CAACH;IAC5D,CAAC;IACDd,QAAQ,CAACD,KAAK,CAAC;EACjB,CAAC;EAED,MAAMmB,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MACFZ,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM8C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEH,MAAM,CAACI,IAAI,CAACA,IAAI,CAAC;MACjDH,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,MAAM,CAACK,KAAK,CAAC;MACtCJ,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEH,MAAM,CAACM,WAAW,IAAI,EAAE,CAAC;MACxDL,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,MAAM,CAACO,KAAK,CAAC;MACtCN,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,MAAM,CAACQ,KAAK,CAAC;MACtCP,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,MAAM,CAACS,OAAO,CAAC;MAC1CR,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEH,MAAM,CAACU,YAAY,IAAI,EAAE,CAAC;MAC1DT,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,MAAM,CAACW,IAAI,IAAI,EAAE,CAAC;MAE1C,MAAMrB,QAAQ,GAAG,MAAMjC,cAAc,CAAC4C,QAAQ,CAAC;MAE/C,IAAIX,QAAQ,CAACC,OAAO,EAAE;QACpB5D,OAAO,CAAC4D,OAAO,CAAC,gEAAgE,CAAC;QACjFlB,qBAAqB,CAAC,KAAK,CAAC;QAC5Ba,IAAI,CAAC0B,WAAW,CAAC,CAAC;QAClBvB,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACL1D,OAAO,CAAC+D,KAAK,CAACJ,QAAQ,CAAC3D,OAAO,IAAI,2BAA2B,CAAC;MAChE;IACF,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACd/D,OAAO,CAAC+D,KAAK,CAAC,0BAA0B,CAAC;IAC3C,CAAC,SAAS;MACRN,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM2D,YAAY,GAAG,MAAOC,EAAE,IAAK;IACjC,IAAI;MACF1B,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMmC,QAAQ,GAAG,MAAMhC,cAAc,CAACwD,EAAE,CAAC;MAEzC,IAAIxB,QAAQ,CAACC,OAAO,EAAE;QACpB5D,OAAO,CAAC4D,OAAO,CAAC,+BAA+B,CAAC;QAChDF,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACL1D,OAAO,CAAC+D,KAAK,CAACJ,QAAQ,CAAC3D,OAAO,IAAI,2BAA2B,CAAC;MAChE;IACF,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACd/D,OAAO,CAAC+D,KAAK,CAAC,yBAAyB,CAAC;IAC1C,CAAC,SAAS;MACRN,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM6D,UAAU,GAAG,MAAOD,EAAE,IAAK;IAC/B,IAAI;MACF1B,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMmC,QAAQ,GAAG,MAAM9B,eAAe,CAACsD,EAAE,CAAC;MAE1C,IAAIxB,QAAQ,CAACC,OAAO,EAAE;QACpBd,mBAAmB,CAACa,QAAQ,CAACE,IAAI,CAAC;QAClCjB,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL5C,OAAO,CAAC+D,KAAK,CAAC,kCAAkC,CAAC;MACnD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/D,OAAO,CAAC+D,KAAK,CAAC,iCAAiC,CAAC;IAClD,CAAC,SAAS;MACRN,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM8D,aAAa,GAAG,MAAOF,EAAE,IAAK;IAClC,IAAI;MACF1B,QAAQ,CAACjC,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMmC,QAAQ,GAAG,MAAM7B,eAAe,CAACqD,EAAE,EAAE;QAAEG,QAAQ,EAAE;MAAK,CAAC,CAAC;MAE9D,IAAI3B,QAAQ,CAACC,OAAO,EAAE;QACpB5D,OAAO,CAAC4D,OAAO,CAAC,gCAAgC,CAAC;QACjDF,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACL1D,OAAO,CAAC+D,KAAK,CAAC,4BAA4B,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/D,OAAO,CAAC+D,KAAK,CAAC,0BAA0B,CAAC;IAC3C,CAAC,SAAS;MACRN,QAAQ,CAAClC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMgE,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC,KAAK,YAAY;QAAE,OAAO,MAAM;MAChC,KAAK,SAAS;QAAE,OAAO,QAAQ;MAC/B,KAAK,QAAQ;QAAE,OAAO,KAAK;MAC3B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,aAAa,GAAID,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,oBAAOxD,OAAA,CAACd,OAAO;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpC,KAAK,YAAY;QAAE,oBAAO7D,OAAA,CAACf,OAAO;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrC,KAAK,SAAS;QAAE,oBAAO7D,OAAA,CAACf,OAAO;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClC,KAAK,QAAQ;QAAE,oBAAO7D,OAAA,CAACb,GAAG;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B;QAAS,oBAAO7D,OAAA,CAACf,OAAO;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC7B;EACF,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACEpB,KAAK,EAAE,OAAO;IACdqB,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBnE,OAAA;MAAAoE,QAAA,gBACEpE,OAAA;QAAKqE,SAAS,EAAC,aAAa;QAAAD,QAAA,EAAEF;MAAI;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzC7D,OAAA;QAAKqE,SAAS,EAAC,uBAAuB;QAAAD,QAAA,GACnCD,MAAM,CAACvB,KAAK,EAAC,gBAAS,EAACuB,MAAM,CAACtB,KAAK,EAAC,UAAG,EAACsB,MAAM,CAACrB,OAAO;MAAA;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEnB,KAAK,EAAE,QAAQ;IACfqB,SAAS,EAAE,kBAAkB;IAC7BC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGT,MAAM,iBACbxD,OAAA,CAAC/B,GAAG;MACFqG,KAAK,EAAEf,cAAc,CAACC,MAAM,CAAE;MAC9Be,IAAI,EAAEd,aAAa,CAACD,MAAM,CAAE;MAAAY,QAAA,EAE3BZ,MAAM,CAACgB,WAAW,CAAC;IAAC;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAET,CAAC,EACD;IACEnB,KAAK,EAAE,eAAe;IACtBqB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAGQ,KAAK,IACZA,KAAK,gBACHzE,OAAA,CAAC5B,QAAQ;MACPsG,OAAO,EAAED,KAAM;MACfE,IAAI,EAAC,OAAO;MACZnB,MAAM,EAAEiB,KAAK,IAAI,EAAE,GAAG,SAAS,GAAGA,KAAK,IAAI,EAAE,GAAG,QAAQ,GAAG;IAAY;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC,GACA;EAER,CAAC,EACD;IACEnB,KAAK,EAAE,UAAU;IACjBqB,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGW,IAAI,IAAK,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC;EACtD,CAAC,EACD;IACEpC,KAAK,EAAE,SAAS;IAChBsB,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACc,CAAC,EAAEZ,MAAM,kBAChBnE,OAAA,CAAC9B,KAAK;MAAAkG,QAAA,gBACJpE,OAAA,CAAC7B,OAAO;QAACuE,KAAK,EAAC,cAAc;QAAA0B,QAAA,eAC3BpE,OAAA,CAACtC,MAAM;UACLsH,IAAI,EAAC,MAAM;UACXT,IAAI,eAAEvE,OAAA,CAACtB,KAAK;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChBoB,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAACe,MAAM,CAACe,GAAG;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAETM,MAAM,CAAChC,gBAAgB,KAAK,WAAW,IAAI,CAACgC,MAAM,CAACgB,UAAU,iBAC5DnF,OAAA,CAAC7B,OAAO;QAACuE,KAAK,EAAC,SAAS;QAAA0B,QAAA,eACtBpE,OAAA,CAACtC,MAAM;UACLsH,IAAI,EAAC,MAAM;UACXT,IAAI,eAAEvE,OAAA,CAACd,OAAO;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAClBoB,OAAO,EAAEA,CAAA,KAAM5B,aAAa,CAACc,MAAM,CAACe,GAAG,CAAE;UACzCE,KAAK,EAAE;YAAEd,KAAK,EAAE;UAAQ;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CACV,eAED7D,OAAA,CAAC7B,OAAO;QAACuE,KAAK,EAAC,UAAU;QAAA0B,QAAA,eACvBpE,OAAA,CAACtC,MAAM;UACLsH,IAAI,EAAC,MAAM;UACXT,IAAI,eAAEvE,OAAA,CAACnB,UAAU;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBoB,OAAO,EAAEA,CAAA,KAAMI,MAAM,CAACC,IAAI,CAAE,iBAAgBnB,MAAM,CAACe,GAAI,WAAU;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEV7D,OAAA,CAAC3B,UAAU;QACTqE,KAAK,EAAC,gDAAgD;QACtD6C,SAAS,EAAEA,CAAA,KAAMrC,YAAY,CAACiB,MAAM,CAACe,GAAG,CAAE;QAC1CM,MAAM,EAAC,KAAK;QACZC,UAAU,EAAC,IAAI;QAAArB,QAAA,eAEfpE,OAAA,CAAC7B,OAAO;UAACuE,KAAK,EAAC,QAAQ;UAAA0B,QAAA,eACrBpE,OAAA,CAACtC,MAAM;YACLsH,IAAI,EAAC,MAAM;YACXT,IAAI,eAAEvE,OAAA,CAACpB,OAAO;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAClB6B,MAAM;UAAA;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE7D,OAAA;IAAKqE,SAAS,EAAC,qBAAqB;IAAAD,QAAA,gBAClCpE,OAAA;MAAKqE,SAAS,EAAC,aAAa;MAAAD,QAAA,gBAC1BpE,OAAA;QAAAoE,QAAA,gBACEpE,OAAA;UAAIqE,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACxBpE,OAAA,CAACjB,MAAM;YAACsF,SAAS,EAAC;UAAY;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7D,OAAA;UAAGqE,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAEhC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN7D,OAAA,CAACtC,MAAM;QACLsH,IAAI,EAAC,SAAS;QACdT,IAAI,eAAEvE,OAAA,CAACX,MAAM;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACjBoB,OAAO,EAAEA,CAAA,KAAMvE,qBAAqB,CAAC,IAAI,CAAE;QAC3CiE,IAAI,EAAC,OAAO;QAAAP,QAAA,EACb;MAED;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7D,OAAA,CAAC1B,GAAG;MAACqH,MAAM,EAAE,EAAG;MAACtB,SAAS,EAAC,WAAW;MAAAD,QAAA,gBACpCpE,OAAA,CAACzB,GAAG;QAACqH,IAAI,EAAE,CAAE;QAAAxB,QAAA,eACXpE,OAAA,CAACxC,IAAI;UAAA4G,QAAA,eACHpE,OAAA,CAACxB,SAAS;YACRkE,KAAK,EAAC,kBAAkB;YACxBmD,KAAK,EAAE5E,KAAK,CAACE,KAAM;YACnB2E,MAAM,eAAE9F,OAAA,CAAClB,UAAU;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7D,OAAA,CAACzB,GAAG;QAACqH,IAAI,EAAE,CAAE;QAAAxB,QAAA,eACXpE,OAAA,CAACxC,IAAI;UAAA4G,QAAA,eACHpE,OAAA,CAACxB,SAAS;YACRkE,KAAK,EAAC,WAAW;YACjBmD,KAAK,EAAE5E,KAAK,CAACI,SAAU;YACvByE,MAAM,eAAE9F,OAAA,CAACd,OAAO;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACpBkC,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7D,OAAA,CAACzB,GAAG;QAACqH,IAAI,EAAE,CAAE;QAAAxB,QAAA,eACXpE,OAAA,CAACxC,IAAI;UAAA4G,QAAA,eACHpE,OAAA,CAACxB,SAAS;YACRkE,KAAK,EAAC,YAAY;YAClBmD,KAAK,EAAE5E,KAAK,CAACG,OAAQ;YACrB0E,MAAM,eAAE9F,OAAA,CAACf,OAAO;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACpBkC,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7D,OAAA,CAACzB,GAAG;QAACqH,IAAI,EAAE,CAAE;QAAAxB,QAAA,eACXpE,OAAA,CAACxC,IAAI;UAAA4G,QAAA,eACHpE,OAAA,CAACxB,SAAS;YACRkE,KAAK,EAAC,QAAQ;YACdmD,KAAK,EAAE5E,KAAK,CAACK,MAAO;YACpBwE,MAAM,eAAE9F,OAAA,CAACb,GAAG;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChBkC,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA,CAACxC,IAAI;MAAC6G,SAAS,EAAC,cAAc;MAAAD,QAAA,eAC5BpE,OAAA,CAAC1B,GAAG;QAACqH,MAAM,EAAE,EAAG;QAAAvB,QAAA,gBACdpE,OAAA,CAACzB,GAAG;UAACqH,IAAI,EAAE,CAAE;UAAAxB,QAAA,eACXpE,OAAA,CAAClC,MAAM;YACLkI,WAAW,EAAC,iBAAiB;YAC7BC,UAAU;YACVC,QAAQ,EAAGL,KAAK,IAAK7E,UAAU,CAAC;cAAE,GAAGD,OAAO;cAAE6B,KAAK,EAAEiD;YAAM,CAAC,CAAE;YAC9DT,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO,CAAE;YAAA/B,QAAA,gBAEzBpE,OAAA,CAACC,MAAM;cAAC4F,KAAK,EAAC,SAAS;cAAAzB,QAAA,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC7D,OAAA,CAACC,MAAM;cAAC4F,KAAK,EAAC,WAAW;cAAAzB,QAAA,EAAC;YAAS;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C7D,OAAA,CAACC,MAAM;cAAC4F,KAAK,EAAC,SAAS;cAAAzB,QAAA,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN7D,OAAA,CAACzB,GAAG;UAACqH,IAAI,EAAE,CAAE;UAAAxB,QAAA,eACXpE,OAAA,CAAClC,MAAM;YACLkI,WAAW,EAAC,kBAAkB;YAC9BC,UAAU;YACVC,QAAQ,EAAGL,KAAK,IAAK7E,UAAU,CAAC;cAAE,GAAGD,OAAO;cAAEoB,gBAAgB,EAAE0D;YAAM,CAAC,CAAE;YACzET,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO,CAAE;YAAA/B,QAAA,gBAEzBpE,OAAA,CAACC,MAAM;cAAC4F,KAAK,EAAC,SAAS;cAAAzB,QAAA,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC7D,OAAA,CAACC,MAAM;cAAC4F,KAAK,EAAC,YAAY;cAAAzB,QAAA,EAAC;YAAU;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C7D,OAAA,CAACC,MAAM;cAAC4F,KAAK,EAAC,WAAW;cAAAzB,QAAA,EAAC;YAAS;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C7D,OAAA,CAACC,MAAM;cAAC4F,KAAK,EAAC,QAAQ;cAAAzB,QAAA,EAAC;YAAM;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN7D,OAAA,CAACzB,GAAG;UAACqH,IAAI,EAAE,CAAE;UAAAxB,QAAA,eACXpE,OAAA,CAACnC,KAAK;YACJmI,WAAW,EAAC,mBAAmB;YAC/BC,UAAU;YACVC,QAAQ,EAAGE,CAAC,IAAKpF,UAAU,CAAC;cAAE,GAAGD,OAAO;cAAE+B,OAAO,EAAEsD,CAAC,CAACC,MAAM,CAACR;YAAM,CAAC;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN7D,OAAA,CAACzB,GAAG;UAACqH,IAAI,EAAE,CAAE;UAAAxB,QAAA,eACXpE,OAAA,CAACtC,MAAM;YACL6G,IAAI,eAAEvE,OAAA,CAACZ,SAAS;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACpBoB,OAAO,EAAEvD,eAAgB;YAAA0C,QAAA,EAC1B;UAED;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP7D,OAAA,CAACxC,IAAI;MAAA4G,QAAA,eACHpE,OAAA,CAACvC,KAAK;QACJqG,OAAO,EAAEA,OAAQ;QACjBwC,UAAU,EAAEjG,UAAW;QACvBkG,MAAM,EAAC,KAAK;QACZhG,OAAO,EAAEA,OAAQ;QACjBiG,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACzF,KAAK,EAAE0F,KAAK,KACrB,GAAEA,KAAK,CAAC,CAAC,CAAE,IAAGA,KAAK,CAAC,CAAC,CAAE,OAAM1F,KAAM;QACxC;MAAE;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP7D,OAAA,CAACrC,KAAK;MACJ+E,KAAK,EAAC,qBAAqB;MAC3B4C,IAAI,EAAE7E,kBAAmB;MACzBqG,QAAQ,EAAEA,CAAA,KAAM;QACdpG,qBAAqB,CAAC,KAAK,CAAC;QAC5Ba,IAAI,CAAC0B,WAAW,CAAC,CAAC;MACpB,CAAE;MACF8D,MAAM,EAAE,IAAK;MACbZ,KAAK,EAAE,GAAI;MAAA/B,QAAA,eAEXpE,OAAA,CAACpC,IAAI;QACH2D,IAAI,EAAEA,IAAK;QACXyF,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE7E,YAAa;QAAAgC,QAAA,gBAEvBpE,OAAA,CAACpC,IAAI,CAACsJ,IAAI;UACRC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,OAAO;UACbC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtJ,OAAO,EAAE;UAA8B,CAAC,CAAE;UAAAoG,QAAA,eAEpEpE,OAAA,CAACnC,KAAK;YAACmI,WAAW,EAAC;UAAiD;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eAEZ7D,OAAA,CAACpC,IAAI,CAACsJ,IAAI;UACRC,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,aAAa;UAAAhD,QAAA,eAEnBpE,OAAA,CAACE,QAAQ;YACPqH,IAAI,EAAE,CAAE;YACRvB,WAAW,EAAC;UAA2C;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ7D,OAAA,CAAC1B,GAAG;UAACqH,MAAM,EAAE,EAAG;UAAAvB,QAAA,gBACdpE,OAAA,CAACzB,GAAG;YAACqH,IAAI,EAAE,CAAE;YAAAxB,QAAA,eACXpE,OAAA,CAACpC,IAAI,CAACsJ,IAAI;cACRC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,OAAO;cACbC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtJ,OAAO,EAAE;cAAsB,CAAC,CAAE;cAAAoG,QAAA,eAE5DpE,OAAA,CAAClC,MAAM;gBAACkI,WAAW,EAAC,cAAc;gBAAA5B,QAAA,gBAChCpE,OAAA,CAACC,MAAM;kBAAC4F,KAAK,EAAC,SAAS;kBAAAzB,QAAA,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC7D,OAAA,CAACC,MAAM;kBAAC4F,KAAK,EAAC,WAAW;kBAAAzB,QAAA,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C7D,OAAA,CAACC,MAAM;kBAAC4F,KAAK,EAAC,SAAS;kBAAAzB,QAAA,EAAC;gBAAO;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN7D,OAAA,CAACzB,GAAG;YAACqH,IAAI,EAAE,CAAE;YAAAxB,QAAA,eACXpE,OAAA,CAACpC,IAAI,CAACsJ,IAAI;cACRC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,OAAO;cACbC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtJ,OAAO,EAAE;cAAqB,CAAC,CAAE;cAAAoG,QAAA,eAE3DpE,OAAA,CAACnC,KAAK;gBAACmI,WAAW,EAAC;cAAS;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN7D,OAAA,CAACzB,GAAG;YAACqH,IAAI,EAAE,CAAE;YAAAxB,QAAA,eACXpE,OAAA,CAACpC,IAAI,CAACsJ,IAAI;cACRC,IAAI,EAAC,SAAS;cACdC,KAAK,EAAC,SAAS;cACfC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtJ,OAAO,EAAE;cAAuB,CAAC,CAAE;cAAAoG,QAAA,eAE7DpE,OAAA,CAACnC,KAAK;gBAACmI,WAAW,EAAC;cAAmB;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7D,OAAA,CAAC1B,GAAG;UAACqH,MAAM,EAAE,EAAG;UAAAvB,QAAA,gBACdpE,OAAA,CAACzB,GAAG;YAACqH,IAAI,EAAE,EAAG;YAAAxB,QAAA,eACZpE,OAAA,CAACpC,IAAI,CAACsJ,IAAI;cACRC,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAC,eAAe;cAAAhD,QAAA,eAErBpE,OAAA,CAACnC,KAAK;gBAACmI,WAAW,EAAC;cAAY;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN7D,OAAA,CAACzB,GAAG;YAACqH,IAAI,EAAE,EAAG;YAAAxB,QAAA,eACZpE,OAAA,CAACpC,IAAI,CAACsJ,IAAI;cACRC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,wBAAwB;cAAAhD,QAAA,eAE9BpE,OAAA,CAACnC,KAAK;gBAACmI,WAAW,EAAC;cAAqC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7D,OAAA,CAACpC,IAAI,CAACsJ,IAAI;UACRC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,mBAAmB;UACzBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtJ,OAAO,EAAE;UAA2B,CAAC,CAAE;UAAAoG,QAAA,eAEjEpE,OAAA,CAACjC,MAAM,CAACyJ,OAAO;YACbC,MAAM,EAAC,MAAM;YACbC,QAAQ,EAAE,CAAE;YACZC,YAAY,EAAEA,CAAA,KAAM,KAAM;YAAAvD,QAAA,gBAE1BpE,OAAA;cAAGqE,SAAS,EAAC,sBAAsB;cAAAD,QAAA,eACjCpE,OAAA,CAACvB,QAAQ;gBAAC2G,KAAK,EAAE;kBAAEwC,QAAQ,EAAE;gBAAO;cAAE;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACJ7D,OAAA;cAAGqE,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAC;YAE/B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ7D,OAAA;cAAGqE,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAC;YAE/B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEZ7D,OAAA,CAACpC,IAAI,CAACsJ,IAAI;UAAA9C,QAAA,eACRpE,OAAA,CAAC9B,KAAK;YAAAkG,QAAA,gBACJpE,OAAA,CAACtC,MAAM;cAACsH,IAAI,EAAC,SAAS;cAAC6C,QAAQ,EAAC,QAAQ;cAAAzD,QAAA,EAAC;YAEzC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7D,OAAA,CAACtC,MAAM;cAACuH,OAAO,EAAEA,CAAA,KAAM;gBACrBvE,qBAAqB,CAAC,KAAK,CAAC;gBAC5Ba,IAAI,CAAC0B,WAAW,CAAC,CAAC;cACpB,CAAE;cAAAmB,QAAA,EAAC;YAEH;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR7D,OAAA,CAACrC,KAAK;MACJ+E,KAAK,EAAC,kBAAkB;MACxB4C,IAAI,EAAE3E,gBAAiB;MACvBmG,QAAQ,EAAEA,CAAA,KAAMlG,mBAAmB,CAAC,KAAK,CAAE;MAC3CmG,MAAM,EAAE,IAAK;MACbZ,KAAK,EAAE,GAAI;MAAA/B,QAAA,EAEVvD,gBAAgB,iBACfb,OAAA;QAAKqE,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC/BpE,OAAA,CAAC1B,GAAG;UAACqH,MAAM,EAAE,EAAG;UAAAvB,QAAA,gBACdpE,OAAA,CAACzB,GAAG;YAACqH,IAAI,EAAE,EAAG;YAAAxB,QAAA,gBACZpE,OAAA;cAAAoE,QAAA,gBAAGpE,OAAA;gBAAAoE,QAAA,EAAQ;cAAM;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,gBAAgB,CAAC6B,KAAK;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD7D,OAAA;cAAAoE,QAAA,gBAAGpE,OAAA;gBAAAoE,QAAA,EAAQ;cAAM;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,gBAAgB,CAAC+B,KAAK;YAAA;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD7D,OAAA;cAAAoE,QAAA,gBAAGpE,OAAA;gBAAAoE,QAAA,EAAQ;cAAM;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,gBAAgB,CAACgC,KAAK;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD7D,OAAA;cAAAoE,QAAA,gBAAGpE,OAAA;gBAAAoE,QAAA,EAAQ;cAAQ;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,gBAAgB,CAACiC,OAAO;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACN7D,OAAA,CAACzB,GAAG;YAACqH,IAAI,EAAE,EAAG;YAAAxB,QAAA,gBACZpE,OAAA;cAAAoE,QAAA,gBAAGpE,OAAA;gBAAAoE,QAAA,EAAQ;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzB7D,OAAA,CAAC/B,GAAG;gBAACqG,KAAK,EAAEf,cAAc,CAAC1C,gBAAgB,CAACsB,gBAAgB,CAAE;gBAAAiC,QAAA,EAC3DvD,gBAAgB,CAACsB;cAAgB;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACJ7D,OAAA;cAAAoE,QAAA,gBAAGpE,OAAA;gBAAAoE,QAAA,EAAQ;cAAc;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAChD,gBAAgB,CAACiH,YAAY,IAAI,KAAK;YAAA;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/E7D,OAAA;cAAAoE,QAAA,gBAAGpE,OAAA;gBAAAoE,QAAA,EAAQ;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,CAAChD,gBAAgB,CAACkH,QAAQ,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;YAAA;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5F7D,OAAA;cAAAoE,QAAA,gBAAGpE,OAAA;gBAAAoE,QAAA,EAAQ;cAAS;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,IAAIgB,IAAI,CAAChE,gBAAgB,CAACoH,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;YAAA;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELhD,gBAAgB,CAAC8B,WAAW,iBAC3B3C,OAAA;UAAAoE,QAAA,gBACEpE,OAAA;YAAAoE,QAAA,EAAI;UAAY;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrB7D,OAAA;YAAAoE,QAAA,EAAIvD,gBAAgB,CAAC8B;UAAW;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACN,EAEAhD,gBAAgB,CAACsH,eAAe,IAAItH,gBAAgB,CAACsH,eAAe,CAACnG,MAAM,GAAG,CAAC,iBAC9EhC,OAAA;UAAAoE,QAAA,gBACEpE,OAAA;YAAAoE,QAAA,EAAI;UAAiB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B7D,OAAA;YAAKqE,SAAS,EAAC,aAAa;YAAAD,QAAA,GACzBvD,gBAAgB,CAACsH,eAAe,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC9DvI,OAAA,CAAC/B,GAAG;cAAaoG,SAAS,EAAC,WAAW;cAAAD,QAAA,EACnCkE,KAAK,CAACE;YAAS,GADRD,KAAK;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACN,CAAC,EACDhD,gBAAgB,CAACsH,eAAe,CAACnG,MAAM,GAAG,EAAE,iBAC3ChC,OAAA,CAAC/B,GAAG;cAAAmG,QAAA,GAAC,GAAC,EAACvD,gBAAgB,CAACsH,eAAe,CAACnG,MAAM,GAAG,EAAE,EAAC,OAAK;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAC/D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAhD,gBAAgB,CAAC4H,eAAe,iBAC/BzI,OAAA;UAAAoE,QAAA,gBACEpE,OAAA;YAAAoE,QAAA,EAAI;UAAiB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B7D,OAAA;YAAGoF,KAAK,EAAE;cAAEd,KAAK,EAAE;YAAM,CAAE;YAAAF,QAAA,EAAEvD,gBAAgB,CAAC4H;UAAe;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACzD,EAAA,CAtjBID,kBAAkB;EAAA,QAaPvC,IAAI,CAAC4D,OAAO,EACVlC,WAAW;AAAA;AAAAoJ,EAAA,GAdxBvI,kBAAkB;AAwjBxB,eAAeA,kBAAkB;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}