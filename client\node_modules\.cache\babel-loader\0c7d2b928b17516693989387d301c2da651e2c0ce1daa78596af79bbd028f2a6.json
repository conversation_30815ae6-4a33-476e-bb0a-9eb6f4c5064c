{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\AIQuestionGeneration\\\\AutoGenerateExamModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { Modal, Form, Row, Col, Input, Select, InputNumber, Button, message, Alert, Divider } from \"antd\";\nimport { FaRobot, FaMagic } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addExam } from \"../../../apicalls/exams\";\nimport { generateExamName } from \"../../../apicalls/aiQuestions\";\nimport { getSubjectsForLevel } from \"../../../apicalls/aiQuestions\";\nimport { getSyllabusesForAI } from \"../../../apicalls/syllabus\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction AutoGenerateExamModal({\n  visible,\n  onCancel,\n  onSuccess,\n  prefilledData = {}\n}) {\n  _s();\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [level, setLevel] = useState(prefilledData.level || \"\");\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [availableSyllabuses, setAvailableSyllabuses] = useState([]);\n  const [selectedSyllabus, setSelectedSyllabus] = useState(null);\n  const [autoGeneratedName, setAutoGeneratedName] = useState(\"\");\n  const [isGeneratingName, setIsGeneratingName] = useState(false);\n  const [passRate, setPassRate] = useState(0);\n  const calculatePassRate = useCallback(values => {\n    const totalMarks = (values === null || values === void 0 ? void 0 : values.totalMarks) || 0;\n    const passingMarks = (values === null || values === void 0 ? void 0 : values.passingMarks) || 0;\n    if (totalMarks > 0 && passingMarks > 0) {\n      return Math.round(passingMarks / totalMarks * 100);\n    }\n    return 0;\n  }, []);\n  const handleLevelChange = useCallback(async selectedLevel => {\n    console.log(`🎯 Level changed to: ${selectedLevel}`);\n    const normalizedLevel = selectedLevel.toLowerCase(); // Normalize to lowercase\n    setLevel(normalizedLevel);\n    form.setFieldsValue({\n      class: undefined,\n      category: \"\",\n      syllabus: undefined\n    });\n\n    // First set hardcoded subjects as fallback immediately\n    let fallbackSubjects = [];\n    switch (normalizedLevel) {\n      case \"primary\":\n        fallbackSubjects = [...primarySubjects, \"science and technology\"]; // Add known syllabus subject\n        break;\n      case \"secondary\":\n        fallbackSubjects = secondarySubjects;\n        break;\n      case \"advance\":\n        fallbackSubjects = advanceSubjects;\n        break;\n      default:\n        fallbackSubjects = [];\n    }\n    setAvailableSubjects(fallbackSubjects);\n    console.log(`🔄 Set initial subjects for ${normalizedLevel}:`, fallbackSubjects);\n\n    // Fetch available syllabuses for this level\n    try {\n      console.log(`📚 Fetching syllabuses for level: ${normalizedLevel}`);\n      const syllabusResponse = await getSyllabusesForAI(normalizedLevel);\n      if (syllabusResponse.success && syllabusResponse.data) {\n        setAvailableSyllabuses(syllabusResponse.data);\n        console.log(`✅ Found ${syllabusResponse.data.length} syllabuses for ${normalizedLevel}`);\n      } else {\n        setAvailableSyllabuses([]);\n        console.warn(`⚠️ No syllabuses found for ${normalizedLevel}`);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching syllabuses:', error);\n      setAvailableSyllabuses([]);\n    }\n\n    // Then try to fetch syllabus-based subjects\n    try {\n      console.log(`🔍 Fetching syllabus subjects for level: ${normalizedLevel}`);\n      const response = await getSubjectsForLevel(normalizedLevel);\n      console.log(`📊 Syllabus response received:`, response);\n      if (response.success && response.data && response.data.length > 0) {\n        // Combine syllabus subjects with hardcoded ones (remove duplicates)\n        const combinedSubjects = [...new Set([...response.data, ...fallbackSubjects])];\n        setAvailableSubjects(combinedSubjects);\n        console.log(`✅ Updated with syllabus subjects for ${normalizedLevel}:`, combinedSubjects);\n      } else {\n        console.warn(`⚠️ No syllabus subjects found for ${normalizedLevel}, using fallback`);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching syllabus subjects:', error);\n      console.log(`🔄 Keeping fallback subjects for ${normalizedLevel}`);\n    }\n  }, [form]);\n  const handleSyllabusChange = useCallback(syllabusId => {\n    const selectedSyll = availableSyllabuses.find(s => s._id === syllabusId);\n    setSelectedSyllabus(selectedSyll);\n    if (selectedSyll) {\n      console.log(`📚 Selected syllabus: ${selectedSyll.title}`);\n      console.log(`📚 Covers classes: ${selectedSyll.classes.join(', ')}`);\n      console.log(`📚 Subject: ${selectedSyll.subject}`);\n\n      // Auto-fill subject if it matches\n      if (selectedSyll.subject) {\n        form.setFieldsValue({\n          category: selectedSyll.subject\n        });\n      }\n    }\n  }, [availableSyllabuses, form]);\n  const handleFormValuesChange = useCallback((changedValues, allValues) => {\n    if (changedValues.totalMarks !== undefined || changedValues.passingMarks !== undefined) {\n      setPassRate(calculatePassRate(allValues));\n    }\n  }, [calculatePassRate]);\n  useEffect(() => {\n    if (visible) {\n      // Reset form when modal opens\n      form.resetFields();\n      setLevel(prefilledData.level || \"\");\n\n      // Set initial values if provided\n      if (prefilledData.level) {\n        var _prefilledData$subjec;\n        form.setFieldsValue({\n          level: prefilledData.level,\n          class: prefilledData.class,\n          category: ((_prefilledData$subjec = prefilledData.subjects) === null || _prefilledData$subjec === void 0 ? void 0 : _prefilledData$subjec[0]) || \"\"\n        });\n        handleLevelChange(prefilledData.level);\n      }\n\n      // Initialize pass rate with default values\n      setPassRate(calculatePassRate({\n        totalMarks: 100,\n        passingMarks: 50\n      }));\n    }\n  }, [visible, prefilledData, form, handleLevelChange, calculatePassRate]);\n  const handleAutoGenerateName = async () => {\n    const currentValues = form.getFieldsValue();\n    const {\n      level: formLevel,\n      class: formClass,\n      category\n    } = currentValues;\n    if (!formLevel || !formClass || !category) {\n      message.warning(\"Please select level, class, and category first\");\n      return;\n    }\n    try {\n      setIsGeneratingName(true);\n      const response = await generateExamName(formLevel, formClass, [category]);\n      if (response.success) {\n        setAutoGeneratedName(response.data.examName);\n        form.setFieldsValue({\n          name: response.data.examName\n        });\n        message.success(\"Exam name generated successfully!\");\n      } else {\n        message.error(\"Failed to generate exam name\");\n      }\n    } catch (error) {\n      message.error(\"Error generating exam name\");\n    } finally {\n      setIsGeneratingName(false);\n    }\n  };\n  const onFinish = async values => {\n    try {\n      dispatch(ShowLoading());\n\n      // Prepare exam data following the same structure as manual creation\n      const examData = {\n        name: values.name,\n        duration: values.duration,\n        level: values.level,\n        category: values.category,\n        class: values.class,\n        totalMarks: values.totalMarks,\n        passingMarks: values.passingMarks,\n        description: values.description || `Auto-generated exam for ${values.category} - ${values.level} Level Class ${values.class}`,\n        isPublic: false,\n        // Default to private\n        questions: [],\n        // Start with empty questions array\n        // Add syllabus information for AI reference\n        selectedSyllabusId: values.syllabus || null,\n        syllabusInfo: selectedSyllabus ? {\n          id: selectedSyllabus._id,\n          title: selectedSyllabus.title,\n          subject: selectedSyllabus.subject,\n          classes: selectedSyllabus.classes,\n          qualityScore: selectedSyllabus.qualityScore\n        } : null\n      };\n      const response = await addExam(examData);\n      if (response.success) {\n        message.success(\"Exam created successfully!\");\n        // Ensure we have valid exam data before passing it back\n        if (response.data && response.data._id) {\n          onSuccess(response.data); // Pass the created exam data back\n        } else {\n          // If no data returned, create a minimal exam object for the UI\n          const fallbackExam = {\n            _id: Date.now().toString(),\n            // Temporary ID\n            name: examData.name,\n            category: examData.category,\n            level: examData.level,\n            class: examData.class\n          };\n          onSuccess(fallbackExam);\n        }\n        form.resetFields();\n        setAutoGeneratedName(\"\");\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to create exam\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const getClassOptions = () => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"];\n      case \"advance\":\n        return [\"Form-5\", \"Form-6\"];\n      default:\n        return [];\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 12\n      },\n      children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n        style: {\n          color: \"#1890ff\",\n          fontSize: 20\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Auto-Generate Exam\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 9\n    }, this),\n    open: visible,\n    onCancel: onCancel,\n    footer: null,\n    width: 800,\n    destroyOnClose: true,\n    focusTriggerAfterClose: false,\n    maskClosable: false,\n    children: [/*#__PURE__*/_jsxDEV(Alert, {\n      message: \"Create New Exam\",\n      description: \"This will create a new exam following the same structure as manual exam creation. You can then use this exam for AI question generation.\",\n      type: \"info\",\n      showIcon: true,\n      style: {\n        marginBottom: 24\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      form: form,\n      layout: \"vertical\",\n      onFinish: onFinish,\n      onValuesChange: handleFormValuesChange,\n      initialValues: {\n        duration: 3600,\n        // Default 1 hour\n        totalMarks: 100,\n        passingMarks: 50\n      },\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"Exam Name\",\n            rules: [{\n              required: true,\n              message: \"Please enter exam name\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"Enter exam name or auto-generate\",\n              suffix: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                icon: /*#__PURE__*/_jsxDEV(FaMagic, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 27\n                }, this),\n                onClick: handleAutoGenerateName,\n                loading: isGeneratingName,\n                size: \"small\",\n                children: \"Auto-Generate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"duration\",\n            label: \"Exam Duration (Seconds)\",\n            rules: [{\n              required: true,\n              message: \"Please enter duration\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 300 // Minimum 5 minutes\n              ,\n              max: 14400 // Maximum 4 hours\n              ,\n              style: {\n                width: \"100%\"\n              },\n              placeholder: \"Duration in seconds\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"level\",\n            label: \"Level\",\n            rules: [{\n              required: true,\n              message: \"Please select level\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Level\",\n              onChange: handleLevelChange,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"primary\",\n                children: \"Primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"secondary\",\n                children: \"Secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"advance\",\n                children: \"Advance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"class\",\n            label: \"Class\",\n            rules: [{\n              required: true,\n              message: \"Please select class\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Class\",\n              disabled: !level,\n              children: getClassOptions().map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                value: cls,\n                children: cls\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"category\",\n            label: \"Category (Subject)\",\n            rules: [{\n              required: true,\n              message: \"Please select category\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Category\",\n              disabled: !level,\n              children: availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(Option, {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"syllabus\",\n            label: \"Choose Syllabus (Optional)\",\n            extra: \"Select a specific syllabus for AI to reference when generating questions\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Syllabus (or leave empty for default)\",\n              disabled: !level,\n              allowClear: true,\n              onChange: handleSyllabusChange,\n              children: availableSyllabuses.map(syllabus => /*#__PURE__*/_jsxDEV(Option, {\n                value: syllabus._id,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontWeight: 'bold'\n                    },\n                    children: syllabus.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '12px',\n                      color: '#666'\n                    },\n                    children: [syllabus.subject, \" \\u2022 Classes: \", syllabus.classes.join(', '), \" \\u2022 Quality: \", syllabus.qualityScore || 'N/A', \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 21\n                }, this)\n              }, syllabus._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"totalMarks\",\n            label: \"Total Marks\",\n            rules: [{\n              required: true,\n              message: \"Please enter total marks\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 1,\n              max: 1000,\n              style: {\n                width: \"100%\"\n              },\n              placeholder: \"Total marks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"passingMarks\",\n            label: \"Passing Marks\",\n            rules: [{\n              required: true,\n              message: \"Please enter passing marks\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(InputNumber, {\n              min: 1,\n              max: 1000,\n              style: {\n                width: \"100%\"\n              },\n              placeholder: \"Passing marks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              paddingTop: 30\n            },\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              message: `Pass Rate: ${passRate}%`,\n              type: \"info\",\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"description\",\n            label: \"Description (Optional)\",\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n              rows: 3,\n              placeholder: \"Enter exam description (optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), autoGeneratedName && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Auto-Generated Name\",\n        description: `Generated exam name: ${autoGeneratedName}`,\n        type: \"success\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          justifyContent: \"flex-end\",\n          gap: 12\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: onCancel,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          htmlType: \"submit\",\n          icon: /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 58\n          }, this),\n          children: \"Create Exam\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n}\n_s(AutoGenerateExamModal, \"uUPebIPc1+xmP6+QQ6ZnF3l6nVQ=\", false, function () {\n  return [useDispatch, Form.useForm];\n});\n_c = AutoGenerateExamModal;\nexport default AutoGenerateExamModal;\nvar _c;\n$RefreshReg$(_c, \"AutoGenerateExamModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "Modal", "Form", "Row", "Col", "Input", "Select", "InputNumber", "<PERSON><PERSON>", "message", "<PERSON><PERSON>", "Divider", "FaRobot", "FaMagic", "HideLoading", "ShowLoading", "addExam", "generateExamName", "getSubjectsForLevel", "getSyllabusesForAI", "primarySubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "Option", "AutoGenerateExamModal", "visible", "onCancel", "onSuccess", "prefilledData", "_s", "dispatch", "form", "useForm", "level", "setLevel", "availableSubjects", "setAvailableSubjects", "availableSyllabuses", "setAvailableSyllabuses", "selectedSyllabus", "setSelectedSyllabus", "autoGeneratedName", "setAutoGeneratedName", "isGeneratingName", "setIsGeneratingName", "passRate", "setPassRate", "calculatePassRate", "values", "totalMarks", "passingMarks", "Math", "round", "handleLevelChange", "selectedLevel", "console", "log", "normalizedLevel", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "class", "undefined", "category", "syllabus", "fallbackSubjects", "syllabusResponse", "success", "data", "length", "warn", "error", "response", "combinedSubjects", "Set", "handleSyllabusChange", "syllabusId", "<PERSON><PERSON><PERSON><PERSON>", "find", "s", "_id", "title", "classes", "join", "subject", "handleFormValuesChange", "changedValues", "allValues", "resetFields", "_prefilledData$subjec", "subjects", "handleAutoGenerateName", "currentV<PERSON>ues", "getFieldsValue", "formLevel", "formClass", "warning", "examName", "name", "onFinish", "examData", "duration", "description", "isPublic", "questions", "selectedSyllabusId", "syllabusInfo", "id", "qualityScore", "fallbackExam", "Date", "now", "toString", "getClassOptions", "style", "display", "alignItems", "gap", "children", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "open", "footer", "width", "destroyOnClose", "focusTriggerAfterClose", "maskClosable", "type", "showIcon", "marginBottom", "layout", "onValuesChange", "initialValues", "gutter", "xs", "md", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "suffix", "icon", "onClick", "loading", "size", "min", "max", "onChange", "value", "disabled", "map", "cls", "extra", "allowClear", "fontWeight", "paddingTop", "TextArea", "rows", "justifyContent", "htmlType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/AIQuestionGeneration/AutoGenerateExamModal.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { \n  Modal, \n  Form, \n  Row, \n  Col, \n  Input, \n  Select, \n  InputNumber, \n  Button, \n  message,\n  Alert,\n  Divider\n} from \"antd\";\nimport { FaRobot, FaMagic } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addExam } from \"../../../apicalls/exams\";\nimport { generateExamName } from \"../../../apicalls/aiQuestions\";\nimport { getSubjectsForLevel } from \"../../../apicalls/aiQuestions\";\nimport { getSyllabusesForAI } from \"../../../apicalls/syllabus\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\n\nconst { Option } = Select;\n\nfunction AutoGenerateExamModal({ \n  visible, \n  onCancel, \n  onSuccess, \n  prefilledData = {} \n}) {\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const [level, setLevel] = useState(prefilledData.level || \"\");\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [availableSyllabuses, setAvailableSyllabuses] = useState([]);\n  const [selectedSyllabus, setSelectedSyllabus] = useState(null);\n  const [autoGeneratedName, setAutoGeneratedName] = useState(\"\");\n  const [isGeneratingName, setIsGeneratingName] = useState(false);\n  const [passRate, setPassRate] = useState(0);\n\n  const calculatePassRate = useCallback((values) => {\n    const totalMarks = values?.totalMarks || 0;\n    const passingMarks = values?.passingMarks || 0;\n    if (totalMarks > 0 && passingMarks > 0) {\n      return Math.round((passingMarks / totalMarks) * 100);\n    }\n    return 0;\n  }, []);\n\n  const handleLevelChange = useCallback(async (selectedLevel) => {\n    console.log(`🎯 Level changed to: ${selectedLevel}`);\n    const normalizedLevel = selectedLevel.toLowerCase(); // Normalize to lowercase\n    setLevel(normalizedLevel);\n    form.setFieldsValue({ class: undefined, category: \"\", syllabus: undefined });\n\n    // First set hardcoded subjects as fallback immediately\n    let fallbackSubjects = [];\n    switch (normalizedLevel) {\n      case \"primary\":\n        fallbackSubjects = [...primarySubjects, \"science and technology\"]; // Add known syllabus subject\n        break;\n      case \"secondary\":\n        fallbackSubjects = secondarySubjects;\n        break;\n      case \"advance\":\n        fallbackSubjects = advanceSubjects;\n        break;\n      default:\n        fallbackSubjects = [];\n    }\n    setAvailableSubjects(fallbackSubjects);\n    console.log(`🔄 Set initial subjects for ${normalizedLevel}:`, fallbackSubjects);\n\n    // Fetch available syllabuses for this level\n    try {\n      console.log(`📚 Fetching syllabuses for level: ${normalizedLevel}`);\n      const syllabusResponse = await getSyllabusesForAI(normalizedLevel);\n      if (syllabusResponse.success && syllabusResponse.data) {\n        setAvailableSyllabuses(syllabusResponse.data);\n        console.log(`✅ Found ${syllabusResponse.data.length} syllabuses for ${normalizedLevel}`);\n      } else {\n        setAvailableSyllabuses([]);\n        console.warn(`⚠️ No syllabuses found for ${normalizedLevel}`);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching syllabuses:', error);\n      setAvailableSyllabuses([]);\n    }\n\n    // Then try to fetch syllabus-based subjects\n    try {\n      console.log(`🔍 Fetching syllabus subjects for level: ${normalizedLevel}`);\n      const response = await getSubjectsForLevel(normalizedLevel);\n      console.log(`📊 Syllabus response received:`, response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        // Combine syllabus subjects with hardcoded ones (remove duplicates)\n        const combinedSubjects = [...new Set([...response.data, ...fallbackSubjects])];\n        setAvailableSubjects(combinedSubjects);\n        console.log(`✅ Updated with syllabus subjects for ${normalizedLevel}:`, combinedSubjects);\n      } else {\n        console.warn(`⚠️ No syllabus subjects found for ${normalizedLevel}, using fallback`);\n      }\n    } catch (error) {\n      console.error('❌ Error fetching syllabus subjects:', error);\n      console.log(`🔄 Keeping fallback subjects for ${normalizedLevel}`);\n    }\n  }, [form]);\n\n  const handleSyllabusChange = useCallback((syllabusId) => {\n    const selectedSyll = availableSyllabuses.find(s => s._id === syllabusId);\n    setSelectedSyllabus(selectedSyll);\n\n    if (selectedSyll) {\n      console.log(`📚 Selected syllabus: ${selectedSyll.title}`);\n      console.log(`📚 Covers classes: ${selectedSyll.classes.join(', ')}`);\n      console.log(`📚 Subject: ${selectedSyll.subject}`);\n\n      // Auto-fill subject if it matches\n      if (selectedSyll.subject) {\n        form.setFieldsValue({ category: selectedSyll.subject });\n      }\n    }\n  }, [availableSyllabuses, form]);\n\n  const handleFormValuesChange = useCallback((changedValues, allValues) => {\n    if (changedValues.totalMarks !== undefined || changedValues.passingMarks !== undefined) {\n      setPassRate(calculatePassRate(allValues));\n    }\n  }, [calculatePassRate]);\n\n  useEffect(() => {\n    if (visible) {\n      // Reset form when modal opens\n      form.resetFields();\n      setLevel(prefilledData.level || \"\");\n\n      // Set initial values if provided\n      if (prefilledData.level) {\n        form.setFieldsValue({\n          level: prefilledData.level,\n          class: prefilledData.class,\n          category: prefilledData.subjects?.[0] || \"\",\n        });\n        handleLevelChange(prefilledData.level);\n      }\n\n      // Initialize pass rate with default values\n      setPassRate(calculatePassRate({ totalMarks: 100, passingMarks: 50 }));\n    }\n  }, [visible, prefilledData, form, handleLevelChange, calculatePassRate]);\n\n  const handleAutoGenerateName = async () => {\n    const currentValues = form.getFieldsValue();\n    const { level: formLevel, class: formClass, category } = currentValues;\n    \n    if (!formLevel || !formClass || !category) {\n      message.warning(\"Please select level, class, and category first\");\n      return;\n    }\n\n    try {\n      setIsGeneratingName(true);\n      const response = await generateExamName(formLevel, formClass, [category]);\n      \n      if (response.success) {\n        setAutoGeneratedName(response.data.examName);\n        form.setFieldsValue({ name: response.data.examName });\n        message.success(\"Exam name generated successfully!\");\n      } else {\n        message.error(\"Failed to generate exam name\");\n      }\n    } catch (error) {\n      message.error(\"Error generating exam name\");\n    } finally {\n      setIsGeneratingName(false);\n    }\n  };\n\n  const onFinish = async (values) => {\n    try {\n      dispatch(ShowLoading());\n      \n      // Prepare exam data following the same structure as manual creation\n      const examData = {\n        name: values.name,\n        duration: values.duration,\n        level: values.level,\n        category: values.category,\n        class: values.class,\n        totalMarks: values.totalMarks,\n        passingMarks: values.passingMarks,\n        description: values.description || `Auto-generated exam for ${values.category} - ${values.level} Level Class ${values.class}`,\n        isPublic: false, // Default to private\n        questions: [], // Start with empty questions array\n        // Add syllabus information for AI reference\n        selectedSyllabusId: values.syllabus || null,\n        syllabusInfo: selectedSyllabus ? {\n          id: selectedSyllabus._id,\n          title: selectedSyllabus.title,\n          subject: selectedSyllabus.subject,\n          classes: selectedSyllabus.classes,\n          qualityScore: selectedSyllabus.qualityScore\n        } : null,\n      };\n\n      const response = await addExam(examData);\n\n      if (response.success) {\n        message.success(\"Exam created successfully!\");\n        // Ensure we have valid exam data before passing it back\n        if (response.data && response.data._id) {\n          onSuccess(response.data); // Pass the created exam data back\n        } else {\n          // If no data returned, create a minimal exam object for the UI\n          const fallbackExam = {\n            _id: Date.now().toString(), // Temporary ID\n            name: examData.name,\n            category: examData.category,\n            level: examData.level,\n            class: examData.class,\n          };\n          onSuccess(fallbackExam);\n        }\n        form.resetFields();\n        setAutoGeneratedName(\"\");\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to create exam\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const getClassOptions = () => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"];\n      case \"advance\":\n        return [\"Form-5\", \"Form-6\"];\n      default:\n        return [];\n    }\n  };\n\n  return (\n    <Modal\n      title={\n        <div style={{ display: \"flex\", alignItems: \"center\", gap: 12 }}>\n          <FaRobot style={{ color: \"#1890ff\", fontSize: 20 }} />\n          <span>Auto-Generate Exam</span>\n        </div>\n      }\n      open={visible}\n      onCancel={onCancel}\n      footer={null}\n      width={800}\n      destroyOnClose\n      focusTriggerAfterClose={false}\n      maskClosable={false}\n    >\n      <Alert\n        message=\"Create New Exam\"\n        description=\"This will create a new exam following the same structure as manual exam creation. You can then use this exam for AI question generation.\"\n        type=\"info\"\n        showIcon\n        style={{ marginBottom: 24 }}\n      />\n\n      <Form\n        form={form}\n        layout=\"vertical\"\n        onFinish={onFinish}\n        onValuesChange={handleFormValuesChange}\n        initialValues={{\n          duration: 3600, // Default 1 hour\n          totalMarks: 100,\n          passingMarks: 50,\n        }}\n      >\n        <Row gutter={[16, 16]}>\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"name\"\n              label=\"Exam Name\"\n              rules={[{ required: true, message: \"Please enter exam name\" }]}\n            >\n              <Input \n                placeholder=\"Enter exam name or auto-generate\"\n                suffix={\n                  <Button\n                    type=\"link\"\n                    icon={<FaMagic />}\n                    onClick={handleAutoGenerateName}\n                    loading={isGeneratingName}\n                    size=\"small\"\n                  >\n                    Auto-Generate\n                  </Button>\n                }\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"duration\"\n              label=\"Exam Duration (Seconds)\"\n              rules={[{ required: true, message: \"Please enter duration\" }]}\n            >\n              <InputNumber\n                min={300} // Minimum 5 minutes\n                max={14400} // Maximum 4 hours\n                style={{ width: \"100%\" }}\n                placeholder=\"Duration in seconds\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"level\"\n              label=\"Level\"\n              rules={[{ required: true, message: \"Please select level\" }]}\n            >\n              <Select\n                placeholder=\"Select Level\"\n                onChange={handleLevelChange}\n              >\n                <Option value=\"primary\">Primary</Option>\n                <Option value=\"secondary\">Secondary</Option>\n                <Option value=\"advance\">Advance</Option>\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"class\"\n              label=\"Class\"\n              rules={[{ required: true, message: \"Please select class\" }]}\n            >\n              <Select placeholder=\"Select Class\" disabled={!level}>\n                {getClassOptions().map((cls) => (\n                  <Option key={cls} value={cls}>\n                    {cls}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"category\"\n              label=\"Category (Subject)\"\n              rules={[{ required: true, message: \"Please select category\" }]}\n            >\n              <Select placeholder=\"Select Category\" disabled={!level}>\n                {availableSubjects.map((subject) => (\n                  <Option key={subject} value={subject}>\n                    {subject}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={12}>\n            <Form.Item\n              name=\"syllabus\"\n              label=\"Choose Syllabus (Optional)\"\n              extra=\"Select a specific syllabus for AI to reference when generating questions\"\n            >\n              <Select\n                placeholder=\"Select Syllabus (or leave empty for default)\"\n                disabled={!level}\n                allowClear\n                onChange={handleSyllabusChange}\n              >\n                {availableSyllabuses.map((syllabus) => (\n                  <Option key={syllabus._id} value={syllabus._id}>\n                    <div>\n                      <div style={{ fontWeight: 'bold' }}>{syllabus.title}</div>\n                      <div style={{ fontSize: '12px', color: '#666' }}>\n                        {syllabus.subject} • Classes: {syllabus.classes.join(', ')} •\n                        Quality: {syllabus.qualityScore || 'N/A'}%\n                      </div>\n                    </div>\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"totalMarks\"\n              label=\"Total Marks\"\n              rules={[{ required: true, message: \"Please enter total marks\" }]}\n            >\n              <InputNumber\n                min={1}\n                max={1000}\n                style={{ width: \"100%\" }}\n                placeholder=\"Total marks\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <Form.Item\n              name=\"passingMarks\"\n              label=\"Passing Marks\"\n              rules={[{ required: true, message: \"Please enter passing marks\" }]}\n            >\n              <InputNumber\n                min={1}\n                max={1000}\n                style={{ width: \"100%\" }}\n                placeholder=\"Passing marks\"\n              />\n            </Form.Item>\n          </Col>\n\n          <Col xs={24} md={8}>\n            <div style={{ paddingTop: 30 }}>\n              <Alert\n                message={`Pass Rate: ${passRate}%`}\n                type=\"info\"\n                size=\"small\"\n              />\n            </div>\n          </Col>\n\n          <Col xs={24}>\n            <Form.Item\n              name=\"description\"\n              label=\"Description (Optional)\"\n            >\n              <Input.TextArea\n                rows={3}\n                placeholder=\"Enter exam description (optional)\"\n              />\n            </Form.Item>\n          </Col>\n        </Row>\n\n        {autoGeneratedName && (\n          <Alert\n            message=\"Auto-Generated Name\"\n            description={`Generated exam name: ${autoGeneratedName}`}\n            type=\"success\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        )}\n\n        <Divider />\n\n        <div style={{ display: \"flex\", justifyContent: \"flex-end\", gap: 12 }}>\n          <Button onClick={onCancel}>\n            Cancel\n          </Button>\n          <Button type=\"primary\" htmlType=\"submit\" icon={<FaRobot />}>\n            Create Exam\n          </Button>\n        </div>\n      </Form>\n    </Modal>\n  );\n}\n\nexport default AutoGenerateExamModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SAASC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AACjD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7F,MAAM;EAAEC;AAAO,CAAC,GAAGnB,MAAM;AAEzB,SAASoB,qBAAqBA,CAAC;EAC7BC,OAAO;EACPC,QAAQ;EACRC,SAAS;EACTC,aAAa,GAAG,CAAC;AACnB,CAAC,EAAE;EAAAC,EAAA;EACD,MAAMC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiC,IAAI,CAAC,GAAG/B,IAAI,CAACgC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAACiC,aAAa,CAACK,KAAK,IAAI,EAAE,CAAC;EAC7D,MAAM,CAACE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC0C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAE3C,MAAMoD,iBAAiB,GAAGlD,WAAW,CAAEmD,MAAM,IAAK;IAChD,MAAMC,UAAU,GAAG,CAAAD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,UAAU,KAAI,CAAC;IAC1C,MAAMC,YAAY,GAAG,CAAAF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,YAAY,KAAI,CAAC;IAC9C,IAAID,UAAU,GAAG,CAAC,IAAIC,YAAY,GAAG,CAAC,EAAE;MACtC,OAAOC,IAAI,CAACC,KAAK,CAAEF,YAAY,GAAGD,UAAU,GAAI,GAAG,CAAC;IACtD;IACA,OAAO,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,iBAAiB,GAAGxD,WAAW,CAAC,MAAOyD,aAAa,IAAK;IAC7DC,OAAO,CAACC,GAAG,CAAE,wBAAuBF,aAAc,EAAC,CAAC;IACpD,MAAMG,eAAe,GAAGH,aAAa,CAACI,WAAW,CAAC,CAAC,CAAC,CAAC;IACrDxB,QAAQ,CAACuB,eAAe,CAAC;IACzB1B,IAAI,CAAC4B,cAAc,CAAC;MAAEC,KAAK,EAAEC,SAAS;MAAEC,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAEF;IAAU,CAAC,CAAC;;IAE5E;IACA,IAAIG,gBAAgB,GAAG,EAAE;IACzB,QAAQP,eAAe;MACrB,KAAK,SAAS;QACZO,gBAAgB,GAAG,CAAC,GAAG9C,eAAe,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACnE;MACF,KAAK,WAAW;QACd8C,gBAAgB,GAAG7C,iBAAiB;QACpC;MACF,KAAK,SAAS;QACZ6C,gBAAgB,GAAG5C,eAAe;QAClC;MACF;QACE4C,gBAAgB,GAAG,EAAE;IACzB;IACA5B,oBAAoB,CAAC4B,gBAAgB,CAAC;IACtCT,OAAO,CAACC,GAAG,CAAE,+BAA8BC,eAAgB,GAAE,EAAEO,gBAAgB,CAAC;;IAEhF;IACA,IAAI;MACFT,OAAO,CAACC,GAAG,CAAE,qCAAoCC,eAAgB,EAAC,CAAC;MACnE,MAAMQ,gBAAgB,GAAG,MAAMhD,kBAAkB,CAACwC,eAAe,CAAC;MAClE,IAAIQ,gBAAgB,CAACC,OAAO,IAAID,gBAAgB,CAACE,IAAI,EAAE;QACrD7B,sBAAsB,CAAC2B,gBAAgB,CAACE,IAAI,CAAC;QAC7CZ,OAAO,CAACC,GAAG,CAAE,WAAUS,gBAAgB,CAACE,IAAI,CAACC,MAAO,mBAAkBX,eAAgB,EAAC,CAAC;MAC1F,CAAC,MAAM;QACLnB,sBAAsB,CAAC,EAAE,CAAC;QAC1BiB,OAAO,CAACc,IAAI,CAAE,8BAA6BZ,eAAgB,EAAC,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDhC,sBAAsB,CAAC,EAAE,CAAC;IAC5B;;IAEA;IACA,IAAI;MACFiB,OAAO,CAACC,GAAG,CAAE,4CAA2CC,eAAgB,EAAC,CAAC;MAC1E,MAAMc,QAAQ,GAAG,MAAMvD,mBAAmB,CAACyC,eAAe,CAAC;MAC3DF,OAAO,CAACC,GAAG,CAAE,gCAA+B,EAAEe,QAAQ,CAAC;MAEvD,IAAIA,QAAQ,CAACL,OAAO,IAAIK,QAAQ,CAACJ,IAAI,IAAII,QAAQ,CAACJ,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjE;QACA,MAAMI,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAGF,QAAQ,CAACJ,IAAI,EAAE,GAAGH,gBAAgB,CAAC,CAAC,CAAC;QAC9E5B,oBAAoB,CAACoC,gBAAgB,CAAC;QACtCjB,OAAO,CAACC,GAAG,CAAE,wCAAuCC,eAAgB,GAAE,EAAEe,gBAAgB,CAAC;MAC3F,CAAC,MAAM;QACLjB,OAAO,CAACc,IAAI,CAAE,qCAAoCZ,eAAgB,kBAAiB,CAAC;MACtF;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3Df,OAAO,CAACC,GAAG,CAAE,oCAAmCC,eAAgB,EAAC,CAAC;IACpE;EACF,CAAC,EAAE,CAAC1B,IAAI,CAAC,CAAC;EAEV,MAAM2C,oBAAoB,GAAG7E,WAAW,CAAE8E,UAAU,IAAK;IACvD,MAAMC,YAAY,GAAGvC,mBAAmB,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,UAAU,CAAC;IACxEnC,mBAAmB,CAACoC,YAAY,CAAC;IAEjC,IAAIA,YAAY,EAAE;MAChBrB,OAAO,CAACC,GAAG,CAAE,yBAAwBoB,YAAY,CAACI,KAAM,EAAC,CAAC;MAC1DzB,OAAO,CAACC,GAAG,CAAE,sBAAqBoB,YAAY,CAACK,OAAO,CAACC,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;MACpE3B,OAAO,CAACC,GAAG,CAAE,eAAcoB,YAAY,CAACO,OAAQ,EAAC,CAAC;;MAElD;MACA,IAAIP,YAAY,CAACO,OAAO,EAAE;QACxBpD,IAAI,CAAC4B,cAAc,CAAC;UAAEG,QAAQ,EAAEc,YAAY,CAACO;QAAQ,CAAC,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAAC9C,mBAAmB,EAAEN,IAAI,CAAC,CAAC;EAE/B,MAAMqD,sBAAsB,GAAGvF,WAAW,CAAC,CAACwF,aAAa,EAAEC,SAAS,KAAK;IACvE,IAAID,aAAa,CAACpC,UAAU,KAAKY,SAAS,IAAIwB,aAAa,CAACnC,YAAY,KAAKW,SAAS,EAAE;MACtFf,WAAW,CAACC,iBAAiB,CAACuC,SAAS,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAACvC,iBAAiB,CAAC,CAAC;EAEvBnD,SAAS,CAAC,MAAM;IACd,IAAI6B,OAAO,EAAE;MACX;MACAM,IAAI,CAACwD,WAAW,CAAC,CAAC;MAClBrD,QAAQ,CAACN,aAAa,CAACK,KAAK,IAAI,EAAE,CAAC;;MAEnC;MACA,IAAIL,aAAa,CAACK,KAAK,EAAE;QAAA,IAAAuD,qBAAA;QACvBzD,IAAI,CAAC4B,cAAc,CAAC;UAClB1B,KAAK,EAAEL,aAAa,CAACK,KAAK;UAC1B2B,KAAK,EAAEhC,aAAa,CAACgC,KAAK;UAC1BE,QAAQ,EAAE,EAAA0B,qBAAA,GAAA5D,aAAa,CAAC6D,QAAQ,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAyB,CAAC,CAAC,KAAI;QAC3C,CAAC,CAAC;QACFnC,iBAAiB,CAACzB,aAAa,CAACK,KAAK,CAAC;MACxC;;MAEA;MACAa,WAAW,CAACC,iBAAiB,CAAC;QAAEE,UAAU,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAG,CAAC,CAAC,CAAC;IACvE;EACF,CAAC,EAAE,CAACzB,OAAO,EAAEG,aAAa,EAAEG,IAAI,EAAEsB,iBAAiB,EAAEN,iBAAiB,CAAC,CAAC;EAExE,MAAM2C,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,MAAMC,aAAa,GAAG5D,IAAI,CAAC6D,cAAc,CAAC,CAAC;IAC3C,MAAM;MAAE3D,KAAK,EAAE4D,SAAS;MAAEjC,KAAK,EAAEkC,SAAS;MAAEhC;IAAS,CAAC,GAAG6B,aAAa;IAEtE,IAAI,CAACE,SAAS,IAAI,CAACC,SAAS,IAAI,CAAChC,QAAQ,EAAE;MACzCvD,OAAO,CAACwF,OAAO,CAAC,gDAAgD,CAAC;MACjE;IACF;IAEA,IAAI;MACFnD,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAM2B,QAAQ,GAAG,MAAMxD,gBAAgB,CAAC8E,SAAS,EAAEC,SAAS,EAAE,CAAChC,QAAQ,CAAC,CAAC;MAEzE,IAAIS,QAAQ,CAACL,OAAO,EAAE;QACpBxB,oBAAoB,CAAC6B,QAAQ,CAACJ,IAAI,CAAC6B,QAAQ,CAAC;QAC5CjE,IAAI,CAAC4B,cAAc,CAAC;UAAEsC,IAAI,EAAE1B,QAAQ,CAACJ,IAAI,CAAC6B;QAAS,CAAC,CAAC;QACrDzF,OAAO,CAAC2D,OAAO,CAAC,mCAAmC,CAAC;MACtD,CAAC,MAAM;QACL3D,OAAO,CAAC+D,KAAK,CAAC,8BAA8B,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/D,OAAO,CAAC+D,KAAK,CAAC,4BAA4B,CAAC;IAC7C,CAAC,SAAS;MACR1B,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMsD,QAAQ,GAAG,MAAOlD,MAAM,IAAK;IACjC,IAAI;MACFlB,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAMsF,QAAQ,GAAG;QACfF,IAAI,EAAEjD,MAAM,CAACiD,IAAI;QACjBG,QAAQ,EAAEpD,MAAM,CAACoD,QAAQ;QACzBnE,KAAK,EAAEe,MAAM,CAACf,KAAK;QACnB6B,QAAQ,EAAEd,MAAM,CAACc,QAAQ;QACzBF,KAAK,EAAEZ,MAAM,CAACY,KAAK;QACnBX,UAAU,EAAED,MAAM,CAACC,UAAU;QAC7BC,YAAY,EAAEF,MAAM,CAACE,YAAY;QACjCmD,WAAW,EAAErD,MAAM,CAACqD,WAAW,IAAK,2BAA0BrD,MAAM,CAACc,QAAS,MAAKd,MAAM,CAACf,KAAM,gBAAee,MAAM,CAACY,KAAM,EAAC;QAC7H0C,QAAQ,EAAE,KAAK;QAAE;QACjBC,SAAS,EAAE,EAAE;QAAE;QACf;QACAC,kBAAkB,EAAExD,MAAM,CAACe,QAAQ,IAAI,IAAI;QAC3C0C,YAAY,EAAElE,gBAAgB,GAAG;UAC/BmE,EAAE,EAAEnE,gBAAgB,CAACwC,GAAG;UACxBC,KAAK,EAAEzC,gBAAgB,CAACyC,KAAK;UAC7BG,OAAO,EAAE5C,gBAAgB,CAAC4C,OAAO;UACjCF,OAAO,EAAE1C,gBAAgB,CAAC0C,OAAO;UACjC0B,YAAY,EAAEpE,gBAAgB,CAACoE;QACjC,CAAC,GAAG;MACN,CAAC;MAED,MAAMpC,QAAQ,GAAG,MAAMzD,OAAO,CAACqF,QAAQ,CAAC;MAExC,IAAI5B,QAAQ,CAACL,OAAO,EAAE;QACpB3D,OAAO,CAAC2D,OAAO,CAAC,4BAA4B,CAAC;QAC7C;QACA,IAAIK,QAAQ,CAACJ,IAAI,IAAII,QAAQ,CAACJ,IAAI,CAACY,GAAG,EAAE;UACtCpD,SAAS,CAAC4C,QAAQ,CAACJ,IAAI,CAAC,CAAC,CAAC;QAC5B,CAAC,MAAM;UACL;UACA,MAAMyC,YAAY,GAAG;YACnB7B,GAAG,EAAE8B,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;YAAE;YAC5Bd,IAAI,EAAEE,QAAQ,CAACF,IAAI;YACnBnC,QAAQ,EAAEqC,QAAQ,CAACrC,QAAQ;YAC3B7B,KAAK,EAAEkE,QAAQ,CAAClE,KAAK;YACrB2B,KAAK,EAAEuC,QAAQ,CAACvC;UAClB,CAAC;UACDjC,SAAS,CAACiF,YAAY,CAAC;QACzB;QACA7E,IAAI,CAACwD,WAAW,CAAC,CAAC;QAClB7C,oBAAoB,CAAC,EAAE,CAAC;MAC1B,CAAC,MAAM;QACLnC,OAAO,CAAC+D,KAAK,CAACC,QAAQ,CAAChE,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACd/D,OAAO,CAAC+D,KAAK,CAAC,uBAAuB,CAAC;IACxC,CAAC,SAAS;MACRxC,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMoG,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQ/E,KAAK;MACX,KAAK,SAAS;QACZ,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC5C,KAAK,WAAW;QACd,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;MACjD,KAAK,SAAS;QACZ,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAC7B;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,oBACEX,OAAA,CAACvB,KAAK;IACJiF,KAAK,eACH1D,OAAA;MAAK2F,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAG,CAAE;MAAAC,QAAA,gBAC7D/F,OAAA,CAACZ,OAAO;QAACuG,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAG;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDrG,OAAA;QAAA+F,QAAA,EAAM;MAAkB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACN;IACDC,IAAI,EAAEnG,OAAQ;IACdC,QAAQ,EAAEA,QAAS;IACnBmG,MAAM,EAAE,IAAK;IACbC,KAAK,EAAE,GAAI;IACXC,cAAc;IACdC,sBAAsB,EAAE,KAAM;IAC9BC,YAAY,EAAE,KAAM;IAAAZ,QAAA,gBAEpB/F,OAAA,CAACd,KAAK;MACJD,OAAO,EAAC,iBAAiB;MACzB8F,WAAW,EAAC,0IAA0I;MACtJ6B,IAAI,EAAC,MAAM;MACXC,QAAQ;MACRlB,KAAK,EAAE;QAAEmB,YAAY,EAAE;MAAG;IAAE;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAEFrG,OAAA,CAACtB,IAAI;MACH+B,IAAI,EAAEA,IAAK;MACXsG,MAAM,EAAC,UAAU;MACjBnC,QAAQ,EAAEA,QAAS;MACnBoC,cAAc,EAAElD,sBAAuB;MACvCmD,aAAa,EAAE;QACbnC,QAAQ,EAAE,IAAI;QAAE;QAChBnD,UAAU,EAAE,GAAG;QACfC,YAAY,EAAE;MAChB,CAAE;MAAAmE,QAAA,gBAEF/F,OAAA,CAACrB,GAAG;QAACuI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAnB,QAAA,gBACpB/F,OAAA,CAACpB,GAAG;UAACuI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAClB/F,OAAA,CAACtB,IAAI,CAAC2I,IAAI;YACR1C,IAAI,EAAC,MAAM;YACX2C,KAAK,EAAC,WAAW;YACjBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEvI,OAAO,EAAE;YAAyB,CAAC,CAAE;YAAA8G,QAAA,eAE/D/F,OAAA,CAACnB,KAAK;cACJ4I,WAAW,EAAC,kCAAkC;cAC9CC,MAAM,eACJ1H,OAAA,CAAChB,MAAM;gBACL4H,IAAI,EAAC,MAAM;gBACXe,IAAI,eAAE3H,OAAA,CAACX,OAAO;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAClBuB,OAAO,EAAExD,sBAAuB;gBAChCyD,OAAO,EAAExG,gBAAiB;gBAC1ByG,IAAI,EAAC,OAAO;gBAAA/B,QAAA,EACb;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YACT;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENrG,OAAA,CAACpB,GAAG;UAACuI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAClB/F,OAAA,CAACtB,IAAI,CAAC2I,IAAI;YACR1C,IAAI,EAAC,UAAU;YACf2C,KAAK,EAAC,yBAAyB;YAC/BC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEvI,OAAO,EAAE;YAAwB,CAAC,CAAE;YAAA8G,QAAA,eAE9D/F,OAAA,CAACjB,WAAW;cACVgJ,GAAG,EAAE,GAAI,CAAC;cAAA;cACVC,GAAG,EAAE,KAAM,CAAC;cAAA;cACZrC,KAAK,EAAE;gBAAEa,KAAK,EAAE;cAAO,CAAE;cACzBiB,WAAW,EAAC;YAAqB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENrG,OAAA,CAACpB,GAAG;UAACuI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB/F,OAAA,CAACtB,IAAI,CAAC2I,IAAI;YACR1C,IAAI,EAAC,OAAO;YACZ2C,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEvI,OAAO,EAAE;YAAsB,CAAC,CAAE;YAAA8G,QAAA,eAE5D/F,OAAA,CAAClB,MAAM;cACL2I,WAAW,EAAC,cAAc;cAC1BQ,QAAQ,EAAElG,iBAAkB;cAAAgE,QAAA,gBAE5B/F,OAAA,CAACC,MAAM;gBAACiI,KAAK,EAAC,SAAS;gBAAAnC,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCrG,OAAA,CAACC,MAAM;gBAACiI,KAAK,EAAC,WAAW;gBAAAnC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CrG,OAAA,CAACC,MAAM;gBAACiI,KAAK,EAAC,SAAS;gBAAAnC,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENrG,OAAA,CAACpB,GAAG;UAACuI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB/F,OAAA,CAACtB,IAAI,CAAC2I,IAAI;YACR1C,IAAI,EAAC,OAAO;YACZ2C,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEvI,OAAO,EAAE;YAAsB,CAAC,CAAE;YAAA8G,QAAA,eAE5D/F,OAAA,CAAClB,MAAM;cAAC2I,WAAW,EAAC,cAAc;cAACU,QAAQ,EAAE,CAACxH,KAAM;cAAAoF,QAAA,EACjDL,eAAe,CAAC,CAAC,CAAC0C,GAAG,CAAEC,GAAG,iBACzBrI,OAAA,CAACC,MAAM;gBAAWiI,KAAK,EAAEG,GAAI;gBAAAtC,QAAA,EAC1BsC;cAAG,GADOA,GAAG;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENrG,OAAA,CAACpB,GAAG;UAACuI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB/F,OAAA,CAACtB,IAAI,CAAC2I,IAAI;YACR1C,IAAI,EAAC,UAAU;YACf2C,KAAK,EAAC,oBAAoB;YAC1BC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEvI,OAAO,EAAE;YAAyB,CAAC,CAAE;YAAA8G,QAAA,eAE/D/F,OAAA,CAAClB,MAAM;cAAC2I,WAAW,EAAC,iBAAiB;cAACU,QAAQ,EAAE,CAACxH,KAAM;cAAAoF,QAAA,EACpDlF,iBAAiB,CAACuH,GAAG,CAAEvE,OAAO,iBAC7B7D,OAAA,CAACC,MAAM;gBAAeiI,KAAK,EAAErE,OAAQ;gBAAAkC,QAAA,EAClClC;cAAO,GADGA,OAAO;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENrG,OAAA,CAACpB,GAAG;UAACuI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAClB/F,OAAA,CAACtB,IAAI,CAAC2I,IAAI;YACR1C,IAAI,EAAC,UAAU;YACf2C,KAAK,EAAC,4BAA4B;YAClCgB,KAAK,EAAC,0EAA0E;YAAAvC,QAAA,eAEhF/F,OAAA,CAAClB,MAAM;cACL2I,WAAW,EAAC,8CAA8C;cAC1DU,QAAQ,EAAE,CAACxH,KAAM;cACjB4H,UAAU;cACVN,QAAQ,EAAE7E,oBAAqB;cAAA2C,QAAA,EAE9BhF,mBAAmB,CAACqH,GAAG,CAAE3F,QAAQ,iBAChCzC,OAAA,CAACC,MAAM;gBAAoBiI,KAAK,EAAEzF,QAAQ,CAACgB,GAAI;gBAAAsC,QAAA,eAC7C/F,OAAA;kBAAA+F,QAAA,gBACE/F,OAAA;oBAAK2F,KAAK,EAAE;sBAAE6C,UAAU,EAAE;oBAAO,CAAE;oBAAAzC,QAAA,EAAEtD,QAAQ,CAACiB;kBAAK;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1DrG,OAAA;oBAAK2F,KAAK,EAAE;sBAAEM,QAAQ,EAAE,MAAM;sBAAED,KAAK,EAAE;oBAAO,CAAE;oBAAAD,QAAA,GAC7CtD,QAAQ,CAACoB,OAAO,EAAC,mBAAY,EAACpB,QAAQ,CAACkB,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,EAAC,mBAClD,EAACnB,QAAQ,CAAC4C,YAAY,IAAI,KAAK,EAAC,GAC3C;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAPK5D,QAAQ,CAACgB,GAAG;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQjB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENrG,OAAA,CAACpB,GAAG;UAACuI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB/F,OAAA,CAACtB,IAAI,CAAC2I,IAAI;YACR1C,IAAI,EAAC,YAAY;YACjB2C,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEvI,OAAO,EAAE;YAA2B,CAAC,CAAE;YAAA8G,QAAA,eAEjE/F,OAAA,CAACjB,WAAW;cACVgJ,GAAG,EAAE,CAAE;cACPC,GAAG,EAAE,IAAK;cACVrC,KAAK,EAAE;gBAAEa,KAAK,EAAE;cAAO,CAAE;cACzBiB,WAAW,EAAC;YAAa;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENrG,OAAA,CAACpB,GAAG;UAACuI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB/F,OAAA,CAACtB,IAAI,CAAC2I,IAAI;YACR1C,IAAI,EAAC,cAAc;YACnB2C,KAAK,EAAC,eAAe;YACrBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEvI,OAAO,EAAE;YAA6B,CAAC,CAAE;YAAA8G,QAAA,eAEnE/F,OAAA,CAACjB,WAAW;cACVgJ,GAAG,EAAE,CAAE;cACPC,GAAG,EAAE,IAAK;cACVrC,KAAK,EAAE;gBAAEa,KAAK,EAAE;cAAO,CAAE;cACzBiB,WAAW,EAAC;YAAe;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENrG,OAAA,CAACpB,GAAG;UAACuI,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACjB/F,OAAA;YAAK2F,KAAK,EAAE;cAAE8C,UAAU,EAAE;YAAG,CAAE;YAAA1C,QAAA,eAC7B/F,OAAA,CAACd,KAAK;cACJD,OAAO,EAAG,cAAasC,QAAS,GAAG;cACnCqF,IAAI,EAAC,MAAM;cACXkB,IAAI,EAAC;YAAO;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrG,OAAA,CAACpB,GAAG;UAACuI,EAAE,EAAE,EAAG;UAAApB,QAAA,eACV/F,OAAA,CAACtB,IAAI,CAAC2I,IAAI;YACR1C,IAAI,EAAC,aAAa;YAClB2C,KAAK,EAAC,wBAAwB;YAAAvB,QAAA,eAE9B/F,OAAA,CAACnB,KAAK,CAAC6J,QAAQ;cACbC,IAAI,EAAE,CAAE;cACRlB,WAAW,EAAC;YAAmC;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELlF,iBAAiB,iBAChBnB,OAAA,CAACd,KAAK;QACJD,OAAO,EAAC,qBAAqB;QAC7B8F,WAAW,EAAG,wBAAuB5D,iBAAkB,EAAE;QACzDyF,IAAI,EAAC,SAAS;QACdC,QAAQ;QACRlB,KAAK,EAAE;UAAEmB,YAAY,EAAE;QAAG;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF,eAEDrG,OAAA,CAACb,OAAO;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXrG,OAAA;QAAK2F,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEgD,cAAc,EAAE,UAAU;UAAE9C,GAAG,EAAE;QAAG,CAAE;QAAAC,QAAA,gBACnE/F,OAAA,CAAChB,MAAM;UAAC4I,OAAO,EAAExH,QAAS;UAAA2F,QAAA,EAAC;QAE3B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrG,OAAA,CAAChB,MAAM;UAAC4H,IAAI,EAAC,SAAS;UAACiC,QAAQ,EAAC,QAAQ;UAAClB,IAAI,eAAE3H,OAAA,CAACZ,OAAO;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,EAAC;QAE5D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ;AAAC9F,EAAA,CAncQL,qBAAqB;EAAA,QAMX1B,WAAW,EACbE,IAAI,CAACgC,OAAO;AAAA;AAAAoI,EAAA,GAPpB5I,qBAAqB;AAqc9B,eAAeA,qBAAqB;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}