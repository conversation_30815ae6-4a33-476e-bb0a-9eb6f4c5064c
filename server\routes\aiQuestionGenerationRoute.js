const router = require("express").Router();
const authMiddleware = require("../middlewares/authMiddleware");
const enhancedAuthMiddleware = require("../middlewares/enhancedAuthMiddleware");
const AIQuestionGenerationService = require("../services/aiQuestionGenerationService");
const { AIQuestionGeneration } = require("../models/aiQuestionGenerationModel");
const Question = require("../models/questionModel");
const Exam = require("../models/examModel");
const { primarySubjects, secondarySubjects, advanceSubjects } = require("../data/Subjects");
const {
  primarySyllabus,
  ordinarySecondarySyllabus,
  advancedSecondarySyllabus
} = require("../data/tieSyllabus");

const aiService = new AIQuestionGenerationService();

// Generate questions using AI
router.post("/generate-questions", enhancedAuthMiddleware, async (req, res) => {
  try {
    // Set a longer timeout for AI generation requests
    req.setTimeout(600000); // 10 minutes
    res.setTimeout(600000); // 10 minutes
    const {
      examId,
      questionTypes,
      subjects,
      level,
      class: className,
      difficultyLevels,
      syllabusTopics,
      totalQuestions,
      questionDistribution,
      selectedSyllabusId
    } = req.body;

    // Validation
    if (!questionTypes || !subjects || !level || !className || !totalQuestions) {
      return res.status(400).send({
        message: "Missing required fields: questionTypes, subjects, level, className, totalQuestions",
        success: false,
      });
    }

    // Verify exam exists if examId is provided
    let exam = null;
    if (examId) {
      exam = await Exam.findById(examId);
      if (!exam) {
        return res.status(404).send({
          message: "Exam not found",
          success: false,
        });
      }
    }

    // Validate subjects for the level
    let validSubjects;
    switch (level) {
      case "primary":
        validSubjects = primarySubjects;
        break;
      case "secondary":
        validSubjects = secondarySubjects;
        break;
      case "advance":
        validSubjects = advanceSubjects;
        break;
      default:
        return res.status(400).send({
          message: "Invalid level",
          success: false,
        });
    }

    const invalidSubjects = subjects.filter(subject => !validSubjects.includes(subject));
    if (invalidSubjects.length > 0) {
      return res.status(400).send({
        message: `Invalid subjects for ${level} level: ${invalidSubjects.join(", ")}`,
        success: false,
      });
    }

    // Validate question distribution
    const totalDistribution = Object.values(questionDistribution).reduce((sum, count) => sum + count, 0);
    if (totalDistribution !== totalQuestions) {
      return res.status(400).send({
        message: "Question distribution does not match total questions",
        success: false,
      });
    }

    const generationParams = {
      questionTypes,
      subjects,
      level,
      class: className,
      difficultyLevels: difficultyLevels || ["medium"],
      syllabusTopics: syllabusTopics || [],
      totalQuestions,
      questionDistribution,
      selectedSyllabusId: selectedSyllabusId || null
    };

    // Generate questions
    const result = await aiService.generateQuestions(
      generationParams,
      req.body.userId,
      examId
    );

    if (result.success) {
      res.send({
        message: "Questions generated successfully",
        success: true,
        data: {
          generationId: result.generationId,
          questions: result.questions,
          generationTime: result.generationTime,
        },
      });
    } else {
      res.status(500).send({
        message: "Failed to generate questions",
        success: false,
        error: result.error,
      });
    }

  } catch (error) {
    console.error("Generate questions error:", error);

    // More detailed error logging
    if (error.response) {
      console.error("OpenAI API error response:", error.response.data);
      console.error("OpenAI API error status:", error.response.status);
    }

    res.status(500).send({
      message: error.message,
      success: false,
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Get generation history
router.get("/generation-history", authMiddleware, async (req, res) => {
  try {
    const { page = 1, limit = 10, examId } = req.query;
    
    const query = {};
    if (examId) {
      query.examId = examId;
    }

    const generations = await AIQuestionGeneration.find(query)
      .populate("requestedBy", "name email")
      .populate("examId", "name")
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await AIQuestionGeneration.countDocuments(query);

    res.send({
      message: "Generation history retrieved successfully",
      success: true,
      data: {
        generations,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        total,
      },
    });

  } catch (error) {
    console.error("Get generation history error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get specific generation details
router.get("/generation/:id", authMiddleware, async (req, res) => {
  try {
    const generation = await AIQuestionGeneration.findById(req.params.id)
      .populate("requestedBy", "name email")
      .populate("examId", "name")
      .populate("generatedQuestions.questionId");

    if (!generation) {
      return res.status(404).send({
        message: "Generation not found",
        success: false,
      });
    }

    res.send({
      message: "Generation details retrieved successfully",
      success: true,
      data: generation,
    });

  } catch (error) {
    console.error("Get generation details error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Approve generated questions and add to exam
router.post("/approve-questions", authMiddleware, async (req, res) => {
  try {
    const { generationId, approvedQuestionIds, rejectedQuestions } = req.body;

    const generation = await AIQuestionGeneration.findById(generationId);
    if (!generation) {
      return res.status(404).send({
        message: "Generation not found",
        success: false,
      });
    }

    const exam = await Exam.findById(generation.examId);
    if (!exam) {
      return res.status(404).send({
        message: "Exam not found",
        success: false,
      });
    }

    const approvedQuestions = [];

    // Process approved questions
    for (const questionIndex of approvedQuestionIds) {
      const generatedQuestion = generation.generatedQuestions[questionIndex];
      if (generatedQuestion) {
        // Create new question in database
        const newQuestion = new Question({
          ...generatedQuestion.generatedContent,
          exam: generation.examId,
        });
        
        const savedQuestion = await newQuestion.save();
        approvedQuestions.push(savedQuestion);

        // Add to exam
        exam.questions.push(savedQuestion._id);

        // Update generation record
        generation.generatedQuestions[questionIndex].approved = true;
        generation.generatedQuestions[questionIndex].questionId = savedQuestion._id;
      }
    }

    // Process rejected questions
    if (rejectedQuestions) {
      rejectedQuestions.forEach(rejection => {
        const questionIndex = rejection.questionIndex;
        if (generation.generatedQuestions[questionIndex]) {
          generation.generatedQuestions[questionIndex].approved = false;
          generation.generatedQuestions[questionIndex].rejectionReason = rejection.reason;
        }
      });
    }

    await generation.save();
    await exam.save();

    // Debug: Check if questions were actually added
    console.log("=== APPROVAL DEBUG ===");
    console.log("Approved questions count:", approvedQuestions.length);
    console.log("Exam questions after save:", exam.questions.length);
    console.log("Exam questions IDs:", exam.questions);
    console.log("======================");

    res.send({
      message: `${approvedQuestions.length} questions approved and added to exam`,
      success: true,
      data: {
        approvedQuestions: approvedQuestions.length,
        addedToExam: approvedQuestions.map(q => q._id),
      },
    });

  } catch (error) {
    console.error("Approve questions error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Preview generated questions before approval
router.get("/preview/:generationId", authMiddleware, async (req, res) => {
  try {
    const generation = await AIQuestionGeneration.findById(req.params.generationId);
    
    if (!generation) {
      return res.status(404).send({
        message: "Generation not found",
        success: false,
      });
    }

    const previewData = {
      generationId: generation._id,
      status: generation.generationStatus,
      totalQuestions: generation.generatedQuestions.length,
      generationTime: generation.generationTime,
      questions: generation.generatedQuestions.map((q, index) => ({
        index,
        ...q.generatedContent,
        approved: q.approved,
        rejectionReason: q.rejectionReason,
      })),
    };

    res.send({
      message: "Preview data retrieved successfully",
      success: true,
      data: previewData,
    });

  } catch (error) {
    console.error("Preview questions error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get available subjects for a level
router.get("/subjects/:level", authMiddleware, async (req, res) => {
  try {
    const { level } = req.params;
    
    let subjects;
    switch (level) {
      case "primary":
        subjects = primarySubjects;
        break;
      case "secondary":
        subjects = secondarySubjects;
        break;
      case "advance":
        subjects = advanceSubjects;
        break;
      default:
        return res.status(400).send({
          message: "Invalid level",
          success: false,
        });
    }

    res.send({
      message: "Subjects retrieved successfully",
      success: true,
      data: subjects,
    });

  } catch (error) {
    console.error("Get subjects error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get Tanzania syllabus topics for specific level, class, and subject
router.get("/syllabus-topics/:level/:class/:subject", authMiddleware, async (req, res) => {
  try {
    const { level, class: className, subject } = req.params;

    let syllabusData;
    switch (level) {
      case "primary":
        syllabusData = primarySyllabus;
        break;
      case "secondary":
        syllabusData = secondarySyllabus;
        break;
      case "advance":
        syllabusData = advanceSyllabus;
        break;
      default:
        return res.status(400).send({
          message: "Invalid education level",
          success: false,
        });
    }

    // Get topics for the specific subject and class
    const subjectData = syllabusData[subject];
    if (!subjectData) {
      return res.status(404).send({
        message: `Subject ${subject} not found in ${level} syllabus`,
        success: false,
      });
    }

    const classData = subjectData[className];
    if (!classData) {
      return res.status(404).send({
        message: `Class ${className} not found for ${subject} in ${level} level`,
        success: false,
      });
    }

    // Extract topics and subtopics
    const topics = classData.topics || [];
    const formattedTopics = topics.map(topic => ({
      topicName: topic.topicName,
      subtopics: topic.subtopics?.map(sub => sub.name) || [],
      difficulty: topic.difficulty || "medium",
    }));

    res.send({
      message: "Syllabus topics retrieved successfully",
      success: true,
      data: {
        level,
        class: className,
        subject,
        topics: formattedTopics,
      },
    });

  } catch (error) {
    console.error("Get syllabus topics error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Generate unique exam name
router.post("/generate-exam-name", authMiddleware, async (req, res) => {
  try {
    const { level, className, subjects } = req.body;

    if (!level || !className || !subjects || subjects.length === 0) {
      return res.status(400).send({
        message: "Level, class, and subjects are required",
        success: false,
      });
    }

    // Generate very short unique exam name
    const randomId = Math.random().toString(36).substring(2, 4).toUpperCase();

    // Create short subject abbreviation (max 2 chars)
    const subjectAbbr = subjects.length === 1
      ? subjects[0].substring(0, 2).toUpperCase()
      : subjects.length <= 2
        ? subjects.map(s => s.charAt(0)).join("").toUpperCase()
        : "MX"; // Multi-subject

    // Format level abbreviation (1 char)
    const levelAbbr = level.charAt(0).toUpperCase(); // P, S, A

    // Generate very short exam name: P3M-A1 (Level+Class+Subject-RandomID)
    const examName = `${levelAbbr}${className}${subjectAbbr}-${randomId}`;

    // Generate descriptive name for display
    const subjectList = subjects.length <= 2
      ? subjects.join(" & ")
      : subjects.length === 3
        ? subjects.slice(0, 2).join(", ") + " & " + subjects[2]
        : subjects.slice(0, 2).join(", ") + ` & ${subjects.length - 2} more`;

    const descriptiveName = `${level.charAt(0).toUpperCase() + level.slice(1)} Class ${className} - ${subjectList}`;

    res.send({
      message: "Exam name generated successfully",
      success: true,
      data: {
        examName,
        descriptiveName,
        shortCode: examName, // Use the same short name as the code
      },
    });

  } catch (error) {
    console.error("Generate exam name error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

module.exports = router;
