{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\DebugAuth.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Button, message, Space, Typography } from 'antd';\nimport { getAllSyllabuses, getAvailableSubjects } from '../apicalls/syllabus';\nimport { getSubjectsForLevel } from '../apicalls/aiQuestions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Text,\n  Title\n} = Typography;\nconst DebugAuth = () => {\n  _s();\n  const [authInfo, setAuthInfo] = useState({});\n  const [testResults, setTestResults] = useState({});\n  const [loading, setLoading] = useState(false);\n  useEffect(() => {\n    checkAuthInfo();\n  }, []);\n  const checkAuthInfo = () => {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n    let tokenInfo = {};\n    if (token) {\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        tokenInfo = {\n          userId: payload.userId,\n          exp: payload.exp,\n          iat: payload.iat,\n          isExpired: payload.exp < Date.now() / 1000\n        };\n      } catch (e) {\n        tokenInfo = {\n          error: 'Invalid token format'\n        };\n      }\n    }\n    setAuthInfo({\n      hasToken: !!token,\n      hasUser: !!user,\n      token: token ? `${token.substring(0, 20)}...` : null,\n      user: user ? JSON.parse(user) : null,\n      tokenInfo\n    });\n  };\n  const testSyllabusAPI = async () => {\n    setLoading(true);\n    const results = {};\n    try {\n      var _syllabusResponse$dat;\n      console.log('🧪 Testing getAllSyllabuses...');\n      const syllabusResponse = await getAllSyllabuses();\n      results.getAllSyllabuses = {\n        success: syllabusResponse.success,\n        dataLength: ((_syllabusResponse$dat = syllabusResponse.data) === null || _syllabusResponse$dat === void 0 ? void 0 : _syllabusResponse$dat.length) || 0,\n        message: syllabusResponse.message,\n        error: syllabusResponse.success ? null : syllabusResponse.message\n      };\n      console.log('📚 Syllabus response:', syllabusResponse);\n    } catch (error) {\n      var _error$response;\n      results.getAllSyllabuses = {\n        success: false,\n        error: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status\n      };\n      console.error('❌ Syllabus error:', error);\n    }\n    try {\n      var _subjectsResponse$dat;\n      console.log('🧪 Testing getAvailableSubjects...');\n      const subjectsResponse = await getAvailableSubjects('primary');\n      results.getAvailableSubjects = {\n        success: subjectsResponse.success,\n        dataLength: ((_subjectsResponse$dat = subjectsResponse.data) === null || _subjectsResponse$dat === void 0 ? void 0 : _subjectsResponse$dat.length) || 0,\n        data: subjectsResponse.data,\n        message: subjectsResponse.message,\n        error: subjectsResponse.success ? null : subjectsResponse.message\n      };\n      console.log('📖 Subjects response:', subjectsResponse);\n    } catch (error) {\n      var _error$response2;\n      results.getAvailableSubjects = {\n        success: false,\n        error: error.message,\n        status: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status\n      };\n      console.error('❌ Subjects error:', error);\n    }\n    try {\n      var _aiSubjectsResponse$d;\n      console.log('🧪 Testing getSubjectsForLevel (AI)...');\n      const aiSubjectsResponse = await getSubjectsForLevel('primary');\n      results.getSubjectsForLevel = {\n        success: aiSubjectsResponse.success,\n        dataLength: ((_aiSubjectsResponse$d = aiSubjectsResponse.data) === null || _aiSubjectsResponse$d === void 0 ? void 0 : _aiSubjectsResponse$d.length) || 0,\n        data: aiSubjectsResponse.data,\n        message: aiSubjectsResponse.message,\n        error: aiSubjectsResponse.success ? null : aiSubjectsResponse.message\n      };\n      console.log('🤖 AI Subjects response:', aiSubjectsResponse);\n    } catch (error) {\n      var _error$response3;\n      results.getSubjectsForLevel = {\n        success: false,\n        error: error.message,\n        status: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status\n      };\n      console.error('❌ AI Subjects error:', error);\n    }\n    setTestResults(results);\n    setLoading(false);\n  };\n  const clearAuth = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    message.info('Authentication cleared');\n    checkAuthInfo();\n  };\n  const refreshAuth = () => {\n    checkAuthInfo();\n    message.info('Authentication info refreshed');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      maxWidth: '800px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\uD83D\\uDD0D Authentication & API Debug\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\uD83D\\uDD10 Authentication Status\",\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Has Token:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 17\n          }, this), \" \", authInfo.hasToken ? '✅ Yes' : '❌ No']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Has User:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 17\n          }, this), \" \", authInfo.hasUser ? '✅ Yes' : '❌ No']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), authInfo.token && /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Token Preview:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 19\n          }, this), \" \", authInfo.token]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this), authInfo.user && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"User:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 21\n            }, this), \" \", authInfo.user.name, \" (\", authInfo.user.email, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Is Admin:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 21\n            }, this), \" \", authInfo.user.isAdmin ? '✅ Yes' : '❌ No']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this), authInfo.tokenInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Token Info:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: [\"User ID: \", authInfo.tokenInfo.userId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: [\"Expires: \", authInfo.tokenInfo.exp ? new Date(authInfo.tokenInfo.exp * 1000).toLocaleString() : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: [\"Is Expired: \", authInfo.tokenInfo.isExpired ? '❌ Yes' : '✅ No']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), authInfo.tokenInfo.error && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"danger\",\n              children: [\"Error: \", authInfo.tokenInfo.error]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '15px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: refreshAuth,\n            children: \"Refresh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: clearAuth,\n            danger: true,\n            children: \"Clear Auth\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\uD83E\\uDDEA API Test Results\",\n      style: {\n        marginBottom: '20px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: testSyllabusAPI,\n          loading: loading,\n          disabled: !authInfo.hasToken,\n          children: \"Test API Endpoints\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), Object.keys(testResults).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            children: \"Test Results:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), Object.entries(testResults).map(([testName, result]) => /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: testName,\n            style: {\n              marginBottom: '10px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Success:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 25\n              }, this), \" \", result.success ? '✅ Yes' : '❌ No']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this), result.dataLength !== undefined && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Data Length:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 29\n                }, this), \" \", result.dataLength]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), result.data && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Data:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 29\n                }, this), \" \", JSON.stringify(result.data)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), result.message && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Message:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 29\n                }, this), \" \", result.message]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), result.error && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                type: \"danger\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Error:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 43\n                }, this), \" \", result.error]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true), result.status && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 29\n                }, this), \" \", result.status]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true)]\n          }, testName, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '15px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: async () => {\n                try {\n                  const response = await getSubjectsForLevel('primary');\n                  console.log('Quick test - Primary subjects:', response);\n                  message.info(`Primary subjects: ${JSON.stringify(response.data)}`);\n                } catch (error) {\n                  console.error('Quick test error:', error);\n                  message.error(`Error: ${error.message}`);\n                }\n              },\n              children: \"Quick Test: Primary Subjects\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: async () => {\n                try {\n                  const response = await getAvailableSubjects('primary');\n                  console.log('Quick test - Available subjects:', response);\n                  message.info(`Available subjects: ${JSON.stringify(response.data)}`);\n                } catch (error) {\n                  console.error('Quick test error:', error);\n                  message.error(`Error: ${error.message}`);\n                }\n              },\n              children: \"Quick Test: Syllabus Subjects\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\uD83D\\uDCCB Debug Instructions\",\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: \"1. Check if you have a valid authentication token\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: \"2. Verify the token is not expired\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: \"3. Test API endpoints to see specific error messages\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: \"4. Check browser console for detailed error logs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: \"5. If token is invalid, try logging out and logging back in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s(DebugAuth, \"zZvMp5mjMSkrTLt2s8dnTaY4kz8=\");\n_c = DebugAuth;\nexport default DebugAuth;\nvar _c;\n$RefreshReg$(_c, \"DebugAuth\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON>", "message", "Space", "Typography", "getAllSyllabuses", "getAvailableSubjects", "getSubjectsForLevel", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Text", "Title", "DebugAuth", "_s", "authInfo", "setAuthInfo", "testResults", "setTestResults", "loading", "setLoading", "checkAuthInfo", "token", "localStorage", "getItem", "user", "tokenInfo", "payload", "JSON", "parse", "atob", "split", "userId", "exp", "iat", "isExpired", "Date", "now", "e", "error", "hasToken", "<PERSON><PERSON>ser", "substring", "testSyllabusAPI", "results", "_syllabusResponse$dat", "console", "log", "syllabusResponse", "success", "dataLength", "data", "length", "_error$response", "status", "response", "_subjectsResponse$dat", "subjectsResponse", "_error$response2", "_aiSubjectsResponse$d", "aiSubjectsResponse", "_error$response3", "clearAuth", "removeItem", "info", "refreshAuth", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "marginBottom", "direction", "width", "name", "email", "isAdmin", "toLocaleString", "type", "marginTop", "onClick", "danger", "disabled", "Object", "keys", "entries", "map", "testName", "result", "size", "undefined", "stringify", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/DebugAuth.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Button, message, Space, Typography } from 'antd';\nimport { getAllSyllabuses, getAvailableSubjects } from '../apicalls/syllabus';\nimport { getSubjectsForLevel } from '../apicalls/aiQuestions';\n\nconst { Text, Title } = Typography;\n\nconst DebugAuth = () => {\n  const [authInfo, setAuthInfo] = useState({});\n  const [testResults, setTestResults] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    checkAuthInfo();\n  }, []);\n\n  const checkAuthInfo = () => {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n    \n    let tokenInfo = {};\n    if (token) {\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        tokenInfo = {\n          userId: payload.userId,\n          exp: payload.exp,\n          iat: payload.iat,\n          isExpired: payload.exp < Date.now() / 1000\n        };\n      } catch (e) {\n        tokenInfo = { error: 'Invalid token format' };\n      }\n    }\n\n    setAuthInfo({\n      hasToken: !!token,\n      hasUser: !!user,\n      token: token ? `${token.substring(0, 20)}...` : null,\n      user: user ? JSON.parse(user) : null,\n      tokenInfo\n    });\n  };\n\n  const testSyllabusAPI = async () => {\n    setLoading(true);\n    const results = {};\n\n    try {\n      console.log('🧪 Testing getAllSyllabuses...');\n      const syllabusResponse = await getAllSyllabuses();\n      results.getAllSyllabuses = {\n        success: syllabusResponse.success,\n        dataLength: syllabusResponse.data?.length || 0,\n        message: syllabusResponse.message,\n        error: syllabusResponse.success ? null : syllabusResponse.message\n      };\n      console.log('📚 Syllabus response:', syllabusResponse);\n    } catch (error) {\n      results.getAllSyllabuses = {\n        success: false,\n        error: error.message,\n        status: error.response?.status\n      };\n      console.error('❌ Syllabus error:', error);\n    }\n\n    try {\n      console.log('🧪 Testing getAvailableSubjects...');\n      const subjectsResponse = await getAvailableSubjects('primary');\n      results.getAvailableSubjects = {\n        success: subjectsResponse.success,\n        dataLength: subjectsResponse.data?.length || 0,\n        data: subjectsResponse.data,\n        message: subjectsResponse.message,\n        error: subjectsResponse.success ? null : subjectsResponse.message\n      };\n      console.log('📖 Subjects response:', subjectsResponse);\n    } catch (error) {\n      results.getAvailableSubjects = {\n        success: false,\n        error: error.message,\n        status: error.response?.status\n      };\n      console.error('❌ Subjects error:', error);\n    }\n\n    try {\n      console.log('🧪 Testing getSubjectsForLevel (AI)...');\n      const aiSubjectsResponse = await getSubjectsForLevel('primary');\n      results.getSubjectsForLevel = {\n        success: aiSubjectsResponse.success,\n        dataLength: aiSubjectsResponse.data?.length || 0,\n        data: aiSubjectsResponse.data,\n        message: aiSubjectsResponse.message,\n        error: aiSubjectsResponse.success ? null : aiSubjectsResponse.message\n      };\n      console.log('🤖 AI Subjects response:', aiSubjectsResponse);\n    } catch (error) {\n      results.getSubjectsForLevel = {\n        success: false,\n        error: error.message,\n        status: error.response?.status\n      };\n      console.error('❌ AI Subjects error:', error);\n    }\n\n    setTestResults(results);\n    setLoading(false);\n  };\n\n  const clearAuth = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    message.info('Authentication cleared');\n    checkAuthInfo();\n  };\n\n  const refreshAuth = () => {\n    checkAuthInfo();\n    message.info('Authentication info refreshed');\n  };\n\n  return (\n    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>\n      <Title level={2}>🔍 Authentication & API Debug</Title>\n      \n      {/* Authentication Info */}\n      <Card title=\"🔐 Authentication Status\" style={{ marginBottom: '20px' }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Text><strong>Has Token:</strong> {authInfo.hasToken ? '✅ Yes' : '❌ No'}</Text>\n          <Text><strong>Has User:</strong> {authInfo.hasUser ? '✅ Yes' : '❌ No'}</Text>\n          \n          {authInfo.token && (\n            <Text><strong>Token Preview:</strong> {authInfo.token}</Text>\n          )}\n          \n          {authInfo.user && (\n            <div>\n              <Text><strong>User:</strong> {authInfo.user.name} ({authInfo.user.email})</Text>\n              <br />\n              <Text><strong>Is Admin:</strong> {authInfo.user.isAdmin ? '✅ Yes' : '❌ No'}</Text>\n            </div>\n          )}\n          \n          {authInfo.tokenInfo && (\n            <div>\n              <Text><strong>Token Info:</strong></Text>\n              <br />\n              <Text>User ID: {authInfo.tokenInfo.userId}</Text>\n              <br />\n              <Text>Expires: {authInfo.tokenInfo.exp ? new Date(authInfo.tokenInfo.exp * 1000).toLocaleString() : 'N/A'}</Text>\n              <br />\n              <Text>Is Expired: {authInfo.tokenInfo.isExpired ? '❌ Yes' : '✅ No'}</Text>\n              {authInfo.tokenInfo.error && (\n                <>\n                  <br />\n                  <Text type=\"danger\">Error: {authInfo.tokenInfo.error}</Text>\n                </>\n              )}\n            </div>\n          )}\n        </Space>\n        \n        <div style={{ marginTop: '15px' }}>\n          <Space>\n            <Button onClick={refreshAuth}>Refresh</Button>\n            <Button onClick={clearAuth} danger>Clear Auth</Button>\n          </Space>\n        </div>\n      </Card>\n\n      {/* API Test Results */}\n      <Card title=\"🧪 API Test Results\" style={{ marginBottom: '20px' }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Button \n            type=\"primary\" \n            onClick={testSyllabusAPI} \n            loading={loading}\n            disabled={!authInfo.hasToken}\n          >\n            Test API Endpoints\n          </Button>\n          \n          {Object.keys(testResults).length > 0 && (\n            <div>\n              <Title level={4}>Test Results:</Title>\n\n              {Object.entries(testResults).map(([testName, result]) => (\n                <Card\n                  key={testName}\n                  size=\"small\"\n                  title={testName}\n                  style={{ marginBottom: '10px' }}\n                >\n                  <Text><strong>Success:</strong> {result.success ? '✅ Yes' : '❌ No'}</Text>\n                  <br />\n                  {result.dataLength !== undefined && (\n                    <>\n                      <Text><strong>Data Length:</strong> {result.dataLength}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.data && (\n                    <>\n                      <Text><strong>Data:</strong> {JSON.stringify(result.data)}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.message && (\n                    <>\n                      <Text><strong>Message:</strong> {result.message}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.error && (\n                    <>\n                      <Text type=\"danger\"><strong>Error:</strong> {result.error}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.status && (\n                    <>\n                      <Text><strong>Status:</strong> {result.status}</Text>\n                      <br />\n                    </>\n                  )}\n                </Card>\n              ))}\n            </div>\n          )}\n\n          {/* Quick Test Buttons */}\n          <div style={{ marginTop: '15px' }}>\n            <Space>\n              <Button\n                onClick={async () => {\n                  try {\n                    const response = await getSubjectsForLevel('primary');\n                    console.log('Quick test - Primary subjects:', response);\n                    message.info(`Primary subjects: ${JSON.stringify(response.data)}`);\n                  } catch (error) {\n                    console.error('Quick test error:', error);\n                    message.error(`Error: ${error.message}`);\n                  }\n                }}\n              >\n                Quick Test: Primary Subjects\n              </Button>\n\n              <Button\n                onClick={async () => {\n                  try {\n                    const response = await getAvailableSubjects('primary');\n                    console.log('Quick test - Available subjects:', response);\n                    message.info(`Available subjects: ${JSON.stringify(response.data)}`);\n                  } catch (error) {\n                    console.error('Quick test error:', error);\n                    message.error(`Error: ${error.message}`);\n                  }\n                }}\n              >\n                Quick Test: Syllabus Subjects\n              </Button>\n            </Space>\n          </div>\n        </Space>\n      </Card>\n\n      {/* Instructions */}\n      <Card title=\"📋 Debug Instructions\">\n        <Space direction=\"vertical\">\n          <Text>1. Check if you have a valid authentication token</Text>\n          <Text>2. Verify the token is not expired</Text>\n          <Text>3. Test API endpoints to see specific error messages</Text>\n          <Text>4. Check browser console for detailed error logs</Text>\n          <Text>5. If token is invalid, try logging out and logging back in</Text>\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default DebugAuth;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,UAAU,QAAQ,MAAM;AAC/D,SAASC,gBAAgB,EAAEC,oBAAoB,QAAQ,sBAAsB;AAC7E,SAASC,mBAAmB,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE9D,MAAM;EAAEC,IAAI;EAAEC;AAAM,CAAC,GAAGT,UAAU;AAElC,MAAMU,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACduB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAEzC,IAAIE,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIJ,KAAK,EAAE;MACT,IAAI;QACF,MAAMK,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACR,KAAK,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrDL,SAAS,GAAG;UACVM,MAAM,EAAEL,OAAO,CAACK,MAAM;UACtBC,GAAG,EAAEN,OAAO,CAACM,GAAG;UAChBC,GAAG,EAAEP,OAAO,CAACO,GAAG;UAChBC,SAAS,EAAER,OAAO,CAACM,GAAG,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG;QACxC,CAAC;MACH,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVZ,SAAS,GAAG;UAAEa,KAAK,EAAE;QAAuB,CAAC;MAC/C;IACF;IAEAvB,WAAW,CAAC;MACVwB,QAAQ,EAAE,CAAC,CAAClB,KAAK;MACjBmB,OAAO,EAAE,CAAC,CAAChB,IAAI;MACfH,KAAK,EAAEA,KAAK,GAAI,GAAEA,KAAK,CAACoB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAE,KAAI,GAAG,IAAI;MACpDjB,IAAI,EAAEA,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,GAAG,IAAI;MACpCC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCvB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMwB,OAAO,GAAG,CAAC,CAAC;IAElB,IAAI;MAAA,IAAAC,qBAAA;MACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,MAAMC,gBAAgB,GAAG,MAAM5C,gBAAgB,CAAC,CAAC;MACjDwC,OAAO,CAACxC,gBAAgB,GAAG;QACzB6C,OAAO,EAAED,gBAAgB,CAACC,OAAO;QACjCC,UAAU,EAAE,EAAAL,qBAAA,GAAAG,gBAAgB,CAACG,IAAI,cAAAN,qBAAA,uBAArBA,qBAAA,CAAuBO,MAAM,KAAI,CAAC;QAC9CnD,OAAO,EAAE+C,gBAAgB,CAAC/C,OAAO;QACjCsC,KAAK,EAAES,gBAAgB,CAACC,OAAO,GAAG,IAAI,GAAGD,gBAAgB,CAAC/C;MAC5D,CAAC;MACD6C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,gBAAgB,CAAC;IACxD,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAc,eAAA;MACdT,OAAO,CAACxC,gBAAgB,GAAG;QACzB6C,OAAO,EAAE,KAAK;QACdV,KAAK,EAAEA,KAAK,CAACtC,OAAO;QACpBqD,MAAM,GAAAD,eAAA,GAAEd,KAAK,CAACgB,QAAQ,cAAAF,eAAA,uBAAdA,eAAA,CAAgBC;MAC1B,CAAC;MACDR,OAAO,CAACP,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C;IAEA,IAAI;MAAA,IAAAiB,qBAAA;MACFV,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,MAAMU,gBAAgB,GAAG,MAAMpD,oBAAoB,CAAC,SAAS,CAAC;MAC9DuC,OAAO,CAACvC,oBAAoB,GAAG;QAC7B4C,OAAO,EAAEQ,gBAAgB,CAACR,OAAO;QACjCC,UAAU,EAAE,EAAAM,qBAAA,GAAAC,gBAAgB,CAACN,IAAI,cAAAK,qBAAA,uBAArBA,qBAAA,CAAuBJ,MAAM,KAAI,CAAC;QAC9CD,IAAI,EAAEM,gBAAgB,CAACN,IAAI;QAC3BlD,OAAO,EAAEwD,gBAAgB,CAACxD,OAAO;QACjCsC,KAAK,EAAEkB,gBAAgB,CAACR,OAAO,GAAG,IAAI,GAAGQ,gBAAgB,CAACxD;MAC5D,CAAC;MACD6C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEU,gBAAgB,CAAC;IACxD,CAAC,CAAC,OAAOlB,KAAK,EAAE;MAAA,IAAAmB,gBAAA;MACdd,OAAO,CAACvC,oBAAoB,GAAG;QAC7B4C,OAAO,EAAE,KAAK;QACdV,KAAK,EAAEA,KAAK,CAACtC,OAAO;QACpBqD,MAAM,GAAAI,gBAAA,GAAEnB,KAAK,CAACgB,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBJ;MAC1B,CAAC;MACDR,OAAO,CAACP,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C;IAEA,IAAI;MAAA,IAAAoB,qBAAA;MACFb,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMa,kBAAkB,GAAG,MAAMtD,mBAAmB,CAAC,SAAS,CAAC;MAC/DsC,OAAO,CAACtC,mBAAmB,GAAG;QAC5B2C,OAAO,EAAEW,kBAAkB,CAACX,OAAO;QACnCC,UAAU,EAAE,EAAAS,qBAAA,GAAAC,kBAAkB,CAACT,IAAI,cAAAQ,qBAAA,uBAAvBA,qBAAA,CAAyBP,MAAM,KAAI,CAAC;QAChDD,IAAI,EAAES,kBAAkB,CAACT,IAAI;QAC7BlD,OAAO,EAAE2D,kBAAkB,CAAC3D,OAAO;QACnCsC,KAAK,EAAEqB,kBAAkB,CAACX,OAAO,GAAG,IAAI,GAAGW,kBAAkB,CAAC3D;MAChE,CAAC;MACD6C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEa,kBAAkB,CAAC;IAC7D,CAAC,CAAC,OAAOrB,KAAK,EAAE;MAAA,IAAAsB,gBAAA;MACdjB,OAAO,CAACtC,mBAAmB,GAAG;QAC5B2C,OAAO,EAAE,KAAK;QACdV,KAAK,EAAEA,KAAK,CAACtC,OAAO;QACpBqD,MAAM,GAAAO,gBAAA,GAAEtB,KAAK,CAACgB,QAAQ,cAAAM,gBAAA,uBAAdA,gBAAA,CAAgBP;MAC1B,CAAC;MACDR,OAAO,CAACP,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;IAEArB,cAAc,CAAC0B,OAAO,CAAC;IACvBxB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM0C,SAAS,GAAGA,CAAA,KAAM;IACtBvC,YAAY,CAACwC,UAAU,CAAC,OAAO,CAAC;IAChCxC,YAAY,CAACwC,UAAU,CAAC,MAAM,CAAC;IAC/B9D,OAAO,CAAC+D,IAAI,CAAC,wBAAwB,CAAC;IACtC3C,aAAa,CAAC,CAAC;EACjB,CAAC;EAED,MAAM4C,WAAW,GAAGA,CAAA,KAAM;IACxB5C,aAAa,CAAC,CAAC;IACfpB,OAAO,CAAC+D,IAAI,CAAC,+BAA+B,CAAC;EAC/C,CAAC;EAED,oBACExD,OAAA;IAAK0D,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnE9D,OAAA,CAACI,KAAK;MAAC2D,KAAK,EAAE,CAAE;MAAAD,QAAA,EAAC;IAA6B;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAGtDnE,OAAA,CAACT,IAAI;MAAC6E,KAAK,EAAC,oCAA0B;MAACV,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,gBACrE9D,OAAA,CAACN,KAAK;QAAC4E,SAAS,EAAC,UAAU;QAACZ,KAAK,EAAE;UAAEa,KAAK,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACnD9D,OAAA,CAACG,IAAI;UAAA2D,QAAA,gBAAC9D,OAAA;YAAA8D,QAAA,EAAQ;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,QAAQ,CAACyB,QAAQ,GAAG,OAAO,GAAG,MAAM;QAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/EnE,OAAA,CAACG,IAAI;UAAA2D,QAAA,gBAAC9D,OAAA;YAAA8D,QAAA,EAAQ;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,QAAQ,CAAC0B,OAAO,GAAG,OAAO,GAAG,MAAM;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAE5E5D,QAAQ,CAACO,KAAK,iBACbd,OAAA,CAACG,IAAI;UAAA2D,QAAA,gBAAC9D,OAAA;YAAA8D,QAAA,EAAQ;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,QAAQ,CAACO,KAAK;QAAA;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC7D,EAEA5D,QAAQ,CAACU,IAAI,iBACZjB,OAAA;UAAA8D,QAAA,gBACE9D,OAAA,CAACG,IAAI;YAAA2D,QAAA,gBAAC9D,OAAA;cAAA8D,QAAA,EAAQ;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5D,QAAQ,CAACU,IAAI,CAACuD,IAAI,EAAC,IAAE,EAACjE,QAAQ,CAACU,IAAI,CAACwD,KAAK,EAAC,GAAC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChFnE,OAAA;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnE,OAAA,CAACG,IAAI;YAAA2D,QAAA,gBAAC9D,OAAA;cAAA8D,QAAA,EAAQ;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC5D,QAAQ,CAACU,IAAI,CAACyD,OAAO,GAAG,OAAO,GAAG,MAAM;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CACN,EAEA5D,QAAQ,CAACW,SAAS,iBACjBlB,OAAA;UAAA8D,QAAA,gBACE9D,OAAA,CAACG,IAAI;YAAA2D,QAAA,eAAC9D,OAAA;cAAA8D,QAAA,EAAQ;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCnE,OAAA;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnE,OAAA,CAACG,IAAI;YAAA2D,QAAA,GAAC,WAAS,EAACvD,QAAQ,CAACW,SAAS,CAACM,MAAM;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjDnE,OAAA;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnE,OAAA,CAACG,IAAI;YAAA2D,QAAA,GAAC,WAAS,EAACvD,QAAQ,CAACW,SAAS,CAACO,GAAG,GAAG,IAAIG,IAAI,CAACrB,QAAQ,CAACW,SAAS,CAACO,GAAG,GAAG,IAAI,CAAC,CAACkD,cAAc,CAAC,CAAC,GAAG,KAAK;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjHnE,OAAA;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnE,OAAA,CAACG,IAAI;YAAA2D,QAAA,GAAC,cAAY,EAACvD,QAAQ,CAACW,SAAS,CAACS,SAAS,GAAG,OAAO,GAAG,MAAM;UAAA;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACzE5D,QAAQ,CAACW,SAAS,CAACa,KAAK,iBACvB/B,OAAA,CAAAE,SAAA;YAAA4D,QAAA,gBACE9D,OAAA;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNnE,OAAA,CAACG,IAAI;cAACyE,IAAI,EAAC,QAAQ;cAAAd,QAAA,GAAC,SAAO,EAACvD,QAAQ,CAACW,SAAS,CAACa,KAAK;YAAA;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,eAC5D,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAERnE,OAAA;QAAK0D,KAAK,EAAE;UAAEmB,SAAS,EAAE;QAAO,CAAE;QAAAf,QAAA,eAChC9D,OAAA,CAACN,KAAK;UAAAoE,QAAA,gBACJ9D,OAAA,CAACR,MAAM;YAACsF,OAAO,EAAErB,WAAY;YAAAK,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9CnE,OAAA,CAACR,MAAM;YAACsF,OAAO,EAAExB,SAAU;YAACyB,MAAM;YAAAjB,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPnE,OAAA,CAACT,IAAI;MAAC6E,KAAK,EAAC,+BAAqB;MAACV,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,eAChE9D,OAAA,CAACN,KAAK;QAAC4E,SAAS,EAAC,UAAU;QAACZ,KAAK,EAAE;UAAEa,KAAK,EAAE;QAAO,CAAE;QAAAT,QAAA,gBACnD9D,OAAA,CAACR,MAAM;UACLoF,IAAI,EAAC,SAAS;UACdE,OAAO,EAAE3C,eAAgB;UACzBxB,OAAO,EAAEA,OAAQ;UACjBqE,QAAQ,EAAE,CAACzE,QAAQ,CAACyB,QAAS;UAAA8B,QAAA,EAC9B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERc,MAAM,CAACC,IAAI,CAACzE,WAAW,CAAC,CAACmC,MAAM,GAAG,CAAC,iBAClC5C,OAAA;UAAA8D,QAAA,gBACE9D,OAAA,CAACI,KAAK;YAAC2D,KAAK,EAAE,CAAE;YAAAD,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAErCc,MAAM,CAACE,OAAO,CAAC1E,WAAW,CAAC,CAAC2E,GAAG,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,kBAClDtF,OAAA,CAACT,IAAI;YAEHgG,IAAI,EAAC,OAAO;YACZnB,KAAK,EAAEiB,QAAS;YAChB3B,KAAK,EAAE;cAAEW,YAAY,EAAE;YAAO,CAAE;YAAAP,QAAA,gBAEhC9D,OAAA,CAACG,IAAI;cAAA2D,QAAA,gBAAC9D,OAAA;gBAAA8D,QAAA,EAAQ;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACmB,MAAM,CAAC7C,OAAO,GAAG,OAAO,GAAG,MAAM;YAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1EnE,OAAA;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACLmB,MAAM,CAAC5C,UAAU,KAAK8C,SAAS,iBAC9BxF,OAAA,CAAAE,SAAA;cAAA4D,QAAA,gBACE9D,OAAA,CAACG,IAAI;gBAAA2D,QAAA,gBAAC9D,OAAA;kBAAA8D,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACmB,MAAM,CAAC5C,UAAU;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9DnE,OAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EACAmB,MAAM,CAAC3C,IAAI,iBACV3C,OAAA,CAAAE,SAAA;cAAA4D,QAAA,gBACE9D,OAAA,CAACG,IAAI;gBAAA2D,QAAA,gBAAC9D,OAAA;kBAAA8D,QAAA,EAAQ;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC/C,IAAI,CAACqE,SAAS,CAACH,MAAM,CAAC3C,IAAI,CAAC;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjEnE,OAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EACAmB,MAAM,CAAC7F,OAAO,iBACbO,OAAA,CAAAE,SAAA;cAAA4D,QAAA,gBACE9D,OAAA,CAACG,IAAI;gBAAA2D,QAAA,gBAAC9D,OAAA;kBAAA8D,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACmB,MAAM,CAAC7F,OAAO;cAAA;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvDnE,OAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EACAmB,MAAM,CAACvD,KAAK,iBACX/B,OAAA,CAAAE,SAAA;cAAA4D,QAAA,gBACE9D,OAAA,CAACG,IAAI;gBAACyE,IAAI,EAAC,QAAQ;gBAAAd,QAAA,gBAAC9D,OAAA;kBAAA8D,QAAA,EAAQ;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACmB,MAAM,CAACvD,KAAK;cAAA;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjEnE,OAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH,EACAmB,MAAM,CAACxC,MAAM,iBACZ9C,OAAA,CAAAE,SAAA;cAAA4D,QAAA,gBACE9D,OAAA,CAACG,IAAI;gBAAA2D,QAAA,gBAAC9D,OAAA;kBAAA8D,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACmB,MAAM,CAACxC,MAAM;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDnE,OAAA;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,eACN,CACH;UAAA,GApCIkB,QAAQ;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqCT,CACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDnE,OAAA;UAAK0D,KAAK,EAAE;YAAEmB,SAAS,EAAE;UAAO,CAAE;UAAAf,QAAA,eAChC9D,OAAA,CAACN,KAAK;YAAAoE,QAAA,gBACJ9D,OAAA,CAACR,MAAM;cACLsF,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACnB,IAAI;kBACF,MAAM/B,QAAQ,GAAG,MAAMjD,mBAAmB,CAAC,SAAS,CAAC;kBACrDwC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEQ,QAAQ,CAAC;kBACvDtD,OAAO,CAAC+D,IAAI,CAAE,qBAAoBpC,IAAI,CAACqE,SAAS,CAAC1C,QAAQ,CAACJ,IAAI,CAAE,EAAC,CAAC;gBACpE,CAAC,CAAC,OAAOZ,KAAK,EAAE;kBACdO,OAAO,CAACP,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;kBACzCtC,OAAO,CAACsC,KAAK,CAAE,UAASA,KAAK,CAACtC,OAAQ,EAAC,CAAC;gBAC1C;cACF,CAAE;cAAAqE,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETnE,OAAA,CAACR,MAAM;cACLsF,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACnB,IAAI;kBACF,MAAM/B,QAAQ,GAAG,MAAMlD,oBAAoB,CAAC,SAAS,CAAC;kBACtDyC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEQ,QAAQ,CAAC;kBACzDtD,OAAO,CAAC+D,IAAI,CAAE,uBAAsBpC,IAAI,CAACqE,SAAS,CAAC1C,QAAQ,CAACJ,IAAI,CAAE,EAAC,CAAC;gBACtE,CAAC,CAAC,OAAOZ,KAAK,EAAE;kBACdO,OAAO,CAACP,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;kBACzCtC,OAAO,CAACsC,KAAK,CAAE,UAASA,KAAK,CAACtC,OAAQ,EAAC,CAAC;gBAC1C;cACF,CAAE;cAAAqE,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPnE,OAAA,CAACT,IAAI;MAAC6E,KAAK,EAAC,iCAAuB;MAAAN,QAAA,eACjC9D,OAAA,CAACN,KAAK;QAAC4E,SAAS,EAAC,UAAU;QAAAR,QAAA,gBACzB9D,OAAA,CAACG,IAAI;UAAA2D,QAAA,EAAC;QAAiD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9DnE,OAAA,CAACG,IAAI;UAAA2D,QAAA,EAAC;QAAkC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/CnE,OAAA,CAACG,IAAI;UAAA2D,QAAA,EAAC;QAAoD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjEnE,OAAA,CAACG,IAAI;UAAA2D,QAAA,EAAC;QAAgD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7DnE,OAAA,CAACG,IAAI;UAAA2D,QAAA,EAAC;QAA2D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7D,EAAA,CAlRID,SAAS;AAAAqF,EAAA,GAATrF,SAAS;AAoRf,eAAeA,SAAS;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}