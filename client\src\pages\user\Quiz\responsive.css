/* ===== RESPONSIVE QUIZ SYSTEM ===== */

/* ===== BASE QUIZ CONTAINER ===== */
.quiz-container {
    height: 100vh;
    background: #ffffff;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
    position: relative;
    overflow: hidden;
    scroll-behavior: smooth;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

/* Enhanced responsive quiz container */
.quiz-responsive {
    padding: 0;
    margin: 0;
    width: 100vw;
    max-width: 100%;
}

/* Touch-friendly interactions */
.touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Enhanced button styles for mobile */
button.touch-manipulation {
    min-height: 44px;
    min-width: 44px;
}

/* ===== ENHANCED MOBILE-FIRST RESPONSIVE BREAKPOINTS ===== */

/* Mobile Small (320px+) - Default styles above */

/* Mobile Large (375px+) */
@media (min-width: 375px) {
    .quiz-container {
        font-size: 14px;
    }
}

/* Tablet Small (640px+) */
@media (min-width: 640px) {
    .quiz-container {
        font-size: 15px;
    }

    .quiz-option {
        min-height: 60px;
    }
}

/* Tablet Large (768px+) */
@media (min-width: 768px) {
    .quiz-container {
        font-size: 16px;
    }

    .quiz-option {
        min-height: 70px;
    }

    .quiz-question-container {
        max-width: 900px;
        margin: 0 auto;
    }
}

/* Desktop Small (1024px+) */
@media (min-width: 1024px) {
    .quiz-container {
        font-size: 17px;
    }

    .quiz-option {
        min-height: 80px;
    }

    .quiz-question-container {
        max-width: 1000px;
        margin: 0 auto;
    }
}

/* Desktop Large (1280px+) */
@media (min-width: 1280px) {
    .quiz-container {
        font-size: 18px;
    }

    .quiz-option {
        min-height: 90px;
    }

    .quiz-question-container {
        max-width: 1200px;
        margin: 0 auto;
    }
}

/* Ultra Wide (1536px+) */
@media (min-width: 1536px) {
    .quiz-container {
        font-size: 19px;
    }

    .quiz-option {
        min-height: 100px;
    }

    .quiz-question-container {
        max-width: 1400px;
        margin: 0 auto;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    .quiz-container,
    .quiz-option,
    .quiz-nav-btn,
    .quiz-result-btn {
        transition: none;
        animation: none;
    }
}

/* ===== FOCUS MANAGEMENT ===== */
.quiz-option:focus-visible,
.quiz-nav-btn:focus-visible,
.quiz-result-btn:focus-visible {
    outline: 2px solid #007BFF;
    outline-offset: 2px;
}

/* ===== ENHANCED QUIZ NAVIGATION RESPONSIVE STYLES ===== */

/* Enhanced navigation button styles */
.quiz-nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Mobile navigation adjustments */
@media (max-width: 639px) {
    .quiz-nav-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        min-width: 80px;
    }
}

/* Tablet navigation adjustments */
@media (min-width: 640px) and (max-width: 1023px) {
    .quiz-nav-btn {
        padding: 0.75rem 1.25rem;
        min-width: 120px;
    }
}

/* Desktop navigation adjustments */
@media (min-width: 1024px) {
    .quiz-nav-btn {
        padding: 1rem 2rem;
        min-width: 140px;
    }
}

/* ===== QUIZ PROGRESS BAR ===== */
.quiz-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #e5e7eb;
    z-index: 1000;
}

.quiz-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007BFF 0%, #0056D2 100%);
    transition: width 0.3s ease;
}

/* ===== QUIZ HEADER ===== */
.quiz-progress-container {
    padding: 1rem 1.5rem;
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 100;
}

.quiz-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

/* Improved header layout with centered timer */
.quiz-header-content-improved {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 1rem;
}

.quiz-timer-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.quiz-question-counter-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.quiz-title-section h1 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
    line-height: 1.4;
}

.quiz-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
}

/* ===== QUIZ TIMER ===== */
.quiz-timer {
    background: #f3f4f6;
    color: #374151;
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
    min-width: 80px;
    text-align: center;
}

.quiz-timer.warning {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fca5a5;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* ===== QUIZ QUESTION COUNTER ===== */
.quiz-question-counter {
    text-align: center;
    font-size: 0.875rem;
    font-weight: 600;
    color: #007BFF;
    background: rgba(0, 123, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    display: inline-block;
    margin: 0 auto;
}

/* ===== QUIZ CONTENT AREA ===== */
.quiz-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0.75rem;
    width: 100%;
    box-sizing: border-box;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

/* ===== QUIZ OPTIONS STYLING ===== */
.quiz-option {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.quiz-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.quiz-option:active {
    transform: translateY(0);
}

.quiz-option-letter {
    transition: all 0.3s ease;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quiz-option-text {
    line-height: 1.6;
    word-wrap: break-word;
    hyphens: auto;
}

/* ===== QUIZ RESULT CONTAINER ===== */
.quiz-result-container {
    overflow-y: auto;
    scroll-behavior: smooth;
    position: relative;
    min-height: 100vh;
    padding-bottom: 2rem;
}

.quiz-result-content {
    padding-bottom: 2rem;
    margin-bottom: 2rem;
}

/* ===== QUIZ REVIEW CONTAINER ===== */
.quiz-review-container {
    overflow-y: auto;
    scroll-behavior: smooth;
    position: relative;
    min-height: 100vh;
    padding-bottom: 2rem;
}

.quiz-review-question {
    margin-bottom: 1rem;
}

/* ===== QUIZ START CONTAINER ===== */
.quiz-start-container {
    overflow-y: auto;
    scroll-behavior: smooth;
    position: relative;
    min-height: 100vh;
}

.quiz-start-card {
    margin-bottom: 2rem;
}

/* ===== QUIZ LISTING CONTAINER ===== */
.quiz-listing-content {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
}

.quiz-grid-container {
    margin-bottom: 2rem;
}

/* ===== SCROLL FIXES FOR ALL QUIZ PAGES ===== */
.quiz-container,
.quiz-result-container,
.quiz-review-container,
.quiz-start-container {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
}

/* Webkit scrollbar styling */
.quiz-container::-webkit-scrollbar,
.quiz-result-container::-webkit-scrollbar,
.quiz-review-container::-webkit-scrollbar,
.quiz-start-container::-webkit-scrollbar {
    width: 6px;
}

.quiz-container::-webkit-scrollbar-track,
.quiz-result-container::-webkit-scrollbar-track,
.quiz-review-container::-webkit-scrollbar-track,
.quiz-start-container::-webkit-scrollbar-track {
    background: #f7fafc;
}

.quiz-container::-webkit-scrollbar-thumb,
.quiz-result-container::-webkit-scrollbar-thumb,
.quiz-review-container::-webkit-scrollbar-thumb,
.quiz-start-container::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.quiz-container::-webkit-scrollbar-thumb:hover,
.quiz-result-container::-webkit-scrollbar-thumb:hover,
.quiz-review-container::-webkit-scrollbar-thumb:hover,
.quiz-start-container::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* ===== QUIZ RESULT ANIMATIONS ===== */

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes glow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
    }
    50% {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.6);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== ENHANCED CREATIVE ANIMATIONS ===== */

@keyframes megaBounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0) scale(1);
    }
    40%, 43% {
        transform: translate3d(0, -40px, 0) scale(1.2);
    }
    70% {
        transform: translate3d(0, -20px, 0) scale(1.1);
    }
    90% {
        transform: translate3d(0, -8px, 0) scale(1.05);
    }
}

@keyframes bigBounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0) scale(1);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0) scale(1.15);
    }
    70% {
        transform: translate3d(0, -15px, 0) scale(1.08);
    }
    90% {
        transform: translate3d(0, -5px, 0) scale(1.03);
    }
}

@keyframes gentlePulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.08);
    }
}

@keyframes encourageShake {
    0%, 100% {
        transform: translateX(0) scale(1);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-8px) scale(1.02);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(8px) scale(1.02);
    }
}

@keyframes rainbowGlow {
    0% {
        box-shadow: 0 0 30px rgba(255, 0, 0, 0.8), 0 0 60px rgba(255, 0, 0, 0.4);
    }
    16.66% {
        box-shadow: 0 0 30px rgba(255, 165, 0, 0.8), 0 0 60px rgba(255, 165, 0, 0.4);
    }
    33.33% {
        box-shadow: 0 0 30px rgba(255, 255, 0, 0.8), 0 0 60px rgba(255, 255, 0, 0.4);
    }
    50% {
        box-shadow: 0 0 30px rgba(0, 255, 0, 0.8), 0 0 60px rgba(0, 255, 0, 0.4);
    }
    66.66% {
        box-shadow: 0 0 30px rgba(0, 0, 255, 0.8), 0 0 60px rgba(0, 0, 255, 0.4);
    }
    83.33% {
        box-shadow: 0 0 30px rgba(75, 0, 130, 0.8), 0 0 60px rgba(75, 0, 130, 0.4);
    }
    100% {
        box-shadow: 0 0 30px rgba(238, 130, 238, 0.8), 0 0 60px rgba(238, 130, 238, 0.4);
    }
}

@keyframes excellentGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.6), 0 0 40px rgba(16, 185, 129, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(16, 185, 129, 0.9), 0 0 80px rgba(16, 185, 129, 0.5);
    }
}

@keyframes passGlow {
    0%, 100% {
        box-shadow: 0 0 15px rgba(59, 130, 246, 0.6), 0 0 30px rgba(59, 130, 246, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(59, 130, 246, 0.8), 0 0 60px rgba(59, 130, 246, 0.4);
    }
}

@keyframes failGlow {
    0%, 100% {
        box-shadow: 0 0 15px rgba(239, 68, 68, 0.5), 0 0 30px rgba(239, 68, 68, 0.2);
    }
    50% {
        box-shadow: 0 0 25px rgba(239, 68, 68, 0.7), 0 0 50px rgba(239, 68, 68, 0.3);
    }
}

@keyframes rotate360 {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

@keyframes twinkle {
    0% {
        opacity: 0.3;
        transform: scale(0.5);
    }
    100% {
        opacity: 1;
        transform: scale(1.5);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* ===== TITLE ANIMATIONS ===== */

@keyframes titleBounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0) scale(1);
    }
    40%, 43% {
        transform: translateY(-15px) scale(1.05);
    }
    70% {
        transform: translateY(-8px) scale(1.02);
    }
    90% {
        transform: translateY(-3px) scale(1.01);
    }
}

@keyframes titlePulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.03);
    }
}

@keyframes titleFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes titleShake {
    0%, 100% {
        transform: translateX(0) scale(1);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px) scale(1.01);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px) scale(1.01);
    }
}

@keyframes rainbowText {
    0% {
        filter: hue-rotate(0deg) brightness(1.2);
    }
    25% {
        filter: hue-rotate(90deg) brightness(1.3);
    }
    50% {
        filter: hue-rotate(180deg) brightness(1.4);
    }
    75% {
        filter: hue-rotate(270deg) brightness(1.3);
    }
    100% {
        filter: hue-rotate(360deg) brightness(1.2);
    }
}

@keyframes excellentText {
    0%, 100% {
        filter: brightness(1) saturate(1);
    }
    50% {
        filter: brightness(1.2) saturate(1.3);
    }
}

@keyframes passText {
    0%, 100% {
        filter: brightness(1) saturate(1);
    }
    50% {
        filter: brightness(1.1) saturate(1.2);
    }
}

@keyframes failText {
    0%, 100% {
        filter: brightness(1) saturate(1);
    }
    50% {
        filter: brightness(1.05) saturate(1.1);
    }
}

/* ===== QUESTION SUMMARY ENHANCEMENTS ===== */

.question-summary-correct {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 50%, #a7f3d0 100%);
    border: 2px solid #16a34a;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.2), 0 0 0 1px rgba(34, 197, 94, 0.1);
}

.question-summary-wrong {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 50%, #fca5a5 100%);
    border: 2px solid #dc2626;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.2), 0 0 0 1px rgba(220, 38, 38, 0.1);
}

.question-summary-correct:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.2);
}

.question-summary-wrong:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3), 0 0 0 1px rgba(220, 38, 38, 0.2);
}

.answer-display-correct {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    border: 2px solid #16a34a;
    color: #15803d;
    font-weight: 600;
}

.answer-display-wrong {
    background: linear-gradient(135deg, #fef2f2, #fecaca);
    border: 2px solid #dc2626;
    color: #b91c1c;
    font-weight: 600;
}

.answer-display-reference {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    border: 2px solid #16a34a;
    color: #15803d;
    font-weight: 700;
}

/* ===== ENHANCED LEARNING SUMMARY STYLES ===== */

.learning-summary-correct {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 50%, #a7f3d0 100%);
    border: 2px solid #16a34a;
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.15), 0 0 0 1px rgba(34, 197, 94, 0.1);
    position: relative;
    overflow: hidden;
}

.learning-summary-correct::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981, #34d399, #6ee7b7);
    animation: shimmer 2s infinite;
}

.learning-summary-wrong {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 50%, #fca5a5 100%);
    border: 2px solid #dc2626;
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15), 0 0 0 1px rgba(220, 38, 38, 0.1);
    position: relative;
    overflow: hidden;
}

.learning-summary-wrong::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #dc2626, #ef4444, #f87171);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0.6;
    }
}

.learning-summary-correct:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 35px rgba(34, 197, 94, 0.25), 0 0 0 1px rgba(34, 197, 94, 0.2);
}

.learning-summary-wrong:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 35px rgba(220, 38, 38, 0.25), 0 0 0 1px rgba(220, 38, 38, 0.2);
}

.answer-badge-correct {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.answer-badge-wrong {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.explanation-button {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.explanation-button:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    transform: translateY(-2px) scale(1.05);
}

.encouragement-correct {
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    border: 2px solid #10b981;
    animation: gentleGlow 3s infinite;
}

@keyframes gentleGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(16, 185, 129, 0.2);
    }
    50% {
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);
    }
}

.question-text-highlight {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: 2px solid #cbd5e1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.question-text-highlight:hover {
    border-color: #94a3b8;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* ===== IMPROVED TEXT CONTRAST ===== */

.learning-summary-text {
    color: #1f2937 !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.learning-summary-question {
    background: #f8fafc !important;
    border: 2px solid #cbd5e1 !important;
    color: #111827 !important;
}

.learning-summary-answer-text {
    color: #111827 !important;
    font-weight: 700 !important;
}

.learning-summary-feedback {
    font-weight: 700 !important;
}

/* Enhanced contrast for different states */
.answer-correct-text {
    color: #14532d !important;
    background: #f0fdf4 !important;
}

.answer-wrong-text {
    color: #7f1d1d !important;
    background: #fef2f2 !important;
}

/* ===== ENHANCED QUESTION SUMMARY VISIBILITY ===== */

.question-summary-question {
    background: #ffffff !important;
    color: #111827 !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    line-height: 1.7 !important;
    border: 3px solid !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.question-summary-answer {
    background: #ffffff !important;
    color: #111827 !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    border: 3px solid !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.ai-explanation-text {
    color: #111827 !important;
    font-weight: 600 !important;
    line-height: 1.7 !important;
    background: #ffffff !important;
    padding: 1rem !important;
    border-radius: 0.5rem !important;
}

/* Force black text for all explanations */
.ai-explanation-text * {
    color: #111827 !important;
}

/* Enhanced borders for better visibility */
.summary-correct-border {
    border-color: #22c55e !important;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.2) !important;
}

.summary-wrong-border {
    border-color: #ef4444 !important;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2) !important;
}

.summary-explanation-border {
    border-color: #3b82f6 !important;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2) !important;
}

/* ===== SIMPLIFIED SUMMARY DESIGN ===== */

.question-number-badge {
    font-size: 1.25rem !important;
    font-weight: 800 !important;
    min-width: 3.5rem !important;
    min-height: 3.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

@media (min-width: 640px) {
    .question-number-badge {
        font-size: 1.5rem !important;
        min-width: 4rem !important;
        min-height: 4rem !important;
    }
}

.simplified-question-container {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.simplified-question-container:hover {
    transform: translateY(-2px);
}

@keyframes confetti {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

/* Performance-based result styling */
.quiz-result-perfect {
    background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
}

.quiz-result-excellent {
    background: linear-gradient(135deg, #10b981, #059669, #047857);
}

.quiz-result-pass {
    background: linear-gradient(135deg, #3b82f6, #2563eb, #1d4ed8);
}

.quiz-result-fail {
    background: linear-gradient(135deg, #ef4444, #dc2626, #b91c1c);
}

/* Enhanced text visibility */
.quiz-result-text {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5);
    color: white !important;
}

.quiz-result-subtitle {
    background: rgba(0,0,0,0.3);
    backdrop-filter: blur(10px);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
}

/* Review section improvements */
.quiz-review-answer-correct {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    border: 2px solid #16a34a;
    color: #15803d;
}

.quiz-review-answer-wrong {
    background: linear-gradient(135deg, #fef2f2, #fecaca);
    border: 2px solid #dc2626;
    color: #b91c1c;
}

.quiz-review-answer-correct-ref {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    border: 2px solid #16a34a;
    color: #15803d;
    font-weight: 600;
}

/* ===== TABLET SPECIFIC IMPROVEMENTS ===== */
@media (min-width: 768px) and (max-width: 1023px) {
    .quiz-option {
        padding: 1.25rem 1.5rem;
        font-size: 1.1rem;
        min-height: 75px;
    }

    .quiz-option-letter {
        width: 3rem;
        height: 3rem;
        font-size: 1.1rem;
    }

    .quiz-question-container {
        padding: 2.5rem;
        margin-bottom: 2rem;
    }
}

/* ===== LAPTOP SPECIFIC IMPROVEMENTS ===== */
@media (min-width: 1024px) {
    .quiz-option {
        padding: 1.5rem 2rem;
        font-size: 1.2rem;
        min-height: 85px;
        border-radius: 1rem;
    }

    .quiz-option:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    }

    .quiz-option-letter {
        width: 3.5rem;
        height: 3.5rem;
        font-size: 1.2rem;
    }

    .quiz-question-container {
        padding: 3rem;
        margin-bottom: 2.5rem;
        border-radius: 1.5rem;
    }

    .quiz-fill-input {
        padding: 1.5rem 2rem;
        font-size: 1.2rem;
        border-radius: 1rem;
    }
}

/* ===== DESKTOP LARGE IMPROVEMENTS ===== */
@media (min-width: 1280px) {
    .quiz-option {
        padding: 1.75rem 2.5rem;
        font-size: 1.3rem;
        min-height: 95px;
    }

    .quiz-option-letter {
        width: 4rem;
        height: 4rem;
        font-size: 1.3rem;
    }

    .quiz-question-container {
        padding: 3.5rem;
        margin-bottom: 3rem;
    }
}

/* Enhanced responsive quiz content */
.quiz-content-responsive {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
    word-wrap: break-word;
}

/* Responsive quiz content */
@media (min-width: 640px) {
    .quiz-content {
        padding: 1rem;
        min-height: calc(100vh - 190px);
    }
}

@media (min-width: 768px) {
    .quiz-content {
        padding: 1.25rem;
        min-height: calc(100vh - 200px);
    }
}

@media (min-width: 1024px) {
    .quiz-content {
        padding: 1.5rem;
        min-height: calc(100vh - 210px);
    }
}

.quiz-question-container {
    background: #ffffff;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    width: 100%;
    box-sizing: border-box;
    max-width: 100%;
    overflow-wrap: break-word;
}

/* Responsive question container */
@media (min-width: 640px) {
    .quiz-question-container {
        padding: 1.25rem;
        border-radius: 0.875rem;
    }
}

@media (min-width: 768px) {
    .quiz-question-container {
        padding: 1.5rem;
        margin-bottom: 1.75rem;
        border-radius: 1rem;
    }
}

@media (min-width: 1024px) {
    .quiz-question-container {
        padding: 2rem;
        margin-bottom: 2rem;
    }
}

.quiz-question-number {
    font-size: 0.875rem;
    font-weight: 600;
    color: #007BFF;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.quiz-question-text {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Responsive question text */
@media (min-width: 640px) {
    .quiz-question-text {
        font-size: 1.125rem;
        margin-bottom: 1.75rem;
    }
}

@media (min-width: 768px) {
    .quiz-question-text {
        font-size: 1.25rem;
        margin-bottom: 2rem;
    }
}

@media (min-width: 1024px) {
    .quiz-question-text {
        font-size: 1.375rem;
    }
}

/* ===== QUIZ OPTIONS ===== */
.quiz-options {
    display: grid;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    width: 100%;
    box-sizing: border-box;
}

/* Responsive quiz options */
@media (min-width: 640px) {
    .quiz-options {
        gap: 0.875rem;
        margin-bottom: 1.75rem;
    }
}

@media (min-width: 768px) {
    .quiz-options {
        gap: 1rem;
        margin-bottom: 2rem;
    }
}

@media (min-width: 1024px) {
    .quiz-options {
        gap: 1.25rem;
    }
}

.quiz-option {
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    position: relative;
    display: flex;
    align-items: center;
    min-height: 48px;
    width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Responsive quiz option */
@media (min-width: 640px) {
    .quiz-option {
        padding: 0.875rem 1.125rem;
        font-size: 0.9375rem;
        min-height: 52px;
        border-radius: 0.625rem;
    }
}

@media (min-width: 768px) {
    .quiz-option {
        padding: 1rem 1.25rem;
        font-size: 1rem;
        min-height: 56px;
        border-radius: 0.75rem;
    }
}

@media (min-width: 1024px) {
    .quiz-option {
        padding: 1.125rem 1.375rem;
        font-size: 1.0625rem;
        min-height: 60px;
    }
}

.quiz-option:hover {
    border-color: #007BFF;
    background: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
}

.quiz-option.selected {
    border-color: #007BFF;
    background: #007BFF;
    color: #ffffff;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

/* Ensure text readability in image questions */
.quiz-option-text {
    flex: 1;
    line-height: 1.5;
    color: inherit;
}

/* For image questions, ensure text remains readable */
.quiz-option.selected .quiz-option-text {
    color: #ffffff;
    font-weight: 600;
}

/* Override for better readability in all cases */
.quiz-option {
    color: #374151;
}

.quiz-option.selected {
    color: #ffffff;
}

.quiz-option-letter {
    background: #e5e7eb;
    color: #374151;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    margin-right: 1rem;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.quiz-option.selected .quiz-option-letter {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

.quiz-option-text {
    flex: 1;
    line-height: 1.5;
}

/* ===== FILL IN THE BLANK ===== */
.quiz-fill-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 1rem;
    background: #f9fafb;
    color: #374151;
    transition: all 0.2s ease;
    font-family: inherit;
}

.quiz-fill-input:focus {
    outline: none;
    border-color: #007BFF;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* ===== QUIZ NAVIGATION ===== */
.quiz-navigation {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 1200px;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-top: 1px solid #e5e7eb;
    border-radius: 1rem 1rem 0 0;
    padding: 1.25rem 1.5rem;
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    z-index: 100;
    box-shadow: 0 -8px 25px rgba(0, 0, 0, 0.1);
}

.quiz-nav-btn {
    padding: 0.875rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    font-family: inherit;
}

.quiz-nav-btn.primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: #ffffff;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

.quiz-nav-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
}

.quiz-nav-btn.secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.quiz-nav-btn.secondary:hover {
    background: #e5e7eb;
    color: #374151;
}

.quiz-nav-btn.disabled,
.quiz-nav-btn:disabled {
    background: #e5e7eb !important;
    color: #9ca3af !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
    transform: none !important;
    box-shadow: none !important;
    border: 1px solid #d1d5db !important;
}

.quiz-nav-btn.disabled:hover,
.quiz-nav-btn:disabled:hover {
    background: #e5e7eb !important;
    color: #9ca3af !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== QUIZ IMAGE DISPLAY ===== */
.quiz-image-container {
    text-align: center;
    margin: 1.5rem 0;
}

.quiz-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

/* ===== MODERN QUIZ IMAGE STYLES ===== */
.quiz-image-container-modern {
    display: flex;
    justify-content: center;
    margin: 1.5rem 0;
    padding: 0 1rem;
}

.quiz-image-wrapper {
    background: white;
    border-radius: 1rem;
    padding: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
    max-width: 100%;
    position: relative;
    overflow: hidden;
}

.quiz-image-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007BFF 0%, #0056D2 100%);
    z-index: 1;
}

.quiz-image-wrapper:hover {
    transform: translateY(-4px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
    border-color: #007BFF;
}

.quiz-image-modern {
    width: 100%;
    height: auto;
    max-width: 100%;
    max-height: 500px;
    object-fit: contain;
    border-radius: 0.5rem;
    transition: transform 0.3s ease;
}

.quiz-image-modern:hover {
    transform: scale(1.02);
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
    .quiz-container {
        padding-bottom: 90px;
        width: 100vw;
        max-width: 100%;
        overflow-x: hidden;
    }

    .quiz-content {
        padding: 0.5rem;
        margin: 0;
        width: 100%;
        max-width: 100%;
    }

    .quiz-progress-container {
        padding: 0.75rem 1rem;
    }

    .quiz-header-content {
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .quiz-header-content-improved {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        gap: 0.75rem;
        text-align: center;
        padding: 0 0.5rem;
    }

    .quiz-timer-center {
        order: 1;
    }

    .quiz-title-section {
        order: 2;
    }

    .quiz-question-counter-right {
        order: 3;
        justify-content: center;
    }

    .quiz-title-section {
        text-align: center;
        width: 100%;
    }

    .quiz-title-section h1 {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .quiz-subtitle {
        font-size: 0.8rem;
    }

    .quiz-timer {
        padding: 0.625rem 0.875rem;
        font-size: 0.875rem;
        min-width: 70px;
    }

    .quiz-question-counter {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }

    .quiz-content {
        padding: 1rem;
    }

    .quiz-question-container {
        padding: 1.25rem;
        margin-bottom: 1.5rem;
        border-radius: 0.75rem;
    }

    .quiz-question-text {
        font-size: 1.125rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
    }

    .quiz-options {
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .quiz-option {
        padding: 1rem;
        min-height: 60px; /* Better touch target */
        font-size: 0.9375rem;
        border-radius: 0.75rem;
        line-height: 1.4;
        transition: all 0.2s ease;
    }

    .quiz-option:active {
        transform: scale(0.98);
    }

    .quiz-option.selected {
        border-width: 3px; /* More prominent selection */
    }

    .quiz-option-letter {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
        margin-right: 0.75rem;
        flex-shrink: 0;
    }

    .quiz-option-text {
        word-break: break-word;
        hyphens: auto;
    }

    .quiz-fill-input {
        padding: 0.875rem;
        font-size: 1rem;
    }

    .quiz-navigation {
        padding: 1rem;
        gap: 0.75rem;
        flex-direction: row;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 1rem 1rem 0 0;
        box-shadow: 0 -8px 25px rgba(0, 0, 0, 0.15);
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        transform: none;
        max-width: none;
        width: 100%;
        z-index: 1000;
    }

    .quiz-nav-btn {
        padding: 1rem 1.5rem;
        font-size: 0.875rem;
        min-width: 0;
        flex: 1;
        border-radius: 0.75rem;
        min-height: 48px; /* Touch-friendly */
        font-weight: 600;
    }

    .quiz-nav-btn:active {
        transform: scale(0.98);
    }

    .quiz-image {
        max-height: 250px;
    }

    /* Modern image styles for mobile */
    .quiz-image-container-modern {
        margin: 1rem 0;
        padding: 0 0.5rem;
    }

    .quiz-image-wrapper {
        padding: 0.75rem;
        border-radius: 0.75rem;
    }

    .quiz-image-modern {
        max-height: 250px;
        border-radius: 0.375rem;
    }

    /* Hide left navigation during quiz */
    .layout-sidebar {
        display: none !important;
    }

    .layout-content {
        margin-left: 0 !important;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .quiz-progress-container {
        padding: 1rem 1.25rem;
    }

    .quiz-header-content {
        margin-bottom: 1rem;
    }

    .quiz-header-content-improved {
        grid-template-columns: 1fr auto 1fr;
        gap: 1rem;
        padding: 0 1rem;
    }

    .quiz-timer-center {
        order: 0;
    }

    .quiz-title-section {
        order: 0;
    }

    .quiz-question-counter-right {
        order: 0;
        justify-content: flex-end;
    }

    .quiz-title-section h1 {
        font-size: 1.125rem;
    }

    .quiz-timer {
        padding: 0.75rem 1rem;
        font-size: 0.9375rem;
    }

    .quiz-content {
        padding: 1.25rem;
    }

    .quiz-question-container {
        padding: 1.75rem;
        margin-bottom: 1.75rem;
    }

    .quiz-question-text {
        font-size: 1.1875rem;
        margin-bottom: 1.75rem;
    }

    .quiz-options {
        gap: 0.875rem;
        margin-bottom: 1.75rem;
    }

    .quiz-option {
        padding: 1rem 1.125rem;
        min-height: 58px;
        font-size: 0.9375rem;
    }

    .quiz-option-letter {
        width: 30px;
        height: 30px;
        margin-right: 0.875rem;
    }

    .quiz-navigation {
        padding: 1rem 1.25rem;
        gap: 0.875rem;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        border-radius: 1rem 1rem 0 0;
        box-shadow: 0 -8px 25px rgba(0, 0, 0, 0.15);
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
    }

    .quiz-nav-btn {
        padding: 0.9375rem 1.75rem;
        font-size: 0.9375rem;
        min-width: 140px;
        border-radius: 0.75rem;
    }

    /* Quiz grid for tablets */
    .quiz-grid-container {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.25rem;
    }

    .quiz-image {
        max-height: 320px;
    }

    /* Modern image styles for tablet */
    .quiz-image-container-modern {
        margin: 1.25rem 0;
        padding: 0 0.75rem;
    }

    .quiz-image-wrapper {
        padding: 0.875rem;
        border-radius: 0.875rem;
    }

    .quiz-image-modern {
        max-height: 350px;
        border-radius: 0.5rem;
    }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .quiz-content {
        padding: 1.5rem;
    }

    .quiz-question-container {
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .quiz-navigation {
        padding: 1rem 1.5rem;
    }

    .quiz-nav-btn {
        min-width: 140px;
    }
}

/* Large Desktop (1025px+) */
@media (min-width: 1025px) {
    .quiz-content {
        padding: 2rem;
    }

    .quiz-question-container {
        padding: 2.5rem;
        margin-bottom: 2.5rem;
    }

    .quiz-question-text {
        font-size: 1.375rem;
        margin-bottom: 2.5rem;
    }

    .quiz-options {
        gap: 1.25rem;
        margin-bottom: 2.5rem;
    }

    .quiz-option {
        padding: 1.25rem 1.5rem;
        min-height: 64px;
        font-size: 1.0625rem;
    }

    .quiz-option-letter {
        width: 36px;
        height: 36px;
        font-size: 0.9375rem;
        margin-right: 1.25rem;
    }

    .quiz-nav-btn {
        min-width: 160px;
        padding: 1rem 2.5rem;
    }

    /* Modern image styles for large desktop */
    .quiz-image-container-modern {
        margin: 2rem 0;
        padding: 0 1.5rem;
    }

    .quiz-image-wrapper {
        padding: 1.5rem;
        border-radius: 1.25rem;
        max-width: 800px;
        margin: 0 auto;
    }

    .quiz-image-modern {
        max-height: 600px;
        border-radius: 0.75rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .quiz-progress-container {
        padding: 0.5rem 1rem;
    }

    .quiz-header-content {
        margin-bottom: 0.5rem;
    }

    .quiz-title-section h1 {
        font-size: 1rem;
        margin-bottom: 0.125rem;
    }

    .quiz-subtitle {
        font-size: 0.75rem;
    }

    .quiz-question-counter {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .quiz-content {
        padding: 0.75rem;
    }

    .quiz-question-container {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .quiz-question-text {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .quiz-options {
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .quiz-option {
        padding: 0.75rem;
        min-height: 48px;
        font-size: 0.875rem;
    }

    .quiz-option-letter {
        width: 24px;
        height: 24px;
        font-size: 0.75rem;
        margin-right: 0.5rem;
    }

    .quiz-navigation {
        padding: 0.5rem 1rem;
        gap: 0.5rem;
    }

    .quiz-nav-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        min-width: 100px;
    }

    .quiz-image {
        max-height: 200px;
    }

    /* Modern image styles for landscape mobile */
    .quiz-image-container-modern {
        margin: 0.75rem 0;
        padding: 0 0.5rem;
    }

    .quiz-image-wrapper {
        padding: 0.5rem;
        border-radius: 0.5rem;
    }

    .quiz-image-modern {
        max-height: 180px;
        border-radius: 0.25rem;
    }
}

/* ===== QUIZ LISTING PAGE STYLES ===== */

.quiz-listing-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f8fafc 100%);
    padding: 1.5rem;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
    position: relative;
    overflow-x: hidden;
}

.quiz-listing-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 123, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 86, 210, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 212, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.quiz-listing-content {
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

/* Quiz Header */
.quiz-listing-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 3rem 2rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 2rem;
    box-shadow:
        0 20px 25px -5px rgba(0, 123, 255, 0.1),
        0 10px 10px -5px rgba(0, 123, 255, 0.04),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 123, 255, 0.08);
    position: relative;
    overflow: hidden;
}

.quiz-listing-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007BFF 0%, #0056D2 50%, #00D4FF 100%);
    z-index: 1;
}

.quiz-listing-title {
    font-size: 2.75rem;
    font-weight: 900;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 50%, #00D4FF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
    text-shadow: 0 0 30px rgba(0, 123, 255, 0.3);
    position: relative;
}

.quiz-listing-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #007BFF 0%, #00D4FF 100%);
    border-radius: 2px;
}

.quiz-listing-subtitle {
    font-size: 1.375rem;
    color: #64748b;
    margin: 0;
    line-height: 1.6;
    font-weight: 500;
    opacity: 0.9;
}

/* Search and Filter Section */
.quiz-search-section {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 1.5rem;
    padding: 2rem;
    margin-bottom: 2.5rem;
    box-shadow:
        0 10px 25px -5px rgba(0, 123, 255, 0.1),
        0 8px 10px -6px rgba(0, 123, 255, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 123, 255, 0.08);
    position: relative;
}

.quiz-search-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007BFF 0%, #0056D2 50%, #00D4FF 100%);
    border-radius: 1.5rem 1.5rem 0 0;
}

.quiz-search-grid {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1.5rem;
    align-items: center;
}

.quiz-search-input {
    width: 100%;
    padding: 1rem 1.25rem 1rem 3rem;
    border: 2px solid rgba(0, 123, 255, 0.1);
    border-radius: 1rem;
    font-size: 1rem;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    color: #374151;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.05);
}

.quiz-search-input:focus {
    outline: none;
    border-color: #007BFF;
    background: #ffffff;
    box-shadow:
        0 0 0 4px rgba(0, 123, 255, 0.1),
        0 8px 25px -8px rgba(0, 123, 255, 0.2);
    transform: translateY(-2px);
}

.quiz-filter-select {
    min-width: 220px;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(0, 123, 255, 0.1);
    border-radius: 1rem;
    font-size: 1rem;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    color: #374151;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.05);
    cursor: pointer;
}

.quiz-filter-select:focus {
    outline: none;
    border-color: #007BFF;
    background: #ffffff;
    box-shadow:
        0 0 0 4px rgba(0, 123, 255, 0.1),
        0 8px 25px -8px rgba(0, 123, 255, 0.2);
    transform: translateY(-2px);
}

.quiz-filter-select:hover {
    border-color: rgba(0, 123, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.1);
}

/* Quiz Grid - Clean Responsive Layout */
.quiz-grid-container {
    display: grid;
    gap: 1.5rem;
    margin: 0 auto 2rem;
    max-width: 1400px;
    padding: 0 1rem;
    /* Mobile: 1 card per row */
    grid-template-columns: 1fr;
}

/* Small Mobile: 2 cards per row */
@media (min-width: 480px) {
    .quiz-grid-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

/* Tablet: 3 cards per row */
@media (min-width: 768px) {
    .quiz-grid-container {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}

/* Desktop: 4 cards per row */
@media (min-width: 1024px) {
    .quiz-grid-container {
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }
}

/* Mobile: 1 column */
@media (max-width: 640px) {
    .quiz-grid-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 0.5rem;
    }

    .quiz-listing-container {
        padding: 1rem 0.5rem;
    }

    .quiz-listing-header {
        padding: 2rem 1rem;
        margin-bottom: 1.5rem;
    }

    .quiz-listing-title {
        font-size: 2rem;
    }

    .quiz-search-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .quiz-search-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .quiz-filter-select {
        min-width: 100%;
    }
}

/* Tablet: Enhanced responsiveness */
@media (min-width: 641px) and (max-width: 1024px) {
    .quiz-listing-container {
        padding: 1.25rem;
    }

    .quiz-listing-header {
        padding: 2.5rem 1.5rem;
    }

    .quiz-search-section {
        padding: 1.75rem;
    }

    .quiz-search-grid {
        grid-template-columns: 1fr auto;
        gap: 1.25rem;
    }
}

/* Desktop: Optimized layout */
@media (min-width: 1025px) {
    .quiz-listing-container {
        padding: 1.5rem;
    }

    .quiz-listing-header {
        padding: 3rem 2rem;
    }

    .quiz-search-section {
        padding: 2rem;
    }
}

/* ===== ENHANCED ANIMATIONS ===== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Apply animations to elements */
.quiz-listing-header {
    animation: fadeInUp 0.8s ease-out;
}

.quiz-search-section {
    animation: slideInFromLeft 0.6s ease-out 0.2s both;
}

.quiz-grid-container {
    animation: fadeInUp 0.6s ease-out 0.4s both;
}

/* Staggered card animations */
.quiz-card-modern:nth-child(1) { animation: fadeInUp 0.6s ease-out 0.1s both; }
.quiz-card-modern:nth-child(2) { animation: fadeInUp 0.6s ease-out 0.2s both; }
.quiz-card-modern:nth-child(3) { animation: fadeInUp 0.6s ease-out 0.3s both; }
.quiz-card-modern:nth-child(4) { animation: fadeInUp 0.6s ease-out 0.4s both; }
.quiz-card-modern:nth-child(5) { animation: fadeInUp 0.6s ease-out 0.5s both; }
.quiz-card-modern:nth-child(6) { animation: fadeInUp 0.6s ease-out 0.6s both; }
.quiz-card-modern:nth-child(7) { animation: fadeInUp 0.6s ease-out 0.7s both; }
.quiz-card-modern:nth-child(8) { animation: fadeInUp 0.6s ease-out 0.8s both; }

/* Mobile: 1 column */
@media (max-width: 640px) {
    .quiz-grid-container {
        grid-template-columns: repeat(1, 1fr);
        gap: 1rem;
    }
}

/* Small tablets: 2 columns */
@media (min-width: 641px) and (max-width: 767px) {
    .quiz-grid-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.25rem;
    }
}

/* Tablets: 3 columns */
@media (min-width: 768px) and (max-width: 1023px) {
    .quiz-grid-container {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}

/* Laptops and Desktop: 4 columns */
@media (min-width: 1024px) {
    .quiz-grid-container {
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
        max-width: 1400px;
        margin: 0 auto 2rem;
    }
}

.quiz-card-wrapper {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Enhanced Quiz Card Hover Effects */
.quiz-card-modern {
    height: 100%;
}

.quiz-card-modern .quiz-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.quiz-card-wrapper:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: #007BFF;
}

/* Stats Cards */
.quiz-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.quiz-stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.quiz-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.quiz-stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: #007BFF;
    margin-bottom: 0.5rem;
}

.quiz-stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== QUIZ LISTING RESPONSIVE BREAKPOINTS ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
    .quiz-listing-container {
        padding: 1rem;
    }

    .quiz-listing-header {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: 1rem;
    }

    .quiz-listing-title {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }

    .quiz-listing-subtitle {
        font-size: 1rem;
    }

    .quiz-search-section {
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-radius: 0.75rem;
    }

    .quiz-search-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .quiz-search-input {
        padding: 1rem;
        font-size: 1rem;
    }

    .quiz-filter-select {
        min-width: 100%;
        padding: 1rem;
        font-size: 1rem;
    }

    .quiz-grid-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .quiz-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .quiz-stat-card {
        padding: 1rem;
    }

    .quiz-stat-number {
        font-size: 1.5rem;
    }

    .quiz-stat-label {
        font-size: 0.75rem;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .quiz-listing-container {
        padding: 1.25rem;
    }

    .quiz-listing-header {
        padding: 1.75rem;
    }

    .quiz-listing-title {
        font-size: 2rem;
    }

    .quiz-listing-subtitle {
        font-size: 1.125rem;
    }

    .quiz-search-grid {
        grid-template-columns: 1fr auto;
        gap: 1rem;
    }

    .quiz-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.25rem;
    }

    .quiz-stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .quiz-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 1.5rem;
    }

    .quiz-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Large Desktop (1025px+) */
@media (min-width: 1025px) {
    .quiz-listing-container {
        padding: 2rem;
    }

    .quiz-listing-header {
        padding: 2.5rem;
    }

    .quiz-listing-title {
        font-size: 3rem;
    }

    .quiz-listing-subtitle {
        font-size: 1.375rem;
    }

    .quiz-search-section {
        padding: 2rem;
    }

    .quiz-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 2rem;
    }

    .quiz-stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }

    .quiz-stat-card {
        padding: 2rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .quiz-listing-header {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .quiz-listing-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .quiz-listing-subtitle {
        font-size: 0.875rem;
    }

    .quiz-search-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .quiz-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .quiz-stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .quiz-stat-card {
        padding: 0.75rem;
    }

    .quiz-stat-number {
        font-size: 1.25rem;
    }

    .quiz-stat-label {
        font-size: 0.7rem;
    }
}

/* ===== QUIZ START PAGE STYLES ===== */

.quiz-start-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
}

.quiz-start-card {
    width: 100%;
    max-width: 600px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.quiz-start-header {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.quiz-start-title {
    font-size: 1.875rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.quiz-start-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.quiz-start-content {
    padding: 2rem;
}

.quiz-start-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.quiz-info-item {
    background: #f8fafc;
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.quiz-info-icon {
    font-size: 1.5rem;
    color: #007BFF;
    margin-bottom: 0.5rem;
}

.quiz-info-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.quiz-info-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.quiz-start-instructions {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 2rem;
}

.quiz-start-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.quiz-start-btn {
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    min-width: 140px;
}

.quiz-start-btn.primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

.quiz-start-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
}

.quiz-start-btn.secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.quiz-start-btn.secondary:hover {
    background: #e5e7eb;
    color: #374151;
}

/* ===== QUIZ RESULT PAGE STYLES ===== */

.quiz-result-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
}

.quiz-result-content {
    max-width: 1000px;
    margin: 0 auto;
}

.quiz-result-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    margin-bottom: 2rem;
}

.quiz-result-header {
    padding: 3rem 2rem;
    text-align: center;
}

.quiz-result-header.pass {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.quiz-result-header.fail {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.quiz-result-gif {
    width: 120px;
    height: 120px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    object-fit: cover;
}

.quiz-result-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.quiz-result-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin: 0;
}

.quiz-result-stats {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.quiz-result-stat {
    text-align: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
}

.quiz-result-stat-value {
    font-size: 2rem;
    font-weight: 800;
    color: #007BFF;
    margin-bottom: 0.5rem;
}

.quiz-result-stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.quiz-result-progress {
    margin: 2rem 0;
    text-align: center;
}

.quiz-result-progress-bar {
    width: 100%;
    height: 1.5rem;
    background: #e5e7eb;
    border-radius: 0.75rem;
    overflow: hidden;
    margin: 1rem 0;
}

.quiz-result-progress-fill {
    height: 100%;
    border-radius: 0.75rem;
    transition: width 1s ease;
}

.quiz-result-progress-fill.pass {
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.quiz-result-progress-fill.fail {
    background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.quiz-result-percentage {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    margin-top: 0.75rem;
}

.quiz-result-actions {
    padding: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    border-top: 1px solid #e5e7eb;
}

.quiz-result-btn {
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    min-width: 140px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.quiz-result-btn.primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

.quiz-result-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
    text-decoration: none;
    color: white;
}

.quiz-result-btn.secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.quiz-result-btn.secondary:hover {
    background: #e5e7eb;
    color: #374151;
    text-decoration: none;
}

/* Question Review Section */
.quiz-review-section {
    margin-top: 2rem;
}

.quiz-review-question {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.quiz-review-question-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.quiz-review-question-header.correct {
    background: #f0fdf4;
    border-bottom-color: #bbf7d0;
}

.quiz-review-question-header.incorrect {
    background: #fef2f2;
    border-bottom-color: #fecaca;
}

.quiz-review-question-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.quiz-review-question-status {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.quiz-review-question-status.correct {
    background: #dcfce7;
    color: #166534;
}

.quiz-review-question-status.incorrect {
    background: #fee2e2;
    color: #991b1b;
}

.quiz-review-question-content {
    padding: 1.5rem;
}

.quiz-review-question-text {
    font-size: 1rem;
    color: #374151;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.quiz-review-answers {
    display: grid;
    gap: 0.75rem;
}

.quiz-review-answer {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.quiz-review-answer.correct {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.quiz-review-answer.incorrect {
    background: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.quiz-review-answer.user-selected {
    font-weight: 600;
}

.quiz-review-answer-icon {
    font-size: 1rem;
    font-weight: 700;
}

.quiz-review-answer-text {
    flex: 1;
}

.quiz-explanation-btn {
    margin-top: 1rem;
    padding: 0.75rem 1.5rem;
    background: #007BFF;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quiz-explanation-btn:hover {
    background: #0056D2;
    transform: translateY(-1px);
}

/* ===== QUIZ START & RESULT RESPONSIVE BREAKPOINTS ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
    .quiz-start-container {
        padding: 1rem;
        align-items: flex-start;
        padding-top: 2rem;
    }

    .quiz-start-card {
        max-width: 100%;
        border-radius: 1rem;
    }

    .quiz-start-header {
        padding: 1.5rem;
    }

    .quiz-start-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .quiz-start-subtitle {
        font-size: 0.875rem;
    }

    .quiz-start-content {
        padding: 1.5rem;
    }

    .quiz-start-info {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .quiz-info-item {
        padding: 0.875rem;
    }

    .quiz-info-icon {
        font-size: 1.25rem;
    }

    .quiz-info-value {
        font-size: 1rem;
    }

    .quiz-info-label {
        font-size: 0.8rem;
    }

    .quiz-start-instructions {
        padding: 0.875rem;
        margin-bottom: 1.5rem;
        font-size: 0.875rem;
    }

    .quiz-start-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .quiz-start-btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        min-width: 100%;
    }

    /* Quiz Result Mobile */
    .quiz-result-container {
        padding: 1rem;
    }

    .quiz-result-header {
        padding: 2rem 1.5rem;
    }

    .quiz-result-gif {
        width: 80px;
        height: 80px;
        margin-bottom: 1rem;
    }

    .quiz-result-title {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }

    .quiz-result-subtitle {
        font-size: 1rem;
    }

    .quiz-result-stats {
        padding: 1.5rem;
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .quiz-result-stat {
        padding: 1rem;
    }

    .quiz-result-stat-value {
        font-size: 1.5rem;
    }

    .quiz-result-stat-label {
        font-size: 0.8rem;
    }

    .quiz-result-progress {
        margin: 1.5rem 0;
    }

    .quiz-result-progress-bar {
        height: 1.25rem;
    }

    .quiz-result-percentage {
        font-size: 1.75rem;
    }

    .quiz-result-actions {
        padding: 1.5rem;
        flex-direction: column;
        gap: 0.75rem;
    }

    .quiz-result-btn {
        padding: 1rem 1.5rem;
        min-width: 100%;
    }

    /* Quiz Review Mobile */
    .quiz-review-question {
        margin-bottom: 1rem;
        border-radius: 0.75rem;
    }

    .quiz-review-question-header {
        padding: 1rem;
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .quiz-review-question-title {
        font-size: 1rem;
    }

    .quiz-review-question-status {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    .quiz-review-question-content {
        padding: 1rem;
    }

    .quiz-review-question-text {
        font-size: 0.9375rem;
        margin-bottom: 0.875rem;
    }

    .quiz-review-answers {
        gap: 0.5rem;
    }

    .quiz-review-answer {
        padding: 0.625rem 0.875rem;
        gap: 0.5rem;
    }

    .quiz-review-answer-icon {
        font-size: 0.875rem;
    }

    .quiz-explanation-btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .quiz-start-container {
        padding: 1.25rem;
    }

    .quiz-start-header {
        padding: 1.75rem;
    }

    .quiz-start-title {
        font-size: 1.75rem;
    }

    .quiz-start-content {
        padding: 1.75rem;
    }

    .quiz-start-info {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .quiz-start-actions {
        gap: 0.875rem;
    }

    .quiz-start-btn {
        min-width: 160px;
    }

    /* Quiz Result Tablet */
    .quiz-result-container {
        padding: 1.25rem;
    }

    .quiz-result-header {
        padding: 2.5rem 2rem;
    }

    .quiz-result-gif {
        width: 100px;
        height: 100px;
    }

    .quiz-result-title {
        font-size: 2rem;
    }

    .quiz-result-subtitle {
        font-size: 1.125rem;
    }

    .quiz-result-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.25rem;
    }

    .quiz-result-actions {
        gap: 0.875rem;
    }

    .quiz-result-btn {
        min-width: 160px;
    }

    /* Quiz Review Tablet */
    .quiz-review-question-header {
        padding: 1.25rem;
    }

    .quiz-review-question-content {
        padding: 1.25rem;
    }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .quiz-start-info {
        grid-template-columns: repeat(3, 1fr);
    }

    .quiz-result-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Large Desktop (1025px+) */
@media (min-width: 1025px) {
    .quiz-start-container {
        padding: 2rem;
    }

    .quiz-start-card {
        max-width: 700px;
    }

    .quiz-start-header {
        padding: 2.5rem;
    }

    .quiz-start-title {
        font-size: 2.25rem;
    }

    .quiz-start-subtitle {
        font-size: 1.125rem;
    }

    .quiz-start-content {
        padding: 2.5rem;
    }

    .quiz-start-info {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
        margin-bottom: 2.5rem;
    }

    .quiz-info-item {
        padding: 1.25rem;
    }

    .quiz-start-btn {
        padding: 1.125rem 2.5rem;
        font-size: 1.0625rem;
        min-width: 180px;
    }

    /* Quiz Result Large Desktop */
    .quiz-result-container {
        padding: 2rem;
    }

    .quiz-result-content {
        max-width: 1200px;
    }

    .quiz-result-header {
        padding: 3.5rem 2.5rem;
    }

    .quiz-result-gif {
        width: 140px;
        height: 140px;
        margin-bottom: 2rem;
    }

    .quiz-result-title {
        font-size: 3rem;
        margin-bottom: 1.25rem;
    }

    .quiz-result-subtitle {
        font-size: 1.375rem;
    }

    .quiz-result-stats {
        padding: 2.5rem;
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }

    .quiz-result-stat {
        padding: 2rem;
    }

    .quiz-result-stat-value {
        font-size: 2.5rem;
    }

    .quiz-result-actions {
        padding: 2.5rem;
        gap: 1.5rem;
    }

    .quiz-result-btn {
        padding: 1.125rem 2.5rem;
        font-size: 1.0625rem;
        min-width: 180px;
    }

    /* Quiz Review Large Desktop */
    .quiz-review-question {
        margin-bottom: 2rem;
    }

    .quiz-review-question-header {
        padding: 2rem;
    }

    .quiz-review-question-title {
        font-size: 1.25rem;
    }

    .quiz-review-question-content {
        padding: 2rem;
    }

    .quiz-review-question-text {
        font-size: 1.0625rem;
        margin-bottom: 1.25rem;
    }

    .quiz-review-answers {
        gap: 1rem;
    }

    .quiz-review-answer {
        padding: 1rem 1.25rem;
        gap: 1rem;
    }

    .quiz-explanation-btn {
        padding: 0.875rem 1.75rem;
        font-size: 1rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .quiz-start-container {
        padding: 0.75rem;
        align-items: center;
    }

    .quiz-start-header {
        padding: 1rem;
    }

    .quiz-start-title {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }

    .quiz-start-subtitle {
        font-size: 0.8rem;
    }

    .quiz-start-content {
        padding: 1rem;
    }

    .quiz-start-info {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .quiz-info-item {
        padding: 0.75rem;
    }

    .quiz-info-icon {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .quiz-info-value {
        font-size: 0.875rem;
        margin-bottom: 0.125rem;
    }

    .quiz-info-label {
        font-size: 0.7rem;
    }

    .quiz-start-instructions {
        padding: 0.75rem;
        margin-bottom: 1rem;
        font-size: 0.8rem;
    }

    .quiz-start-actions {
        gap: 0.5rem;
    }

    .quiz-start-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        min-width: 120px;
    }

    /* Quiz Result Landscape Mobile */
    .quiz-result-container {
        padding: 0.75rem;
    }

    .quiz-result-header {
        padding: 1.5rem 1rem;
    }

    .quiz-result-gif {
        width: 60px;
        height: 60px;
        margin-bottom: 0.75rem;
    }

    .quiz-result-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .quiz-result-subtitle {
        font-size: 0.875rem;
    }

    .quiz-result-stats {
        padding: 1rem;
        grid-template-columns: repeat(4, 1fr);
        gap: 0.75rem;
    }

    .quiz-result-stat {
        padding: 0.75rem;
    }

    .quiz-result-stat-value {
        font-size: 1.25rem;
    }

    .quiz-result-stat-label {
        font-size: 0.7rem;
    }

    .quiz-result-progress {
        margin: 1rem 0;
    }

    .quiz-result-progress-bar {
        height: 1rem;
    }

    .quiz-result-percentage {
        font-size: 1.5rem;
        margin-top: 0.5rem;
    }

    .quiz-result-actions {
        padding: 1rem;
        gap: 0.5rem;
    }

    .quiz-result-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        min-width: 120px;
    }
}

/* ===== UTILITY CLASSES FOR QUIZ SYSTEM ===== */

/* ===== QUIZ PAGE SPECIFIC STYLES ===== */

/* Hub Button Styling */
.hub-button {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%) !important;
    border: none !important;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25) !important;
    transition: all 0.3s ease !important;
}

.hub-button:hover {
    background: linear-gradient(135deg, #0056D2 0%, #004494 100%) !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35) !important;
}

.hub-button:active {
    transform: translateY(0) scale(1.02) !important;
}

/* Quiz instruction, play, and result pages - simplified header */
.quiz-fullscreen .nav-modern {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%) !important;
    border-bottom: none !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.quiz-fullscreen .nav-modern h1 {
    color: white !important;
    font-weight: 700 !important;
    font-size: 1.25rem !important;
}

.quiz-fullscreen .nav-modern .text-gray-900 {
    color: white !important;
}

.quiz-fullscreen .nav-modern .text-gray-600 {
    color: rgba(255, 255, 255, 0.9) !important;
}

.quiz-fullscreen .nav-modern .border-gray-200 {
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.quiz-fullscreen .hub-button {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    backdrop-filter: blur(10px) !important;
}

.quiz-fullscreen .hub-button:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 8px 25px 0 rgba(255, 255, 255, 0.2) !important;
}

/* Responsive header for quiz pages */
@media (max-width: 640px) {
    .quiz-fullscreen .nav-modern h1 {
        font-size: 1rem !important;
    }

    .quiz-fullscreen .nav-modern .text-right {
        display: none !important;
    }

    .quiz-fullscreen .nav-modern .w-5 {
        width: 1rem !important;
        height: 0.75rem !important;
    }
}

@media (max-width: 480px) {
    .quiz-fullscreen .nav-modern h1 {
        font-size: 0.875rem !important;
    }

    .quiz-fullscreen .nav-modern .px-4 {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
    }

    .quiz-fullscreen .nav-modern .space-x-3 {
        gap: 0.5rem !important;
    }
}

/* General responsive improvements for all pages */
@media (max-width: 640px) {
    .hub-button {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
    }

    .hub-button span {
        display: none !important;
    }

    .nav-modern .text-right {
        display: none !important;
    }

    .nav-modern .w-5 {
        width: 1rem !important;
        height: 0.75rem !important;
    }
}

@media (max-width: 480px) {
    .hub-button {
        padding: 0.375rem 0.75rem !important;
        font-size: 0.8rem !important;
    }

    .nav-modern .px-4 {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
    }

    .nav-modern .space-x-3 {
        gap: 0.5rem !important;
    }
}



/* Hide elements on mobile */
@media (max-width: 768px) {
    .hide-on-mobile {
        display: none !important;
    }

    /* Full screen quiz experience on mobile */
    .quiz-fullscreen-mobile {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        background: white;
    }

    /* Hide sidebar during quiz on mobile */
    .layout-sidebar {
        display: none !important;
    }

    .layout-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
}

/* Show elements only on mobile */
@media (min-width: 769px) {
    .show-on-mobile-only {
        display: none !important;
    }
}

/* Accessibility improvements */
.quiz-option:focus,
.quiz-nav-btn:focus,
.quiz-start-btn:focus,
.quiz-result-btn:focus {
    outline: 2px solid #007BFF;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .quiz-option {
        border-width: 3px;
    }

    .quiz-option.selected {
        border-width: 4px;
    }

    .quiz-timer.warning {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .quiz-option,
    .quiz-nav-btn,
    .quiz-start-btn,
    .quiz-result-btn,
    .quiz-card-wrapper {
        transition: none !important;
        animation: none !important;
    }

    .quiz-timer.warning {
        animation: none !important;
    }
}

/* Print styles */
@media print {
    .quiz-navigation,
    .quiz-timer,
    .quiz-progress-bar,
    .quiz-start-actions,
    .quiz-result-actions {
        display: none !important;
    }

    .quiz-container,
    .quiz-start-container,
    .quiz-result-container {
        background: white !important;
        box-shadow: none !important;
    }

    .quiz-question-container,
    .quiz-start-card,
    .quiz-result-card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
    .quiz-container,
    .quiz-listing-container,
    .quiz-start-container,
    .quiz-result-container {
        background: #1f2937;
        color: #f9fafb;
    }

    .quiz-question-container,
    .quiz-start-card,
    .quiz-result-card,
    .quiz-card-wrapper {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .quiz-option {
        background: #4b5563;
        border-color: #6b7280;
        color: #f9fafb;
    }

    .quiz-option:hover {
        background: #374151;
        border-color: #007BFF;
    }

    .quiz-fill-input {
        background: #4b5563;
        border-color: #6b7280;
        color: #f9fafb;
    }

    .quiz-timer {
        background: #4b5563;
        color: #f9fafb;
        border-color: #6b7280;
    }
}

/* Very small screens (smartwatches, etc.) */
@media (max-width: 280px) {
    .quiz-progress-container {
        padding: 0.5rem;
    }

    .quiz-title-section h1 {
        font-size: 0.875rem;
    }

    .quiz-subtitle {
        font-size: 0.7rem;
    }

    .quiz-timer {
        padding: 0.5rem;
        font-size: 0.8rem;
        min-width: 60px;
    }

    .quiz-content {
        padding: 0.5rem;
    }

    .quiz-question-container {
        padding: 0.75rem;
    }

    .quiz-question-text {
        font-size: 0.875rem;
    }

    .quiz-option {
        padding: 0.5rem;
        min-height: 40px;
        font-size: 0.8rem;
    }

    .quiz-option-letter {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        margin-right: 0.375rem;
    }

    .quiz-nav-btn {
        padding: 0.625rem 1rem;
        font-size: 0.8rem;
        min-width: 80px;
    }
}

/* Large screens (4K, ultrawide) */
@media (min-width: 1920px) {
    .quiz-listing-content,
    .quiz-result-content {
        max-width: 1600px;
    }

    .quiz-question-container {
        max-width: 1000px;
        margin-left: auto;
        margin-right: auto;
    }

    .quiz-question-text {
        font-size: 1.5rem;
    }

    .quiz-option {
        font-size: 1.125rem;
        min-height: 70px;
    }

    .quiz-option-letter {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    /* Ultra-wide image styles */
    .quiz-image-container-modern {
        margin: 2.5rem 0;
        padding: 0 2rem;
    }

    .quiz-image-wrapper {
        padding: 2rem;
        border-radius: 1.5rem;
        max-width: 1000px;
        margin: 0 auto;
    }

    .quiz-image-modern {
        max-height: 700px;
        border-radius: 1rem;
    }
}

/* ===== ENHANCED QUIZ CARD RESPONSIVE STYLES ===== */

/* Mobile Devices (max-width: 480px) */
@media (max-width: 480px) {
    .quiz-grid-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 0.5rem;
    }

    .quiz-card-modern .quiz-card {
        border-radius: 1rem;
    }

    .quiz-card-modern .quiz-card .bg-gradient-to-r {
        padding: 1rem;
        border-radius: 1rem 1rem 0 0;
    }

    .quiz-card-modern .quiz-card .grid-cols-3 {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .quiz-card-modern .quiz-card .bg-gray-50 {
        padding: 0.75rem;
        border-radius: 0.5rem;
    }

    .quiz-card-modern .quiz-card .text-xl {
        font-size: 1rem;
        line-height: 1.3;
    }

    .quiz-card-modern .quiz-card .text-lg {
        font-size: 0.875rem;
    }

    .quiz-card-modern .quiz-card .text-sm {
        font-size: 0.75rem;
    }

    /* Mobile button adjustments */
    .quiz-card-modern .quiz-card .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .quiz-card-modern .quiz-card .pb-6 {
        padding-bottom: 1rem;
    }

    /* Mobile quiz stats adjustments */
    .quiz-card-modern .quiz-card .flex-wrap {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .quiz-card-modern .quiz-card .space-x-3 {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .quiz-card-modern .quiz-card .flex.space-x-3 {
        flex-direction: column;
        gap: 0.5rem;
    }

    .quiz-card-modern .quiz-card .flex-1 {
        flex: none;
    }

    .quiz-card-modern .quiz-card .p-6 {
        padding: 0.75rem;
    }

    .quiz-card-modern .quiz-card .px-6 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .quiz-card-modern .quiz-card .pb-6 {
        padding-bottom: 0.75rem;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .quiz-grid-container {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .quiz-card-modern .quiz-card .bg-gradient-to-r {
        padding: 1rem;
    }

    .quiz-card-modern .quiz-card .grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }

    .quiz-card-modern .quiz-card .bg-gray-50 {
        padding: 0.625rem;
    }

    .quiz-card-modern .quiz-card .text-xl {
        font-size: 1.125rem;
        line-height: 1.4;
    }

    .quiz-card-modern .quiz-card .text-lg {
        font-size: 0.9375rem;
    }

    .quiz-card-modern .quiz-card .p-6 {
        padding: 1rem;
    }

    .quiz-card-modern .quiz-card .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .quiz-card-modern .quiz-card .pb-6 {
        padding-bottom: 1rem;
    }
}

/* Large screens - enhanced hover effects */
@media (min-width: 1024px) {

    .quiz-card-modern:hover {
        transform: translateY(-6px) scale(1.01);
    }

    .quiz-card-modern .quiz-card:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15);
    }

    .quiz-card-modern .quiz-card .bg-gradient-to-r {
        padding: 1.25rem;
    }

    .quiz-card-modern .quiz-card .p-6 {
        padding: 1.25rem;
    }

    .quiz-card-modern .quiz-card .px-6 {
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    .quiz-card-modern .quiz-card .pb-6 {
        padding-bottom: 1.25rem;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .quiz-card-modern,
    .quiz-card-modern .quiz-card,
    .quiz-image-wrapper,
    .quiz-image-modern {
        animation: none !important;
        transition: none !important;
    }

    .quiz-card-modern:hover,
    .quiz-card-modern:hover .quiz-card,
    .quiz-image-wrapper:hover,
    .quiz-image-modern:hover {
        transform: none !important;
    }
}

/* ===== ENHANCED QUIZ PAGE RESPONSIVENESS ===== */

/* Very Small Screens (max-width: 320px) */
@media (max-width: 320px) {
    .quiz-content {
        padding: 0.5rem;
    }

    .quiz-question-container {
        padding: 0.75rem;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        overflow-wrap: break-word;
    }

    .quiz-question-text {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 1.5rem;
    }

    .quiz-options {
        gap: 0.5rem;
    }

    .quiz-option {
        padding: 0.75rem;
        min-height: 48px;
        font-size: 0.875rem;
        border-radius: 0.5rem;
    }

    .quiz-option-letter {
        width: 24px;
        height: 24px;
        font-size: 0.75rem;
        margin-right: 0.5rem;
    }

    .quiz-image-container-modern {
        margin: 0.75rem 0;
        padding: 0;
    }

    .quiz-image-wrapper {
        padding: 0.5rem;
        border-radius: 0.5rem;
        margin: 0 0.25rem;
    }

    .quiz-image-modern {
        max-height: 200px;
        border-radius: 0.25rem;
    }
}

/* High DPI Screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .quiz-image-modern {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .quiz-image-wrapper {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #000 !important;
        page-break-inside: avoid;
    }

    .quiz-image-modern {
        max-height: 400px !important;
        page-break-inside: avoid;
    }

    .quiz-navigation {
        display: none !important;
    }
}

/* ===== ENHANCED MOBILE OPTIMIZATIONS ===== */

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .quiz-option {
        min-height: 56px;
        padding: 1rem 1.25rem;
    }

    .quiz-option-letter {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }

    .quiz-nav-btn {
        min-height: 48px;
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .quiz-image-wrapper {
        padding: 1rem;
    }
}

/* ===== ENHANCED LARGE SCREEN OPTIMIZATIONS ===== */

/* 2K Screens (1440p) */
@media (min-width: 2560px) {
    .quiz-content {
        padding: 3rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    .quiz-question-container {
        padding: 3rem;
        max-width: 1200px;
        margin: 0 auto 3rem;
        border-radius: 1.5rem;
    }

    .quiz-question-text {
        font-size: 1.75rem;
        line-height: 1.6;
        margin-bottom: 3rem;
    }

    .quiz-options {
        gap: 1.5rem;
        margin-bottom: 3rem;
    }

    .quiz-option {
        padding: 1.5rem 2rem;
        min-height: 80px;
        font-size: 1.25rem;
        border-radius: 1rem;
    }

    .quiz-option-letter {
        width: 48px;
        height: 48px;
        font-size: 1.125rem;
        margin-right: 1.5rem;
    }

    .quiz-navigation {
        padding: 1.5rem 2rem;
        gap: 2rem;
    }

    .quiz-nav-btn {
        padding: 1.25rem 3rem;
        font-size: 1.125rem;
        min-width: 200px;
        border-radius: 0.75rem;
    }

    /* 2K Image styles */
    .quiz-image-container-modern {
        margin: 3rem 0;
        padding: 0 3rem;
    }

    .quiz-image-wrapper {
        padding: 2.5rem;
        border-radius: 2rem;
        max-width: 1200px;
        margin: 0 auto;
    }

    .quiz-image-modern {
        max-height: 800px;
        border-radius: 1.25rem;
    }
}

/* 4K Screens (2160p) */
@media (min-width: 3840px) {
    .quiz-content {
        padding: 4rem;
        max-width: 1800px;
    }

    .quiz-question-container {
        padding: 4rem;
        max-width: 1600px;
        border-radius: 2rem;
    }

    .quiz-question-text {
        font-size: 2rem;
        margin-bottom: 4rem;
    }

    .quiz-options {
        gap: 2rem;
        margin-bottom: 4rem;
    }

    .quiz-option {
        padding: 2rem 2.5rem;
        min-height: 100px;
        font-size: 1.5rem;
        border-radius: 1.25rem;
    }

    .quiz-option-letter {
        width: 56px;
        height: 56px;
        font-size: 1.25rem;
        margin-right: 2rem;
    }

    /* 4K Image styles */
    .quiz-image-container-modern {
        margin: 4rem 0;
        padding: 0 4rem;
    }

    .quiz-image-wrapper {
        padding: 3rem;
        border-radius: 2.5rem;
        max-width: 1400px;
    }

    .quiz-image-modern {
        max-height: 1000px;
        border-radius: 1.5rem;
    }
}

/* ===== QUIZ START PAGE RESPONSIVE STYLES ===== */

/* Mobile Devices (max-width: 480px) */
@media (max-width: 480px) {
    .quiz-start-container {
        padding: 1rem 0.5rem;
    }

    .quiz-start-card {
        padding: 1.5rem;
        border-radius: 1rem;
    }

    .quiz-start-title {
        font-size: 1.75rem;
    }

    .quiz-start-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .quiz-start-info {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .quiz-info-item {
        padding: 1rem;
    }

    .quiz-info-value {
        font-size: 1.5rem;
    }

    .quiz-start-instructions {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .quiz-start-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .quiz-start-btn {
        padding: 0.875rem 1.5rem;
        min-width: auto;
        width: 100%;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .quiz-start-card {
        padding: 2rem;
    }

    .quiz-start-title {
        font-size: 2rem;
    }

    .quiz-start-info {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .quiz-info-item {
        padding: 1.25rem;
    }

    .quiz-start-actions {
        flex-direction: row;
        gap: 1rem;
    }
}

/* ===== QUIZ RESULT PAGE RESPONSIVE STYLES ===== */

/* Mobile Devices (max-width: 480px) */
@media (max-width: 480px) {
    .quiz-result-container {
        padding: 1rem 0.5rem;
    }

    .quiz-result-header {
        padding: 1.5rem;
        border-radius: 1rem;
        margin-bottom: 1.5rem;
    }

    .quiz-result-gif {
        width: 80px;
        height: 80px;
    }

    .quiz-result-title {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
        line-height: 1.2;
    }

    .quiz-result-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
        line-height: 1.4;
    }

    .quiz-result-score-circle {
        width: 120px;
        height: 120px;
    }

    .quiz-result-score-value {
        font-size: 2rem;
    }

    /* Statistics grid improvements */
    .quiz-result-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .quiz-result-stat-card {
        padding: 1rem;
        border-radius: 0.75rem;
    }

    .quiz-result-stat-value {
        font-size: 1.5rem;
    }

    .quiz-result-stat-label {
        font-size: 0.75rem;
    }

    /* Action buttons */
    .quiz-result-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .quiz-result-btn {
        width: 100%;
        padding: 1rem;
        font-size: 0.875rem;
        border-radius: 0.75rem;
        min-height: 48px;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .quiz-result-header {
        padding: 2rem;
    }

    .quiz-result-title {
        font-size: 2.5rem;
    }

    .quiz-result-score-circle {
        width: 130px;
        height: 130px;
    }

    .quiz-result-score-value {
        font-size: 2.25rem;
    }
}

/* Large screens */
@media (min-width: 1024px) {
    .quiz-start-card {
        max-width: 700px;
        padding: 3.5rem;
    }

    .quiz-start-title {
        font-size: 3rem;
    }

    .quiz-result-header {
        max-width: 900px;
        margin: 0 auto 2rem;
        padding: 3.5rem;
    }

    .quiz-result-title {
        font-size: 3.5rem;
    }

    .quiz-result-score-circle {
        width: 180px;
        height: 180px;
    }

    .quiz-result-score-value {
        font-size: 3rem;
    }
}
