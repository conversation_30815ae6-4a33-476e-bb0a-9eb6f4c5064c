{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\common\\\\ProfilePicture.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { getUserOnlineStatus } from '../../apicalls/notifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePicture = ({\n  user,\n  size = 'md',\n  showOnlineStatus = true,\n  className = '',\n  onClick = null,\n  style = {},\n  refreshInterval = 30000,\n  // 30 seconds\n  ...props\n}) => {\n  _s();\n  const [isOnline, setIsOnline] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  // Check actual online status from server\n  useEffect(() => {\n    if (!showOnlineStatus || !(user !== null && user !== void 0 && user._id)) {\n      setLoading(false);\n      setIsOnline(false);\n      return;\n    }\n    const checkOnlineStatus = async () => {\n      try {\n        const response = await getUserOnlineStatus(user._id);\n        if (response.success) {\n          setIsOnline(response.data.isOnline);\n        } else {\n          setIsOnline(false);\n        }\n      } catch (error) {\n        console.error('Error checking online status:', error);\n        setIsOnline(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Initial check\n    checkOnlineStatus();\n\n    // Set up interval for periodic checks\n    const interval = setInterval(checkOnlineStatus, refreshInterval);\n    return () => clearInterval(interval);\n  }, [user === null || user === void 0 ? void 0 : user._id, showOnlineStatus, refreshInterval]);\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'xs':\n        return {\n          container: 'w-6 h-6',\n          text: 'text-xs'\n        };\n      case 'sm':\n        return {\n          container: 'w-8 h-8',\n          text: 'text-xs'\n        };\n      case 'md':\n        return {\n          container: 'w-10 h-10',\n          text: 'text-sm'\n        };\n      case 'lg':\n        return {\n          container: 'w-12 h-12',\n          text: 'text-base'\n        };\n      case 'xl':\n        return {\n          container: 'w-16 h-16',\n          text: 'text-lg'\n        };\n      case '2xl':\n        return {\n          container: 'w-20 h-20',\n          text: 'text-xl'\n        };\n      case '3xl':\n        return {\n          container: 'w-24 h-24',\n          text: 'text-2xl'\n        };\n      default:\n        return {\n          container: 'w-10 h-10',\n          text: 'text-sm'\n        };\n    }\n  };\n  const sizeClasses = getSizeClasses();\n  const isClickable = onClick !== null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative inline-block ${className}`,\n    style: {\n      padding: showOnlineStatus ? '2px' : '0'\n    },\n    ...props,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n          ${sizeClasses.container}\n          rounded-full overflow-hidden border-2 border-white/20 relative\n          ${isClickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}\n        `,\n      style: {\n        background: '#f0f0f0',\n        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n        ...style\n      },\n      onClick: onClick,\n      children: [user !== null && user !== void 0 && user.profileImage || user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n        src: user.profileImage || user.profilePicture,\n        alt: user.name || 'User',\n        className: \"object-cover rounded-full w-full h-full\",\n        style: {\n          objectFit: 'cover'\n        },\n        onError: e => {\n          // Fallback to initials if image fails to load\n          e.target.style.display = 'none';\n          e.target.nextSibling.style.display = 'flex';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n            rounded-full flex items-center justify-center font-semibold w-full h-full\n            ${sizeClasses.text}\n            ${user !== null && user !== void 0 && user.profileImage || user !== null && user !== void 0 && user.profilePicture ? 'hidden' : 'flex'}\n          `,\n        style: {\n          background: user !== null && user !== void 0 && user.profileImage || user !== null && user !== void 0 && user.profilePicture ? 'transparent' : '#25D366',\n          color: '#FFFFFF'\n        },\n        children: ((user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.username) || 'U').charAt(0).toUpperCase()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), showOnlineStatus && (user === null || user === void 0 ? void 0 : user._id) && !loading && isOnline && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        width: size === 'xs' ? '8px' : size === 'sm' ? '10px' : size === 'md' ? '12px' : size === 'lg' ? '14px' : size === 'xl' ? '16px' : size === '2xl' ? '18px' : size === '3xl' ? '20px' : '12px',\n        height: size === 'xs' ? '8px' : size === 'sm' ? '10px' : size === 'md' ? '12px' : size === 'lg' ? '14px' : size === 'xl' ? '16px' : size === '2xl' ? '18px' : size === '3xl' ? '20px' : '12px',\n        bottom: '-3px',\n        right: '-3px',\n        zIndex: 999,\n        borderRadius: '50%',\n        backgroundColor: '#22c55e',\n        background: '#22c55e',\n        border: '2px solid #ffffff',\n        outline: 'none',\n        boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n        animation: 'pulse 2s infinite'\n      },\n      title: \"Online\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePicture, \"ACeR92UGDK0RjWcqBbMeJyufZFA=\");\n_c = ProfilePicture;\nexport default ProfilePicture;\nvar _c;\n$RefreshReg$(_c, \"ProfilePicture\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "getUserOnlineStatus", "jsxDEV", "_jsxDEV", "ProfilePicture", "user", "size", "showOnlineStatus", "className", "onClick", "style", "refreshInterval", "props", "_s", "isOnline", "setIsOnline", "loading", "setLoading", "_id", "checkOnlineStatus", "response", "success", "data", "error", "console", "interval", "setInterval", "clearInterval", "getSizeClasses", "container", "text", "sizeClasses", "isClickable", "padding", "children", "background", "boxShadow", "profileImage", "profilePicture", "src", "alt", "name", "objectFit", "onError", "e", "target", "display", "nextS<PERSON>ling", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "username", "char<PERSON>t", "toUpperCase", "position", "width", "height", "bottom", "right", "zIndex", "borderRadius", "backgroundColor", "border", "outline", "animation", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/common/ProfilePicture.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { getUserOnlineStatus } from '../../apicalls/notifications';\n\nconst ProfilePicture = ({\n  user,\n  size = 'md',\n  showOnlineStatus = true,\n  className = '',\n  onClick = null,\n  style = {},\n  refreshInterval = 30000, // 30 seconds\n  ...props\n}) => {\n  const [isOnline, setIsOnline] = useState(false);\n  const [loading, setLoading] = useState(true);\n\n  // Check actual online status from server\n  useEffect(() => {\n    if (!showOnlineStatus || !user?._id) {\n      setLoading(false);\n      setIsOnline(false);\n      return;\n    }\n\n    const checkOnlineStatus = async () => {\n      try {\n        const response = await getUserOnlineStatus(user._id);\n        if (response.success) {\n          setIsOnline(response.data.isOnline);\n        } else {\n          setIsOnline(false);\n        }\n      } catch (error) {\n        console.error('Error checking online status:', error);\n        setIsOnline(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Initial check\n    checkOnlineStatus();\n\n    // Set up interval for periodic checks\n    const interval = setInterval(checkOnlineStatus, refreshInterval);\n\n    return () => clearInterval(interval);\n  }, [user?._id, showOnlineStatus, refreshInterval]);\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'xs':\n        return { container: 'w-6 h-6', text: 'text-xs' };\n      case 'sm':\n        return { container: 'w-8 h-8', text: 'text-xs' };\n      case 'md':\n        return { container: 'w-10 h-10', text: 'text-sm' };\n      case 'lg':\n        return { container: 'w-12 h-12', text: 'text-base' };\n      case 'xl':\n        return { container: 'w-16 h-16', text: 'text-lg' };\n      case '2xl':\n        return { container: 'w-20 h-20', text: 'text-xl' };\n      case '3xl':\n        return { container: 'w-24 h-24', text: 'text-2xl' };\n      default:\n        return { container: 'w-10 h-10', text: 'text-sm' };\n    }\n  };\n\n  const sizeClasses = getSizeClasses();\n  const isClickable = onClick !== null;\n\n  return (\n    <div \n      className={`relative inline-block ${className}`} \n      style={{ padding: showOnlineStatus ? '2px' : '0' }}\n      {...props}\n    >\n      <div\n        className={`\n          ${sizeClasses.container}\n          rounded-full overflow-hidden border-2 border-white/20 relative\n          ${isClickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}\n        `}\n        style={{\n          background: '#f0f0f0',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n          ...style\n        }}\n        onClick={onClick}\n      >\n        {user?.profileImage || user?.profilePicture ? (\n          <img\n            src={user.profileImage || user.profilePicture}\n            alt={user.name || 'User'}\n            className=\"object-cover rounded-full w-full h-full\"\n            style={{ objectFit: 'cover' }}\n            onError={(e) => {\n              // Fallback to initials if image fails to load\n              e.target.style.display = 'none';\n              e.target.nextSibling.style.display = 'flex';\n            }}\n          />\n        ) : null}\n        \n        {/* Fallback initials */}\n        <div\n          className={`\n            rounded-full flex items-center justify-center font-semibold w-full h-full\n            ${sizeClasses.text}\n            ${user?.profileImage || user?.profilePicture ? 'hidden' : 'flex'}\n          `}\n          style={{\n            background: user?.profileImage || user?.profilePicture ? 'transparent' : '#25D366',\n            color: '#FFFFFF'\n          }}\n        >\n          {(user?.name || user?.username || 'U').charAt(0).toUpperCase()}\n        </div>\n      </div>\n\n      {/* Online Status Indicator - Only show if actually online */}\n      {showOnlineStatus && user?._id && !loading && isOnline && (\n        <div\n          style={{\n            position: 'absolute',\n            width: size === 'xs' ? '8px' :\n                   size === 'sm' ? '10px' :\n                   size === 'md' ? '12px' :\n                   size === 'lg' ? '14px' :\n                   size === 'xl' ? '16px' :\n                   size === '2xl' ? '18px' :\n                   size === '3xl' ? '20px' : '12px',\n            height: size === 'xs' ? '8px' :\n                    size === 'sm' ? '10px' :\n                    size === 'md' ? '12px' :\n                    size === 'lg' ? '14px' :\n                    size === 'xl' ? '16px' :\n                    size === '2xl' ? '18px' :\n                    size === '3xl' ? '20px' : '12px',\n            bottom: '-3px',\n            right: '-3px',\n            zIndex: 999,\n            borderRadius: '50%',\n            backgroundColor: '#22c55e',\n            background: '#22c55e',\n            border: '2px solid #ffffff',\n            outline: 'none',\n            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n            animation: 'pulse 2s infinite'\n          }}\n          title=\"Online\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default ProfilePicture;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,mBAAmB,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,cAAc,GAAGA,CAAC;EACtBC,IAAI;EACJC,IAAI,GAAG,IAAI;EACXC,gBAAgB,GAAG,IAAI;EACvBC,SAAS,GAAG,EAAE;EACdC,OAAO,GAAG,IAAI;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,eAAe,GAAG,KAAK;EAAE;EACzB,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,IAAI,CAACQ,gBAAgB,IAAI,EAACF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEa,GAAG,GAAE;MACnCD,UAAU,CAAC,KAAK,CAAC;MACjBF,WAAW,CAAC,KAAK,CAAC;MAClB;IACF;IAEA,MAAMI,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMnB,mBAAmB,CAACI,IAAI,CAACa,GAAG,CAAC;QACpD,IAAIE,QAAQ,CAACC,OAAO,EAAE;UACpBN,WAAW,CAACK,QAAQ,CAACE,IAAI,CAACR,QAAQ,CAAC;QACrC,CAAC,MAAM;UACLC,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDR,WAAW,CAAC,KAAK,CAAC;MACpB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACAE,iBAAiB,CAAC,CAAC;;IAEnB;IACA,MAAMM,QAAQ,GAAGC,WAAW,CAACP,iBAAiB,EAAER,eAAe,CAAC;IAEhE,OAAO,MAAMgB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,GAAG,EAAEX,gBAAgB,EAAEI,eAAe,CAAC,CAAC;EAElD,MAAMiB,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQtB,IAAI;MACV,KAAK,IAAI;QACP,OAAO;UAAEuB,SAAS,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAC;MAClD,KAAK,IAAI;QACP,OAAO;UAAED,SAAS,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAC;MAClD,KAAK,IAAI;QACP,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAU,CAAC;MACpD,KAAK,IAAI;QACP,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAY,CAAC;MACtD,KAAK,IAAI;QACP,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAU,CAAC;MACpD,KAAK,KAAK;QACR,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAU,CAAC;MACpD,KAAK,KAAK;QACR,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAW,CAAC;MACrD;QACE,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAU,CAAC;IACtD;EACF,CAAC;EAED,MAAMC,WAAW,GAAGH,cAAc,CAAC,CAAC;EACpC,MAAMI,WAAW,GAAGvB,OAAO,KAAK,IAAI;EAEpC,oBACEN,OAAA;IACEK,SAAS,EAAG,yBAAwBA,SAAU,EAAE;IAChDE,KAAK,EAAE;MAAEuB,OAAO,EAAE1B,gBAAgB,GAAG,KAAK,GAAG;IAAI,CAAE;IAAA,GAC/CK,KAAK;IAAAsB,QAAA,gBAET/B,OAAA;MACEK,SAAS,EAAG;AACpB,YAAYuB,WAAW,CAACF,SAAU;AAClC;AACA,YAAYG,WAAW,GAAG,kEAAkE,GAAG,EAAG;AAClG,SAAU;MACFtB,KAAK,EAAE;QACLyB,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,4BAA4B;QACvC,GAAG1B;MACL,CAAE;MACFD,OAAO,EAAEA,OAAQ;MAAAyB,QAAA,GAEhB7B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,YAAY,IAAIhC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiC,cAAc,gBACzCnC,OAAA;QACEoC,GAAG,EAAElC,IAAI,CAACgC,YAAY,IAAIhC,IAAI,CAACiC,cAAe;QAC9CE,GAAG,EAAEnC,IAAI,CAACoC,IAAI,IAAI,MAAO;QACzBjC,SAAS,EAAC,yCAAyC;QACnDE,KAAK,EAAE;UAAEgC,SAAS,EAAE;QAAQ,CAAE;QAC9BC,OAAO,EAAGC,CAAC,IAAK;UACd;UACAA,CAAC,CAACC,MAAM,CAACnC,KAAK,CAACoC,OAAO,GAAG,MAAM;UAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAACrC,KAAK,CAACoC,OAAO,GAAG,MAAM;QAC7C;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACA,IAAI,eAGRhD,OAAA;QACEK,SAAS,EAAG;AACtB;AACA,cAAcuB,WAAW,CAACD,IAAK;AAC/B,cAAczB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,YAAY,IAAIhC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiC,cAAc,GAAG,QAAQ,GAAG,MAAO;AAC7E,WAAY;QACF5B,KAAK,EAAE;UACLyB,UAAU,EAAE9B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,YAAY,IAAIhC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiC,cAAc,GAAG,aAAa,GAAG,SAAS;UAClFc,KAAK,EAAE;QACT,CAAE;QAAAlB,QAAA,EAED,CAAC,CAAA7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,IAAI,MAAIpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgD,QAAQ,KAAI,GAAG,EAAEC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;MAAC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5C,gBAAgB,KAAIF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,GAAG,KAAI,CAACF,OAAO,IAAIF,QAAQ,iBACpDX,OAAA;MACEO,KAAK,EAAE;QACL8C,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAEnD,IAAI,KAAK,IAAI,GAAG,KAAK,GACrBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,KAAK,GAAG,MAAM,GACvBA,IAAI,KAAK,KAAK,GAAG,MAAM,GAAG,MAAM;QACvCoD,MAAM,EAAEpD,IAAI,KAAK,IAAI,GAAG,KAAK,GACrBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,KAAK,GAAG,MAAM,GACvBA,IAAI,KAAK,KAAK,GAAG,MAAM,GAAG,MAAM;QACxCqD,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,GAAG;QACXC,YAAY,EAAE,KAAK;QACnBC,eAAe,EAAE,SAAS;QAC1B5B,UAAU,EAAE,SAAS;QACrB6B,MAAM,EAAE,mBAAmB;QAC3BC,OAAO,EAAE,MAAM;QACf7B,SAAS,EAAE,kCAAkC;QAC7C8B,SAAS,EAAE;MACb,CAAE;MACFC,KAAK,EAAC;IAAQ;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtC,EAAA,CA1JIT,cAAc;AAAAgE,EAAA,GAAdhE,cAAc;AA4JpB,eAAeA,cAAc;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}