const express = require("express");
const app = express();
const morgan = require('morgan');
const chalk = require('chalk');
require("dotenv").config();
const dbConfig = require("./config/dbConfig");
const cors = require('cors')
const path = require("path");
const port = process.env.PORT || 5000;

const usersRoute = require("./routes/usersRoute");
const plansRoute = require("./routes/planRoute");
const examsRoute = require("./routes/examsRoute");
const resportsRoute = require("./routes/reportsRoute");
const studyRoute = require("./routes/studyRoute");
const reviewsRoute = require("./routes/reviewsRoute");
const forumQuestionRoute = require("./routes/forumQuestionRoute");
const chatgptRoute = require("./routes/chatRoute");
const awsBucketRoute = require("./uploads/awsBucket");
const paymentRoute =require("./routes/paymentRoute")
const aiQuestionGenerationRoute = require("./routes/aiQuestionGenerationRoute");
const authRoute = require("./routes/authRoute");
const enhancedQuizRoute = require("./routes/enhancedQuizRoute");
const xpDashboardRoute = require("./routes/xpDashboardRoute");
const notificationRoute = require("./routes/notificationRoute");

//Express Middlewares
// Enhanced CORS configuration for video streaming and document downloads
app.use(cors({
  origin: ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "https://www.stjosephkibadaengine.com"],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Range', 'Accept', 'Origin', 'X-Requested-With', 'Content-Disposition'],
  exposedHeaders: ['Content-Range', 'Accept-Ranges', 'Content-Length', 'Content-Type', 'Content-Disposition']
}));

morgan.format('short', (tokens, req, res) => {
  const method = tokens.method(req, res);
  const url = tokens.url(req, res);
  const status = tokens.status(req, res);
  const responseTime = Math.round(tokens['response-time'](req, res)); // Remove decimals
  let colorStatus = status >= 400 ? chalk.red(status) : chalk.green(status);

  return `${chalk.blue(method)} ${chalk.yellow(url)} ${colorStatus} ${responseTime}ms`;
});
app.use(morgan('short'))
app.use('/uploads', express.static(path.join(__dirname, 'Photos')));
app.use((req, res, next) => {
  if (req.originalUrl.startsWith('/api/payment/webhook')) {
      express.raw({ type: 'application/json' })(req, res, next);
  } else {
      express.json({ limit: '500mb' })(req, res, next);
  }
});

// Add URL encoded middleware with increased limit
app.use(express.urlencoded({ extended: true, limit: '500mb' }));

//Server Status Endpoint
app.get('/', (req, res) => {
  res.send('Server is Up!');
});

//Endpoint Routes
app.use("/api/chatgpt", chatgptRoute);
app.use("/api/users", usersRoute);
app.use("/api/image", awsBucketRoute);
app.use("/api/exams", examsRoute);
app.use("/api/reports", resportsRoute);
app.use("/api/study", studyRoute);
app.use("/api/reviews", reviewsRoute);
app.use("/api/forum", forumQuestionRoute);
app.use("/api/plans", plansRoute);
app.use("/api/payment", paymentRoute);
app.use("/api/ai-questions", aiQuestionGenerationRoute);
app.use("/api/auth", authRoute);
app.use("/api/quiz", enhancedQuizRoute);
app.use("/api/xp-dashboard", xpDashboardRoute);
app.use("/api/notifications", notificationRoute);



app.listen(port, () => {
  console.log(`Server listening on port ${port}`);
});