{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\AIQuestionGeneration\\\\QuestionGenerationForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { Card, Form, Select, InputNumber, Button, Row, Col, Checkbox, message, Divider, Alert, Progress } from \"antd\";\nimport { FaArrowLeft, FaRobot } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllExams } from \"../../../apicalls/exams\";\nimport { generateQuestions, getSubjectsForLevel, getSyllabusTopics } from \"../../../apicalls/aiQuestions\";\nimport { getSyllabusesForAI } from \"../../../apicalls/syllabus\";\nimport AutoGenerateExamModal from \"./AutoGenerateExamModal\";\nimport AILoginModal from \"../../../components/AILoginModal\";\nimport { useAIAuth } from \"../../../hooks/useAIAuth\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction QuestionGenerationForm({\n  onBack,\n  onSuccess\n}) {\n  _s();\n  var _classOptions$selecte;\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n\n  // Enhanced authentication\n  const {\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    loading: authLoading,\n    requiresUpgrade,\n    needsLogin,\n    handleLoginSuccess,\n    requireAIAuth,\n    sessionExpiringSoon,\n    timeUntilExpiry\n  } = useAIAuth();\n  const [exams, setExams] = useState([]);\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [availableTopics, setAvailableTopics] = useState([]);\n  const [availableSyllabuses, setAvailableSyllabuses] = useState([]);\n  const [selectedLevel, setSelectedLevel] = useState(\"\");\n  const [selectedClass, setSelectedClass] = useState(\"\");\n  const [selectedSubjects, setSelectedSubjects] = useState([]);\n  const [selectedSyllabus, setSelectedSyllabus] = useState(null);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationProgress, setGenerationProgress] = useState(0);\n  const [showAutoGenerateModal, setShowAutoGenerateModal] = useState(false);\n  const [showLoginModal, setShowLoginModal] = useState(false);\n  const fetchExams = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      if (response.success && Array.isArray(response.data)) {\n        // Filter out any null or undefined values\n        const validExams = response.data.filter(exam => exam && exam._id);\n        setExams(validExams);\n      } else {\n        message.error(\"Failed to fetch exams\");\n        setExams([]); // Ensure exams is always an array\n      }\n    } catch (error) {\n      message.error(\"Error fetching exams\");\n      setExams([]); // Ensure exams is always an array\n    } finally {\n      dispatch(HideLoading());\n    }\n  }, [dispatch]);\n  useEffect(() => {\n    // Only fetch exams if we have authentication, otherwise let the auth hook handle it\n    if (isAuthenticated && hasAIAccess && !authLoading) {\n      fetchExams();\n    }\n  }, [isAuthenticated, hasAIAccess, authLoading, fetchExams]);\n  const handleLevelChange = async level => {\n    setSelectedLevel(level);\n    setSelectedClass(\"\");\n    setSelectedSubjects([]);\n    setAvailableTopics([]);\n    form.setFieldsValue({\n      class: undefined,\n      subjects: [],\n      syllabusTopics: []\n    });\n    try {\n      const response = await getSubjectsForLevel(level);\n      if (response.success) {\n        setAvailableSubjects(response.data);\n      } else {\n        message.error(\"Failed to fetch subjects\");\n      }\n    } catch (error) {\n      message.error(\"Error fetching subjects\");\n    }\n  };\n  const handleClassChange = className => {\n    setSelectedClass(className);\n    setAvailableTopics([]);\n    form.setFieldsValue({\n      syllabusTopics: []\n    });\n\n    // If subjects are already selected, fetch topics\n    if (selectedSubjects.length > 0) {\n      fetchTopicsForSubjects(selectedLevel, className, selectedSubjects);\n    }\n  };\n  const handleSubjectsChange = subjects => {\n    setSelectedSubjects(subjects);\n    setAvailableTopics([]);\n    form.setFieldsValue({\n      syllabusTopics: []\n    });\n\n    // If class is selected, fetch topics\n    if (selectedClass) {\n      fetchTopicsForSubjects(selectedLevel, selectedClass, subjects);\n    }\n\n    // Note: Auto-generate exam functionality moved to modal\n  };\n\n  const fetchTopicsForSubjects = async (level, className, subjects) => {\n    if (!level || !className || subjects.length === 0) return;\n    try {\n      const allTopics = [];\n      for (const subject of subjects) {\n        const response = await getSyllabusTopics(level, className, subject);\n        if (response.success) {\n          const subjectTopics = response.data.topics.map(topic => ({\n            ...topic,\n            subject: subject,\n            fullName: `${subject}: ${topic.topicName}`\n          }));\n          allTopics.push(...subjectTopics);\n        }\n      }\n      setAvailableTopics(allTopics);\n    } catch (error) {\n      console.error(\"Error fetching topics:\", error);\n      message.error(\"Failed to fetch syllabus topics\");\n    }\n  };\n  const handleAutoGenerateExamSuccess = newExam => {\n    // Add the new exam to the list and select it\n    if (newExam && newExam._id) {\n      const updatedExams = [...exams, newExam];\n      setExams(updatedExams);\n      form.setFieldsValue({\n        examId: newExam._id\n      });\n      setShowAutoGenerateModal(false);\n      message.success(`Exam created successfully: ${newExam.name}`);\n    } else {\n      message.error(\"Invalid exam data received\");\n      setShowAutoGenerateModal(false);\n    }\n  };\n  const openAutoGenerateModal = () => {\n    setShowAutoGenerateModal(true);\n  };\n  const onFinish = async values => {\n    console.log(\"🚀 Form submission started\");\n    console.log(\"📝 Form values:\", values);\n    try {\n      setIsGenerating(true);\n      setGenerationProgress(10);\n\n      // Validate question distribution\n      const totalDistribution = Object.values(values.questionDistribution || {}).reduce((sum, count) => sum + (count || 0), 0);\n      console.log(\"📊 Total distribution:\", totalDistribution, \"Total questions:\", values.totalQuestions);\n      if (totalDistribution !== values.totalQuestions) {\n        console.error(\"❌ Distribution validation failed\");\n        message.error(\"Question distribution must equal total questions\");\n        setIsGenerating(false);\n        return;\n      }\n      console.log(\"✅ Distribution validation passed\");\n      setGenerationProgress(30);\n\n      // Check authentication and AI access\n      if (!isAuthenticated || !hasAIAccess) {\n        setIsGenerating(false);\n        setShowLoginModal(true);\n        message.warning(\"Please login to access AI question generation features.\");\n        return;\n      }\n\n      // Double-check with server-side validation\n      const authCheck = await requireAIAuth();\n      if (!authCheck.success) {\n        setIsGenerating(false);\n        switch (authCheck.reason) {\n          case 'not_authenticated':\n          case 'refresh_failed':\n            setShowLoginModal(true);\n            message.warning(\"Please login to generate AI questions.\");\n            return;\n          case 'no_ai_access':\n            message.error(\"AI features are not available for your account.\");\n            return;\n          case 'requires_upgrade':\n            message.warning(\"AI question generation requires a premium subscription. Please upgrade your account.\");\n            return;\n          default:\n            setShowLoginModal(true);\n            message.warning(\"Authentication check failed. Please login again.\");\n            return;\n        }\n      }\n      const payload = {\n        examId: values.examId,\n        questionTypes: values.questionTypes,\n        subjects: values.subjects,\n        level: values.level,\n        class: values.class,\n        difficultyLevels: values.difficultyLevels,\n        syllabusTopics: values.syllabusTopics || [],\n        totalQuestions: values.totalQuestions,\n        questionDistribution: values.questionDistribution,\n        userId: user._id\n      };\n      console.log(\"📤 Sending payload:\", payload);\n      setGenerationProgress(50);\n      console.log(\"🌐 Making API call to generate questions...\");\n\n      // Show progress message to user\n      message.info(\"AI is generating your questions... This may take a few minutes.\", 5);\n      const response = await generateQuestions(payload);\n      console.log(\"📥 API response received:\", response);\n      setGenerationProgress(90);\n      if (response.success) {\n        setGenerationProgress(100);\n        message.success(\"Questions generated successfully!\");\n        setTimeout(() => {\n          onSuccess();\n        }, 1000);\n      } else {\n        message.error(response.message || \"Failed to generate questions\");\n      }\n    } catch (error) {\n      console.error(\"Question generation error:\", error);\n\n      // More detailed error handling\n      let errorMessage = \"Error generating questions\";\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n        // Handle timeout errors specifically\n        errorMessage = \"Question generation is taking longer than expected. This might be due to high server load. Please try again with fewer questions or check your internet connection.\";\n      } else if (error.response) {\n        // Server responded with error status\n        console.error(\"Server error response:\", error.response.data);\n        console.error(\"Server error status:\", error.response.status);\n        if (error.response.status === 401) {\n          // Handle authentication errors for AI requests\n          const errorData = error.response.data;\n          if (errorData !== null && errorData !== void 0 && errorData.requiresLogin) {\n            // Show the AI login modal instead of redirecting\n            setShowLoginModal(true);\n            errorMessage = errorData.message || \"Authentication required for AI features.\";\n          } else {\n            errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.message) || \"Authentication failed. Please login again.\";\n          }\n        } else if (error.response.status === 403) {\n          // Handle permission/subscription errors\n          const errorData = error.response.data;\n          if (errorData !== null && errorData !== void 0 && errorData.upgradeRequired) {\n            errorMessage = \"AI question generation requires a premium subscription. Please upgrade your account.\";\n          } else {\n            errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.message) || \"Access denied for AI features.\";\n          }\n        } else if (error.response.status === 504 || error.response.status === 502) {\n          errorMessage = \"Server timeout. The AI generation process is taking longer than expected. Please try again with fewer questions.\";\n        } else {\n          var _error$response$data;\n          errorMessage = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || `Server error: ${error.response.status}`;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        console.error(\"Network error:\", error.request);\n        errorMessage = \"Network error - please check your connection. If the problem persists, try generating fewer questions at once.\";\n      } else {\n        // Something else happened\n        console.error(\"Error:\", error.message);\n        errorMessage = error.message || \"Unknown error occurred\";\n      }\n      message.error(errorMessage);\n    } finally {\n      setIsGenerating(false);\n      setGenerationProgress(0);\n    }\n  };\n  const questionTypeOptions = [{\n    label: \"Multiple Choice\",\n    value: \"multiple_choice\"\n  }, {\n    label: \"Fill in the Blank\",\n    value: \"fill_blank\"\n  }, {\n    label: \"Picture-based\",\n    value: \"picture_based\"\n  }];\n  const difficultyOptions = [{\n    label: \"Easy\",\n    value: \"easy\"\n  }, {\n    label: \"Medium\",\n    value: \"medium\"\n  }, {\n    label: \"Hard\",\n    value: \"hard\"\n  }];\n  const levelOptions = [{\n    label: \"Primary Education (Standards I-VI)\",\n    value: \"primary\"\n  }, {\n    label: \"Ordinary Secondary (Forms I-IV)\",\n    value: \"ordinary_secondary\"\n  }, {\n    label: \"Advanced Secondary (Forms V-VI)\",\n    value: \"advanced_secondary\"\n  }];\n  const classOptions = {\n    primary: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\"],\n    // Standards I-VI (TIE Primary)\n    ordinary_secondary: [\"I\", \"II\", \"III\", \"IV\"],\n    // Forms I-IV (TIE Ordinary Secondary)\n    advanced_secondary: [\"V\", \"VI\"] // Forms V-VI (TIE Advanced Secondary)\n  };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"question-generation-form\",\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 21\n          }, this),\n          onClick: onBack,\n          className: \"back-button\",\n          children: \"Back to Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"title-section\",\n          children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Generate AI Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this),\n      children: [authLoading ? /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Checking Authentication...\",\n        description: \"Verifying your access to AI features.\",\n        type: \"info\",\n        showIcon: true,\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this) : !isAuthenticated ? /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Login Required\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please login to access AI question generation features.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"small\",\n            onClick: () => setShowLoginModal(true),\n            style: {\n              marginTop: 8\n            },\n            children: \"Login Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 15\n        }, this),\n        type: \"warning\",\n        showIcon: true,\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 11\n      }, this) : !hasAIAccess ? /*#__PURE__*/_jsxDEV(Alert, {\n        message: requiresUpgrade ? \"Upgrade Required\" : \"AI Access Restricted\",\n        description: requiresUpgrade ? \"AI question generation requires a premium subscription. Please upgrade your account.\" : \"AI features are not available for your account. Please contact support.\",\n        type: \"error\",\n        showIcon: true,\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 11\n      }, this) : sessionExpiringSoon ? /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Session Expiring Soon\",\n        description: `Your session will expire in ${timeUntilExpiry}. Consider refreshing your login.`,\n        type: \"warning\",\n        showIcon: true,\n        className: \"mb-4\",\n        action: /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          onClick: () => setShowLoginModal(true),\n          children: \"Refresh Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"AI Features Ready\",\n        description: `Welcome ${user === null || user === void 0 ? void 0 : user.name}! You have full access to AI question generation.`,\n        type: \"success\",\n        showIcon: true,\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 11\n      }, this), isGenerating && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"Generating Questions\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"AI is generating your questions. This may take a few moments...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: generationProgress,\n            status: \"active\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 15\n        }, this),\n        type: \"info\",\n        showIcon: true,\n        className: \"mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: onFinish,\n        disabled: isGenerating || !hasAIAccess || authLoading,\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              message: \"Exam Selection\",\n              description: \"You can either select an existing exam or create a new one using the auto-generate feature. Questions can also be generated independently without an exam.\",\n              type: \"info\",\n              showIcon: true,\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 16,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"examId\",\n              label: \"Target Exam (Optional)\",\n              extra: \"Leave empty to generate standalone questions, or select an existing exam\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"Optional: Choose an existing exam\",\n                allowClear: true,\n                children: exams && exams.length > 0 && exams.map(exam => exam && exam._id ? /*#__PURE__*/_jsxDEV(Option, {\n                  value: exam._id,\n                  children: [exam.name, \" - \", exam.category]\n                }, exam._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 23\n                }, this) : null)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Or Create New Exam\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"dashed\",\n                icon: /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 25\n                }, this),\n                onClick: openAutoGenerateModal,\n                style: {\n                  width: \"100%\"\n                },\n                disabled: isGenerating || !hasAIAccess || authLoading,\n                children: \"Auto-Generate New Exam\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"level\",\n              label: \"Education Level\",\n              rules: [{\n                required: true,\n                message: \"Please select a level\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"Choose education level\",\n                onChange: handleLevelChange,\n                children: levelOptions.map(option => /*#__PURE__*/_jsxDEV(Option, {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"class\",\n              label: \"Class\",\n              rules: [{\n                required: true,\n                message: \"Please select a class\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"Choose class\",\n                disabled: !selectedLevel,\n                onChange: handleClassChange,\n                children: selectedLevel && ((_classOptions$selecte = classOptions[selectedLevel]) === null || _classOptions$selecte === void 0 ? void 0 : _classOptions$selecte.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                  value: cls,\n                  children: [\"Class \", cls]\n                }, cls, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 21\n                }, this)))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"subjects\",\n              label: \"Subjects\",\n              rules: [{\n                required: true,\n                message: \"Please select at least one subject\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                mode: \"multiple\",\n                placeholder: \"Choose subjects\",\n                disabled: !selectedLevel,\n                onChange: handleSubjectsChange,\n                children: availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(Option, {\n                  value: subject,\n                  children: subject\n                }, subject, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"questionTypes\",\n              label: \"Question Types\",\n              rules: [{\n                required: true,\n                message: \"Please select at least one question type\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n                options: questionTypeOptions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"difficultyLevels\",\n              label: \"Difficulty Levels\",\n              rules: [{\n                required: true,\n                message: \"Please select at least one difficulty level\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n                options: difficultyOptions\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"totalQuestions\",\n              label: \"Total Questions\",\n              rules: [{\n                required: true,\n                message: \"Please enter total questions\"\n              }, {\n                type: \"number\",\n                min: 1,\n                max: 50,\n                message: \"Must be between 1 and 50\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 50,\n                placeholder: \"Enter total questions\",\n                style: {\n                  width: \"100%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          children: \"Question Distribution\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 11\n        }, this), selectedLevel && selectedClass && selectedSubjects.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"Tanzania Syllabus Information\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Level:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 22\n              }, this), \" \", selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Class:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 22\n              }, this), \" \", selectedClass]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Subjects:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 22\n              }, this), \" \", selectedSubjects.join(\", \")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Available Topics:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 22\n              }, this), \" \", availableTopics.length, \" topics from Tanzania National Curriculum\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Auto-generate:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 22\n              }, this), \" Use the button above to create a new exam with proper structure\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: [\"questionDistribution\", \"multiple_choice\"],\n              label: \"Multiple Choice\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                placeholder: \"0\",\n                style: {\n                  width: \"100%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: [\"questionDistribution\", \"fill_blank\"],\n              label: \"Fill in the Blank\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                placeholder: \"0\",\n                style: {\n                  width: \"100%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: [\"questionDistribution\", \"picture_based\"],\n              label: \"Picture-based\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                placeholder: \"0\",\n                style: {\n                  width: \"100%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"syllabusTopics\",\n          label: `Tanzania Syllabus Topics (${availableTopics.length} available)`,\n          extra: availableTopics.length === 0 ? \"Select level, class, and subjects to see available topics\" : \"Select specific topics from Tanzania National Curriculum\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            mode: \"multiple\",\n            placeholder: availableTopics.length === 0 ? \"No topics available - select level, class, and subjects first\" : \"Choose specific topics from Tanzania syllabus\",\n            style: {\n              width: \"100%\"\n            },\n            disabled: availableTopics.length === 0,\n            optionFilterProp: \"children\",\n            showSearch: true,\n            filterOption: (input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0,\n            children: availableTopics.map((topic, index) => /*#__PURE__*/_jsxDEV(Option, {\n              value: topic.topicName,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: topic.topicName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: \"12px\",\n                    color: \"#666\"\n                  },\n                  children: [topic.subject, \" \\u2022 Difficulty: \", topic.difficulty]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 21\n                }, this), topic.subtopics && topic.subtopics.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: \"11px\",\n                    color: \"#999\"\n                  },\n                  children: [\"Subtopics: \", topic.subtopics.slice(0, 3).join(\", \"), topic.subtopics.length > 3 && ` +${topic.subtopics.length - 3} more`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 19\n              }, this)\n            }, `${topic.subject}-${topic.topicName}-${index}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: onBack,\n            disabled: isGenerating,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            loading: isGenerating,\n            disabled: !hasAIAccess || authLoading,\n            icon: /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 21\n            }, this),\n            children: isGenerating ? \"Generating...\" : !hasAIAccess ? \"Login Required\" : \"Generate Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AutoGenerateExamModal, {\n      visible: showAutoGenerateModal,\n      onCancel: () => setShowAutoGenerateModal(false),\n      onSuccess: handleAutoGenerateExamSuccess,\n      prefilledData: {\n        level: selectedLevel,\n        class: selectedClass,\n        subjects: selectedSubjects\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AILoginModal, {\n      visible: showLoginModal,\n      onCancel: () => setShowLoginModal(false),\n      onSuccess: userData => {\n        handleLoginSuccess(userData);\n        setShowLoginModal(false);\n      },\n      title: \"AI Features Login Required\",\n      description: \"Please login to access AI question generation features. Your session may have expired or you need enhanced permissions.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 727,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 351,\n    columnNumber: 5\n  }, this);\n}\n_s(QuestionGenerationForm, \"nMbP+V0Jfat1gF8meRKeCkNhaio=\", false, function () {\n  return [useDispatch, Form.useForm, useAIAuth];\n});\n_c = QuestionGenerationForm;\nexport default QuestionGenerationForm;\nvar _c;\n$RefreshReg$(_c, \"QuestionGenerationForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "Card", "Form", "Select", "InputNumber", "<PERSON><PERSON>", "Row", "Col", "Checkbox", "message", "Divider", "<PERSON><PERSON>", "Progress", "FaArrowLeft", "FaRobot", "HideLoading", "ShowLoading", "getAllExams", "generateQuestions", "getSubjectsForLevel", "getSyllabusTopics", "getSyllabusesForAI", "AutoGenerateExamModal", "AILoginModal", "useAIAuth", "jsxDEV", "_jsxDEV", "Option", "QuestionGenerationForm", "onBack", "onSuccess", "_s", "_classOptions$selecte", "dispatch", "form", "useForm", "isAuthenticated", "hasAIAccess", "user", "loading", "authLoading", "requiresUpgrade", "<PERSON><PERSON><PERSON><PERSON>", "handleLoginSuccess", "requireAIAuth", "sessionExpiringSoon", "timeUntilExpiry", "exams", "setExams", "availableSubjects", "setAvailableSubjects", "availableTopics", "setAvailableTopics", "availableSyllabuses", "setAvailableSyllabuses", "selectedLevel", "setSelectedLevel", "selectedClass", "setSelectedClass", "selectedSubjects", "setSelectedSubjects", "selectedSyllabus", "setSelectedSyllabus", "isGenerating", "setIsGenerating", "generationProgress", "setGenerationProgress", "showAutoGenerateModal", "setShowAutoGenerateModal", "showLoginModal", "setShowLoginModal", "fetchExams", "response", "success", "Array", "isArray", "data", "validExams", "filter", "exam", "_id", "error", "handleLevelChange", "level", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "class", "undefined", "subjects", "syllabusTopics", "handleClassChange", "className", "length", "fetchTopicsForSubjects", "handleSubjectsChange", "allTopics", "subject", "subjectTopics", "topics", "map", "topic", "fullName", "topicName", "push", "console", "handleAutoGenerateExamSuccess", "newExam", "updatedExams", "examId", "name", "openAutoGenerateModal", "onFinish", "values", "log", "totalDistribution", "Object", "questionDistribution", "reduce", "sum", "count", "totalQuestions", "warning", "auth<PERSON><PERSON><PERSON>", "reason", "payload", "questionTypes", "difficultyLevels", "userId", "info", "setTimeout", "errorMessage", "code", "includes", "status", "errorData", "requiresLogin", "upgradeRequired", "_error$response$data", "request", "questionTypeOptions", "label", "value", "difficultyOptions", "levelOptions", "classOptions", "primary", "ordinary_secondary", "advanced_secondary", "children", "title", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "description", "showIcon", "size", "style", "marginTop", "action", "percent", "layout", "disabled", "gutter", "xs", "marginBottom", "md", "<PERSON><PERSON>", "extra", "placeholder", "allowClear", "category", "width", "rules", "required", "onChange", "option", "cls", "mode", "Group", "options", "min", "max", "char<PERSON>t", "toUpperCase", "slice", "join", "optionFilterProp", "showSearch", "filterOption", "input", "toLowerCase", "indexOf", "index", "fontSize", "color", "difficulty", "subtopics", "htmlType", "visible", "onCancel", "prefilledData", "userData", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/AIQuestionGeneration/QuestionGenerationForm.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport {\n  Card,\n  Form,\n  Select,\n  InputNumber,\n  Button,\n  Row,\n  Col,\n  Checkbox,\n  message,\n  Divider,\n  Alert,\n  Progress\n} from \"antd\";\nimport { FaArrowLeft, FaRobot } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllExams } from \"../../../apicalls/exams\";\nimport {\n  generateQuestions,\n  getSubjectsForLevel,\n  getSyllabusTopics\n} from \"../../../apicalls/aiQuestions\";\nimport { getSyllabusesForAI } from \"../../../apicalls/syllabus\";\nimport AutoGenerateExamModal from \"./AutoGenerateExamModal\";\nimport AILoginModal from \"../../../components/AILoginModal\";\nimport { useAIAuth } from \"../../../hooks/useAIAuth\";\n\nconst { Option } = Select;\n\nfunction QuestionGenerationForm({ onBack, onSuccess }) {\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n\n  // Enhanced authentication\n  const {\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    loading: authLoading,\n    requiresUpgrade,\n    needsLogin,\n    handleLoginSuccess,\n    requireAIAuth,\n    sessionExpiringSoon,\n    timeUntilExpiry\n  } = useAIAuth();\n\n  const [exams, setExams] = useState([]);\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [availableTopics, setAvailableTopics] = useState([]);\n  const [availableSyllabuses, setAvailableSyllabuses] = useState([]);\n  const [selectedLevel, setSelectedLevel] = useState(\"\");\n  const [selectedClass, setSelectedClass] = useState(\"\");\n  const [selectedSubjects, setSelectedSubjects] = useState([]);\n  const [selectedSyllabus, setSelectedSyllabus] = useState(null);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationProgress, setGenerationProgress] = useState(0);\n  const [showAutoGenerateModal, setShowAutoGenerateModal] = useState(false);\n  const [showLoginModal, setShowLoginModal] = useState(false);\n\n  const fetchExams = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      if (response.success && Array.isArray(response.data)) {\n        // Filter out any null or undefined values\n        const validExams = response.data.filter(exam => exam && exam._id);\n        setExams(validExams);\n      } else {\n        message.error(\"Failed to fetch exams\");\n        setExams([]); // Ensure exams is always an array\n      }\n    } catch (error) {\n      message.error(\"Error fetching exams\");\n      setExams([]); // Ensure exams is always an array\n    } finally {\n      dispatch(HideLoading());\n    }\n  }, [dispatch]);\n\n  useEffect(() => {\n    // Only fetch exams if we have authentication, otherwise let the auth hook handle it\n    if (isAuthenticated && hasAIAccess && !authLoading) {\n      fetchExams();\n    }\n  }, [isAuthenticated, hasAIAccess, authLoading, fetchExams]);\n\n  const handleLevelChange = async (level) => {\n    setSelectedLevel(level);\n    setSelectedClass(\"\");\n    setSelectedSubjects([]);\n    setAvailableTopics([]);\n    form.setFieldsValue({\n      class: undefined,\n      subjects: [],\n      syllabusTopics: []\n    });\n\n    try {\n      const response = await getSubjectsForLevel(level);\n      if (response.success) {\n        setAvailableSubjects(response.data);\n      } else {\n        message.error(\"Failed to fetch subjects\");\n      }\n    } catch (error) {\n      message.error(\"Error fetching subjects\");\n    }\n  };\n\n  const handleClassChange = (className) => {\n    setSelectedClass(className);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If subjects are already selected, fetch topics\n    if (selectedSubjects.length > 0) {\n      fetchTopicsForSubjects(selectedLevel, className, selectedSubjects);\n    }\n  };\n\n  const handleSubjectsChange = (subjects) => {\n    setSelectedSubjects(subjects);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If class is selected, fetch topics\n    if (selectedClass) {\n      fetchTopicsForSubjects(selectedLevel, selectedClass, subjects);\n    }\n\n    // Note: Auto-generate exam functionality moved to modal\n  };\n\n  const fetchTopicsForSubjects = async (level, className, subjects) => {\n    if (!level || !className || subjects.length === 0) return;\n\n    try {\n      const allTopics = [];\n\n      for (const subject of subjects) {\n        const response = await getSyllabusTopics(level, className, subject);\n        if (response.success) {\n          const subjectTopics = response.data.topics.map(topic => ({\n            ...topic,\n            subject: subject,\n            fullName: `${subject}: ${topic.topicName}`,\n          }));\n          allTopics.push(...subjectTopics);\n        }\n      }\n\n      setAvailableTopics(allTopics);\n    } catch (error) {\n      console.error(\"Error fetching topics:\", error);\n      message.error(\"Failed to fetch syllabus topics\");\n    }\n  };\n\n  const handleAutoGenerateExamSuccess = (newExam) => {\n    // Add the new exam to the list and select it\n    if (newExam && newExam._id) {\n      const updatedExams = [...exams, newExam];\n      setExams(updatedExams);\n      form.setFieldsValue({ examId: newExam._id });\n      setShowAutoGenerateModal(false);\n      message.success(`Exam created successfully: ${newExam.name}`);\n    } else {\n      message.error(\"Invalid exam data received\");\n      setShowAutoGenerateModal(false);\n    }\n  };\n\n  const openAutoGenerateModal = () => {\n    setShowAutoGenerateModal(true);\n  };\n\n  const onFinish = async (values) => {\n    console.log(\"🚀 Form submission started\");\n    console.log(\"📝 Form values:\", values);\n\n    try {\n      setIsGenerating(true);\n      setGenerationProgress(10);\n\n      // Validate question distribution\n      const totalDistribution = Object.values(values.questionDistribution || {}).reduce((sum, count) => sum + (count || 0), 0);\n      console.log(\"📊 Total distribution:\", totalDistribution, \"Total questions:\", values.totalQuestions);\n\n      if (totalDistribution !== values.totalQuestions) {\n        console.error(\"❌ Distribution validation failed\");\n        message.error(\"Question distribution must equal total questions\");\n        setIsGenerating(false);\n        return;\n      }\n\n      console.log(\"✅ Distribution validation passed\");\n\n      setGenerationProgress(30);\n\n      // Check authentication and AI access\n      if (!isAuthenticated || !hasAIAccess) {\n        setIsGenerating(false);\n        setShowLoginModal(true);\n        message.warning(\"Please login to access AI question generation features.\");\n        return;\n      }\n\n      // Double-check with server-side validation\n      const authCheck = await requireAIAuth();\n      if (!authCheck.success) {\n        setIsGenerating(false);\n\n        switch (authCheck.reason) {\n          case 'not_authenticated':\n          case 'refresh_failed':\n            setShowLoginModal(true);\n            message.warning(\"Please login to generate AI questions.\");\n            return;\n          case 'no_ai_access':\n            message.error(\"AI features are not available for your account.\");\n            return;\n          case 'requires_upgrade':\n            message.warning(\"AI question generation requires a premium subscription. Please upgrade your account.\");\n            return;\n          default:\n            setShowLoginModal(true);\n            message.warning(\"Authentication check failed. Please login again.\");\n            return;\n        }\n      }\n\n      const payload = {\n        examId: values.examId,\n        questionTypes: values.questionTypes,\n        subjects: values.subjects,\n        level: values.level,\n        class: values.class,\n        difficultyLevels: values.difficultyLevels,\n        syllabusTopics: values.syllabusTopics || [],\n        totalQuestions: values.totalQuestions,\n        questionDistribution: values.questionDistribution,\n        userId: user._id,\n      };\n\n      console.log(\"📤 Sending payload:\", payload);\n\n      setGenerationProgress(50);\n\n      console.log(\"🌐 Making API call to generate questions...\");\n\n      // Show progress message to user\n      message.info(\"AI is generating your questions... This may take a few minutes.\", 5);\n\n      const response = await generateQuestions(payload);\n      console.log(\"📥 API response received:\", response);\n\n      setGenerationProgress(90);\n\n      if (response.success) {\n        setGenerationProgress(100);\n        message.success(\"Questions generated successfully!\");\n        setTimeout(() => {\n          onSuccess();\n        }, 1000);\n      } else {\n        message.error(response.message || \"Failed to generate questions\");\n      }\n    } catch (error) {\n      console.error(\"Question generation error:\", error);\n\n      // More detailed error handling\n      let errorMessage = \"Error generating questions\";\n\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n        // Handle timeout errors specifically\n        errorMessage = \"Question generation is taking longer than expected. This might be due to high server load. Please try again with fewer questions or check your internet connection.\";\n      } else if (error.response) {\n        // Server responded with error status\n        console.error(\"Server error response:\", error.response.data);\n        console.error(\"Server error status:\", error.response.status);\n\n        if (error.response.status === 401) {\n          // Handle authentication errors for AI requests\n          const errorData = error.response.data;\n\n          if (errorData?.requiresLogin) {\n            // Show the AI login modal instead of redirecting\n            setShowLoginModal(true);\n            errorMessage = errorData.message || \"Authentication required for AI features.\";\n          } else {\n            errorMessage = errorData?.message || \"Authentication failed. Please login again.\";\n          }\n        } else if (error.response.status === 403) {\n          // Handle permission/subscription errors\n          const errorData = error.response.data;\n          if (errorData?.upgradeRequired) {\n            errorMessage = \"AI question generation requires a premium subscription. Please upgrade your account.\";\n          } else {\n            errorMessage = errorData?.message || \"Access denied for AI features.\";\n          }\n        } else if (error.response.status === 504 || error.response.status === 502) {\n          errorMessage = \"Server timeout. The AI generation process is taking longer than expected. Please try again with fewer questions.\";\n        } else {\n          errorMessage = error.response.data?.message || `Server error: ${error.response.status}`;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        console.error(\"Network error:\", error.request);\n        errorMessage = \"Network error - please check your connection. If the problem persists, try generating fewer questions at once.\";\n      } else {\n        // Something else happened\n        console.error(\"Error:\", error.message);\n        errorMessage = error.message || \"Unknown error occurred\";\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setIsGenerating(false);\n      setGenerationProgress(0);\n    }\n  };\n\n  const questionTypeOptions = [\n    { label: \"Multiple Choice\", value: \"multiple_choice\" },\n    { label: \"Fill in the Blank\", value: \"fill_blank\" },\n    { label: \"Picture-based\", value: \"picture_based\" },\n  ];\n\n  const difficultyOptions = [\n    { label: \"Easy\", value: \"easy\" },\n    { label: \"Medium\", value: \"medium\" },\n    { label: \"Hard\", value: \"hard\" },\n  ];\n\n  const levelOptions = [\n    { label: \"Primary Education (Standards I-VI)\", value: \"primary\" },\n    { label: \"Ordinary Secondary (Forms I-IV)\", value: \"ordinary_secondary\" },\n    { label: \"Advanced Secondary (Forms V-VI)\", value: \"advanced_secondary\" },\n  ];\n\n  const classOptions = {\n    primary: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\"], // Standards I-VI (TIE Primary)\n    ordinary_secondary: [\"I\", \"II\", \"III\", \"IV\"], // Forms I-IV (TIE Ordinary Secondary)\n    advanced_secondary: [\"V\", \"VI\"], // Forms V-VI (TIE Advanced Secondary)\n  };\n\n  return (\n    <div className=\"question-generation-form\">\n      <Card\n        title={\n          <div className=\"form-header\">\n            <Button\n              type=\"text\"\n              icon={<FaArrowLeft />}\n              onClick={onBack}\n              className=\"back-button\"\n            >\n              Back to Dashboard\n            </Button>\n            <div className=\"title-section\">\n              <FaRobot className=\"title-icon\" />\n              <span>Generate AI Questions</span>\n            </div>\n          </div>\n        }\n      >\n        {/* Authentication Status */}\n        {authLoading ? (\n          <Alert\n            message=\"Checking Authentication...\"\n            description=\"Verifying your access to AI features.\"\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !isAuthenticated ? (\n          <Alert\n            message=\"Login Required\"\n            description={\n              <div>\n                <p>Please login to access AI question generation features.</p>\n                <Button\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => setShowLoginModal(true)}\n                  style={{ marginTop: 8 }}\n                >\n                  Login Now\n                </Button>\n              </div>\n            }\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !hasAIAccess ? (\n          <Alert\n            message={requiresUpgrade ? \"Upgrade Required\" : \"AI Access Restricted\"}\n            description={\n              requiresUpgrade\n                ? \"AI question generation requires a premium subscription. Please upgrade your account.\"\n                : \"AI features are not available for your account. Please contact support.\"\n            }\n            type=\"error\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : sessionExpiringSoon ? (\n          <Alert\n            message=\"Session Expiring Soon\"\n            description={`Your session will expire in ${timeUntilExpiry}. Consider refreshing your login.`}\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n            action={\n              <Button\n                size=\"small\"\n                onClick={() => setShowLoginModal(true)}\n              >\n                Refresh Login\n              </Button>\n            }\n          />\n        ) : (\n          <Alert\n            message=\"AI Features Ready\"\n            description={`Welcome ${user?.name}! You have full access to AI question generation.`}\n            type=\"success\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        {isGenerating && (\n          <Alert\n            message=\"Generating Questions\"\n            description={\n              <div>\n                <p>AI is generating your questions. This may take a few moments...</p>\n                <Progress percent={generationProgress} status=\"active\" />\n              </div>\n            }\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={onFinish}\n          disabled={isGenerating || !hasAIAccess || authLoading}\n        >\n          <Row gutter={[16, 16]}>\n            <Col xs={24}>\n              <Alert\n                message=\"Exam Selection\"\n                description=\"You can either select an existing exam or create a new one using the auto-generate feature. Questions can also be generated independently without an exam.\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 16 }}\n              />\n            </Col>\n\n            <Col xs={24} md={16}>\n              <Form.Item\n                name=\"examId\"\n                label=\"Target Exam (Optional)\"\n                extra=\"Leave empty to generate standalone questions, or select an existing exam\"\n              >\n                <Select\n                  placeholder=\"Optional: Choose an existing exam\"\n                  allowClear\n                >\n                  {exams && exams.length > 0 && exams.map((exam) => (\n                    exam && exam._id ? (\n                      <Option key={exam._id} value={exam._id}>\n                        {exam.name} - {exam.category}\n                      </Option>\n                    ) : null\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item label=\"Or Create New Exam\">\n                <Button\n                  type=\"dashed\"\n                  icon={<FaRobot />}\n                  onClick={openAutoGenerateModal}\n                  style={{ width: \"100%\" }}\n                  disabled={isGenerating || !hasAIAccess || authLoading}\n                >\n                  Auto-Generate New Exam\n                </Button>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"level\"\n                label=\"Education Level\"\n                rules={[{ required: true, message: \"Please select a level\" }]}\n              >\n                <Select \n                  placeholder=\"Choose education level\"\n                  onChange={handleLevelChange}\n                >\n                  {levelOptions.map((option) => (\n                    <Option key={option.value} value={option.value}>\n                      {option.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"class\"\n                label=\"Class\"\n                rules={[{ required: true, message: \"Please select a class\" }]}\n              >\n                <Select\n                  placeholder=\"Choose class\"\n                  disabled={!selectedLevel}\n                  onChange={handleClassChange}\n                >\n                  {selectedLevel && classOptions[selectedLevel]?.map((cls) => (\n                    <Option key={cls} value={cls}>\n                      Class {cls}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"subjects\"\n                label=\"Subjects\"\n                rules={[{ required: true, message: \"Please select at least one subject\" }]}\n              >\n                <Select\n                  mode=\"multiple\"\n                  placeholder=\"Choose subjects\"\n                  disabled={!selectedLevel}\n                  onChange={handleSubjectsChange}\n                >\n                  {availableSubjects.map((subject) => (\n                    <Option key={subject} value={subject}>\n                      {subject}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"questionTypes\"\n                label=\"Question Types\"\n                rules={[{ required: true, message: \"Please select at least one question type\" }]}\n              >\n                <Checkbox.Group options={questionTypeOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"difficultyLevels\"\n                label=\"Difficulty Levels\"\n                rules={[{ required: true, message: \"Please select at least one difficulty level\" }]}\n              >\n                <Checkbox.Group options={difficultyOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"totalQuestions\"\n                label=\"Total Questions\"\n                rules={[\n                  { required: true, message: \"Please enter total questions\" },\n                  { type: \"number\", min: 1, max: 50, message: \"Must be between 1 and 50\" }\n                ]}\n              >\n                <InputNumber\n                  min={1}\n                  max={50}\n                  placeholder=\"Enter total questions\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Divider>Question Distribution</Divider>\n\n          {selectedLevel && selectedClass && selectedSubjects.length > 0 && (\n            <Alert\n              message=\"Tanzania Syllabus Information\"\n              description={\n                <div>\n                  <p><strong>Level:</strong> {selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</p>\n                  <p><strong>Class:</strong> {selectedClass}</p>\n                  <p><strong>Subjects:</strong> {selectedSubjects.join(\", \")}</p>\n                  <p><strong>Available Topics:</strong> {availableTopics.length} topics from Tanzania National Curriculum</p>\n                  <p><strong>Auto-generate:</strong> Use the button above to create a new exam with proper structure</p>\n                </div>\n              }\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n          )}\n\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"multiple_choice\"]}\n                label=\"Multiple Choice\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"fill_blank\"]}\n                label=\"Fill in the Blank\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"picture_based\"]}\n                label=\"Picture-based\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"syllabusTopics\"\n            label={`Tanzania Syllabus Topics (${availableTopics.length} available)`}\n            extra={availableTopics.length === 0 ? \"Select level, class, and subjects to see available topics\" : \"Select specific topics from Tanzania National Curriculum\"}\n          >\n            <Select\n              mode=\"multiple\"\n              placeholder={availableTopics.length === 0 ? \"No topics available - select level, class, and subjects first\" : \"Choose specific topics from Tanzania syllabus\"}\n              style={{ width: \"100%\" }}\n              disabled={availableTopics.length === 0}\n              optionFilterProp=\"children\"\n              showSearch\n              filterOption={(input, option) =>\n                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0\n              }\n            >\n              {availableTopics.map((topic, index) => (\n                <Option key={`${topic.subject}-${topic.topicName}-${index}`} value={topic.topicName}>\n                  <div>\n                    <strong>{topic.topicName}</strong>\n                    <div style={{ fontSize: \"12px\", color: \"#666\" }}>\n                      {topic.subject} • Difficulty: {topic.difficulty}\n                    </div>\n                    {topic.subtopics && topic.subtopics.length > 0 && (\n                      <div style={{ fontSize: \"11px\", color: \"#999\" }}>\n                        Subtopics: {topic.subtopics.slice(0, 3).join(\", \")}\n                        {topic.subtopics.length > 3 && ` +${topic.subtopics.length - 3} more`}\n                      </div>\n                    )}\n                  </div>\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <div className=\"form-actions\">\n            <Button onClick={onBack} disabled={isGenerating}>\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={isGenerating}\n              disabled={!hasAIAccess || authLoading}\n              icon={<FaRobot />}\n            >\n              {isGenerating ? \"Generating...\" : !hasAIAccess ? \"Login Required\" : \"Generate Questions\"}\n            </Button>\n          </div>\n        </Form>\n      </Card>\n\n      <AutoGenerateExamModal\n        visible={showAutoGenerateModal}\n        onCancel={() => setShowAutoGenerateModal(false)}\n        onSuccess={handleAutoGenerateExamSuccess}\n        prefilledData={{\n          level: selectedLevel,\n          class: selectedClass,\n          subjects: selectedSubjects,\n        }}\n      />\n\n      <AILoginModal\n        visible={showLoginModal}\n        onCancel={() => setShowLoginModal(false)}\n        onSuccess={(userData) => {\n          handleLoginSuccess(userData);\n          setShowLoginModal(false);\n        }}\n        title=\"AI Features Login Required\"\n        description=\"Please login to access AI question generation features. Your session may have expired or you need enhanced permissions.\"\n      />\n    </div>\n  );\n}\n\nexport default QuestionGenerationForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,KAAK,EACLC,QAAQ,QACH,MAAM;AACb,SAASC,WAAW,EAAEC,OAAO,QAAQ,gBAAgB;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SACEC,iBAAiB,EACjBC,mBAAmB,EACnBC,iBAAiB,QACZ,+BAA+B;AACtC,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,SAASC,SAAS,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAM;EAAEC;AAAO,CAAC,GAAGxB,MAAM;AAEzB,SAASyB,sBAAsBA,CAAC;EAAEC,MAAM;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACrD,MAAMC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAM;IACJC,eAAe;IACfC,WAAW;IACXC,IAAI;IACJC,OAAO,EAAEC,WAAW;IACpBC,eAAe;IACfC,UAAU;IACVC,kBAAkB;IAClBC,aAAa;IACbC,mBAAmB;IACnBC;EACF,CAAC,GAAGtB,SAAS,CAAC,CAAC;EAEf,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACsE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM0E,UAAU,GAAGxE,WAAW,CAAC,YAAY;IACzC,IAAI;MACFkC,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMwD,QAAQ,GAAG,MAAMvD,WAAW,CAAC,CAAC;MACpC,IAAIuD,QAAQ,CAACC,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAACI,IAAI,CAAC,EAAE;QACpD;QACA,MAAMC,UAAU,GAAGL,QAAQ,CAACI,IAAI,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC;QACjEhC,QAAQ,CAAC6B,UAAU,CAAC;MACtB,CAAC,MAAM;QACLpE,OAAO,CAACwE,KAAK,CAAC,uBAAuB,CAAC;QACtCjC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdxE,OAAO,CAACwE,KAAK,CAAC,sBAAsB,CAAC;MACrCjC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,SAAS;MACRf,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACkB,QAAQ,CAAC,CAAC;EAEdnC,SAAS,CAAC,MAAM;IACd;IACA,IAAIsC,eAAe,IAAIC,WAAW,IAAI,CAACG,WAAW,EAAE;MAClD+B,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACnC,eAAe,EAAEC,WAAW,EAAEG,WAAW,EAAE+B,UAAU,CAAC,CAAC;EAE3D,MAAMW,iBAAiB,GAAG,MAAOC,KAAK,IAAK;IACzC3B,gBAAgB,CAAC2B,KAAK,CAAC;IACvBzB,gBAAgB,CAAC,EAAE,CAAC;IACpBE,mBAAmB,CAAC,EAAE,CAAC;IACvBR,kBAAkB,CAAC,EAAE,CAAC;IACtBlB,IAAI,CAACkD,cAAc,CAAC;MAClBC,KAAK,EAAEC,SAAS;MAChBC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE;IAClB,CAAC,CAAC;IAEF,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMrD,mBAAmB,CAACgE,KAAK,CAAC;MACjD,IAAIX,QAAQ,CAACC,OAAO,EAAE;QACpBvB,oBAAoB,CAACsB,QAAQ,CAACI,IAAI,CAAC;MACrC,CAAC,MAAM;QACLnE,OAAO,CAACwE,KAAK,CAAC,0BAA0B,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdxE,OAAO,CAACwE,KAAK,CAAC,yBAAyB,CAAC;IAC1C;EACF,CAAC;EAED,MAAMQ,iBAAiB,GAAIC,SAAS,IAAK;IACvChC,gBAAgB,CAACgC,SAAS,CAAC;IAC3BtC,kBAAkB,CAAC,EAAE,CAAC;IACtBlB,IAAI,CAACkD,cAAc,CAAC;MAAEI,cAAc,EAAE;IAAG,CAAC,CAAC;;IAE3C;IACA,IAAI7B,gBAAgB,CAACgC,MAAM,GAAG,CAAC,EAAE;MAC/BC,sBAAsB,CAACrC,aAAa,EAAEmC,SAAS,EAAE/B,gBAAgB,CAAC;IACpE;EACF,CAAC;EAED,MAAMkC,oBAAoB,GAAIN,QAAQ,IAAK;IACzC3B,mBAAmB,CAAC2B,QAAQ,CAAC;IAC7BnC,kBAAkB,CAAC,EAAE,CAAC;IACtBlB,IAAI,CAACkD,cAAc,CAAC;MAAEI,cAAc,EAAE;IAAG,CAAC,CAAC;;IAE3C;IACA,IAAI/B,aAAa,EAAE;MACjBmC,sBAAsB,CAACrC,aAAa,EAAEE,aAAa,EAAE8B,QAAQ,CAAC;IAChE;;IAEA;EACF,CAAC;;EAED,MAAMK,sBAAsB,GAAG,MAAAA,CAAOT,KAAK,EAAEO,SAAS,EAAEH,QAAQ,KAAK;IACnE,IAAI,CAACJ,KAAK,IAAI,CAACO,SAAS,IAAIH,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;IAEnD,IAAI;MACF,MAAMG,SAAS,GAAG,EAAE;MAEpB,KAAK,MAAMC,OAAO,IAAIR,QAAQ,EAAE;QAC9B,MAAMf,QAAQ,GAAG,MAAMpD,iBAAiB,CAAC+D,KAAK,EAAEO,SAAS,EAAEK,OAAO,CAAC;QACnE,IAAIvB,QAAQ,CAACC,OAAO,EAAE;UACpB,MAAMuB,aAAa,GAAGxB,QAAQ,CAACI,IAAI,CAACqB,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;YACvD,GAAGA,KAAK;YACRJ,OAAO,EAAEA,OAAO;YAChBK,QAAQ,EAAG,GAAEL,OAAQ,KAAII,KAAK,CAACE,SAAU;UAC3C,CAAC,CAAC,CAAC;UACHP,SAAS,CAACQ,IAAI,CAAC,GAAGN,aAAa,CAAC;QAClC;MACF;MAEA5C,kBAAkB,CAAC0C,SAAS,CAAC;IAC/B,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CxE,OAAO,CAACwE,KAAK,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC;EAED,MAAMuB,6BAA6B,GAAIC,OAAO,IAAK;IACjD;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACzB,GAAG,EAAE;MAC1B,MAAM0B,YAAY,GAAG,CAAC,GAAG3D,KAAK,EAAE0D,OAAO,CAAC;MACxCzD,QAAQ,CAAC0D,YAAY,CAAC;MACtBxE,IAAI,CAACkD,cAAc,CAAC;QAAEuB,MAAM,EAAEF,OAAO,CAACzB;MAAI,CAAC,CAAC;MAC5CZ,wBAAwB,CAAC,KAAK,CAAC;MAC/B3D,OAAO,CAACgE,OAAO,CAAE,8BAA6BgC,OAAO,CAACG,IAAK,EAAC,CAAC;IAC/D,CAAC,MAAM;MACLnG,OAAO,CAACwE,KAAK,CAAC,4BAA4B,CAAC;MAC3Cb,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAMyC,qBAAqB,GAAGA,CAAA,KAAM;IAClCzC,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAM0C,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjCR,OAAO,CAACS,GAAG,CAAC,4BAA4B,CAAC;IACzCT,OAAO,CAACS,GAAG,CAAC,iBAAiB,EAAED,MAAM,CAAC;IAEtC,IAAI;MACF/C,eAAe,CAAC,IAAI,CAAC;MACrBE,qBAAqB,CAAC,EAAE,CAAC;;MAEzB;MACA,MAAM+C,iBAAiB,GAAGC,MAAM,CAACH,MAAM,CAACA,MAAM,CAACI,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAIC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MACxHf,OAAO,CAACS,GAAG,CAAC,wBAAwB,EAAEC,iBAAiB,EAAE,kBAAkB,EAAEF,MAAM,CAACQ,cAAc,CAAC;MAEnG,IAAIN,iBAAiB,KAAKF,MAAM,CAACQ,cAAc,EAAE;QAC/ChB,OAAO,CAACtB,KAAK,CAAC,kCAAkC,CAAC;QACjDxE,OAAO,CAACwE,KAAK,CAAC,kDAAkD,CAAC;QACjEjB,eAAe,CAAC,KAAK,CAAC;QACtB;MACF;MAEAuC,OAAO,CAACS,GAAG,CAAC,kCAAkC,CAAC;MAE/C9C,qBAAqB,CAAC,EAAE,CAAC;;MAEzB;MACA,IAAI,CAAC9B,eAAe,IAAI,CAACC,WAAW,EAAE;QACpC2B,eAAe,CAAC,KAAK,CAAC;QACtBM,iBAAiB,CAAC,IAAI,CAAC;QACvB7D,OAAO,CAAC+G,OAAO,CAAC,yDAAyD,CAAC;QAC1E;MACF;;MAEA;MACA,MAAMC,SAAS,GAAG,MAAM7E,aAAa,CAAC,CAAC;MACvC,IAAI,CAAC6E,SAAS,CAAChD,OAAO,EAAE;QACtBT,eAAe,CAAC,KAAK,CAAC;QAEtB,QAAQyD,SAAS,CAACC,MAAM;UACtB,KAAK,mBAAmB;UACxB,KAAK,gBAAgB;YACnBpD,iBAAiB,CAAC,IAAI,CAAC;YACvB7D,OAAO,CAAC+G,OAAO,CAAC,wCAAwC,CAAC;YACzD;UACF,KAAK,cAAc;YACjB/G,OAAO,CAACwE,KAAK,CAAC,iDAAiD,CAAC;YAChE;UACF,KAAK,kBAAkB;YACrBxE,OAAO,CAAC+G,OAAO,CAAC,sFAAsF,CAAC;YACvG;UACF;YACElD,iBAAiB,CAAC,IAAI,CAAC;YACvB7D,OAAO,CAAC+G,OAAO,CAAC,kDAAkD,CAAC;YACnE;QACJ;MACF;MAEA,MAAMG,OAAO,GAAG;QACdhB,MAAM,EAAEI,MAAM,CAACJ,MAAM;QACrBiB,aAAa,EAAEb,MAAM,CAACa,aAAa;QACnCrC,QAAQ,EAAEwB,MAAM,CAACxB,QAAQ;QACzBJ,KAAK,EAAE4B,MAAM,CAAC5B,KAAK;QACnBE,KAAK,EAAE0B,MAAM,CAAC1B,KAAK;QACnBwC,gBAAgB,EAAEd,MAAM,CAACc,gBAAgB;QACzCrC,cAAc,EAAEuB,MAAM,CAACvB,cAAc,IAAI,EAAE;QAC3C+B,cAAc,EAAER,MAAM,CAACQ,cAAc;QACrCJ,oBAAoB,EAAEJ,MAAM,CAACI,oBAAoB;QACjDW,MAAM,EAAExF,IAAI,CAAC0C;MACf,CAAC;MAEDuB,OAAO,CAACS,GAAG,CAAC,qBAAqB,EAAEW,OAAO,CAAC;MAE3CzD,qBAAqB,CAAC,EAAE,CAAC;MAEzBqC,OAAO,CAACS,GAAG,CAAC,6CAA6C,CAAC;;MAE1D;MACAvG,OAAO,CAACsH,IAAI,CAAC,iEAAiE,EAAE,CAAC,CAAC;MAElF,MAAMvD,QAAQ,GAAG,MAAMtD,iBAAiB,CAACyG,OAAO,CAAC;MACjDpB,OAAO,CAACS,GAAG,CAAC,2BAA2B,EAAExC,QAAQ,CAAC;MAElDN,qBAAqB,CAAC,EAAE,CAAC;MAEzB,IAAIM,QAAQ,CAACC,OAAO,EAAE;QACpBP,qBAAqB,CAAC,GAAG,CAAC;QAC1BzD,OAAO,CAACgE,OAAO,CAAC,mCAAmC,CAAC;QACpDuD,UAAU,CAAC,MAAM;UACflG,SAAS,CAAC,CAAC;QACb,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLrB,OAAO,CAACwE,KAAK,CAACT,QAAQ,CAAC/D,OAAO,IAAI,8BAA8B,CAAC;MACnE;IACF,CAAC,CAAC,OAAOwE,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAElD;MACA,IAAIgD,YAAY,GAAG,4BAA4B;MAE/C,IAAIhD,KAAK,CAACiD,IAAI,KAAK,cAAc,IAAIjD,KAAK,CAACxE,OAAO,CAAC0H,QAAQ,CAAC,SAAS,CAAC,EAAE;QACtE;QACAF,YAAY,GAAG,qKAAqK;MACtL,CAAC,MAAM,IAAIhD,KAAK,CAACT,QAAQ,EAAE;QACzB;QACA+B,OAAO,CAACtB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAACT,QAAQ,CAACI,IAAI,CAAC;QAC5D2B,OAAO,CAACtB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACT,QAAQ,CAAC4D,MAAM,CAAC;QAE5D,IAAInD,KAAK,CAACT,QAAQ,CAAC4D,MAAM,KAAK,GAAG,EAAE;UACjC;UACA,MAAMC,SAAS,GAAGpD,KAAK,CAACT,QAAQ,CAACI,IAAI;UAErC,IAAIyD,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEC,aAAa,EAAE;YAC5B;YACAhE,iBAAiB,CAAC,IAAI,CAAC;YACvB2D,YAAY,GAAGI,SAAS,CAAC5H,OAAO,IAAI,0CAA0C;UAChF,CAAC,MAAM;YACLwH,YAAY,GAAG,CAAAI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE5H,OAAO,KAAI,4CAA4C;UACnF;QACF,CAAC,MAAM,IAAIwE,KAAK,CAACT,QAAQ,CAAC4D,MAAM,KAAK,GAAG,EAAE;UACxC;UACA,MAAMC,SAAS,GAAGpD,KAAK,CAACT,QAAQ,CAACI,IAAI;UACrC,IAAIyD,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEE,eAAe,EAAE;YAC9BN,YAAY,GAAG,sFAAsF;UACvG,CAAC,MAAM;YACLA,YAAY,GAAG,CAAAI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE5H,OAAO,KAAI,gCAAgC;UACvE;QACF,CAAC,MAAM,IAAIwE,KAAK,CAACT,QAAQ,CAAC4D,MAAM,KAAK,GAAG,IAAInD,KAAK,CAACT,QAAQ,CAAC4D,MAAM,KAAK,GAAG,EAAE;UACzEH,YAAY,GAAG,kHAAkH;QACnI,CAAC,MAAM;UAAA,IAAAO,oBAAA;UACLP,YAAY,GAAG,EAAAO,oBAAA,GAAAvD,KAAK,CAACT,QAAQ,CAACI,IAAI,cAAA4D,oBAAA,uBAAnBA,oBAAA,CAAqB/H,OAAO,KAAK,iBAAgBwE,KAAK,CAACT,QAAQ,CAAC4D,MAAO,EAAC;QACzF;MACF,CAAC,MAAM,IAAInD,KAAK,CAACwD,OAAO,EAAE;QACxB;QACAlC,OAAO,CAACtB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACwD,OAAO,CAAC;QAC9CR,YAAY,GAAG,gHAAgH;MACjI,CAAC,MAAM;QACL;QACA1B,OAAO,CAACtB,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAACxE,OAAO,CAAC;QACtCwH,YAAY,GAAGhD,KAAK,CAACxE,OAAO,IAAI,wBAAwB;MAC1D;MAEAA,OAAO,CAACwE,KAAK,CAACgD,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRjE,eAAe,CAAC,KAAK,CAAC;MACtBE,qBAAqB,CAAC,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,MAAMwE,mBAAmB,GAAG,CAC1B;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAED,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAa,CAAC,EACnD;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC,CACnD;EAED,MAAMC,iBAAiB,GAAG,CACxB;IAAEF,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,CACjC;EAED,MAAME,YAAY,GAAG,CACnB;IAAEH,KAAK,EAAE,oCAAoC;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjE;IAAED,KAAK,EAAE,iCAAiC;IAAEC,KAAK,EAAE;EAAqB,CAAC,EACzE;IAAED,KAAK,EAAE,iCAAiC;IAAEC,KAAK,EAAE;EAAqB,CAAC,CAC1E;EAED,MAAMG,YAAY,GAAG;IACnBC,OAAO,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;IAAE;IAC9CC,kBAAkB,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAAE;IAC9CC,kBAAkB,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAE;EACnC,CAAC;;EAED,oBACExH,OAAA;IAAKgE,SAAS,EAAC,0BAA0B;IAAAyD,QAAA,gBACvCzH,OAAA,CAACzB,IAAI;MACHmJ,KAAK,eACH1H,OAAA;QAAKgE,SAAS,EAAC,aAAa;QAAAyD,QAAA,gBAC1BzH,OAAA,CAACrB,MAAM;UACLgJ,IAAI,EAAC,MAAM;UACXC,IAAI,eAAE5H,OAAA,CAACb,WAAW;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBC,OAAO,EAAE9H,MAAO;UAChB6D,SAAS,EAAC,aAAa;UAAAyD,QAAA,EACxB;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA;UAAKgE,SAAS,EAAC,eAAe;UAAAyD,QAAA,gBAC5BzH,OAAA,CAACZ,OAAO;YAAC4E,SAAS,EAAC;UAAY;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClChI,OAAA;YAAAyH,QAAA,EAAM;UAAqB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;MAAAP,QAAA,GAGA3G,WAAW,gBACVd,OAAA,CAACf,KAAK;QACJF,OAAO,EAAC,4BAA4B;QACpCmJ,WAAW,EAAC,uCAAuC;QACnDP,IAAI,EAAC,MAAM;QACXQ,QAAQ;QACRnE,SAAS,EAAC;MAAM;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACA,CAACtH,eAAe,gBAClBV,OAAA,CAACf,KAAK;QACJF,OAAO,EAAC,gBAAgB;QACxBmJ,WAAW,eACTlI,OAAA;UAAAyH,QAAA,gBACEzH,OAAA;YAAAyH,QAAA,EAAG;UAAuD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9DhI,OAAA,CAACrB,MAAM;YACLgJ,IAAI,EAAC,SAAS;YACdS,IAAI,EAAC,OAAO;YACZH,OAAO,EAAEA,CAAA,KAAMrF,iBAAiB,CAAC,IAAI,CAAE;YACvCyF,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAE,CAAE;YAAAb,QAAA,EACzB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;QACDL,IAAI,EAAC,SAAS;QACdQ,QAAQ;QACRnE,SAAS,EAAC;MAAM;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACA,CAACrH,WAAW,gBACdX,OAAA,CAACf,KAAK;QACJF,OAAO,EAAEgC,eAAe,GAAG,kBAAkB,GAAG,sBAAuB;QACvEmH,WAAW,EACTnH,eAAe,GACX,sFAAsF,GACtF,yEACL;QACD4G,IAAI,EAAC,OAAO;QACZQ,QAAQ;QACRnE,SAAS,EAAC;MAAM;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACA7G,mBAAmB,gBACrBnB,OAAA,CAACf,KAAK;QACJF,OAAO,EAAC,uBAAuB;QAC/BmJ,WAAW,EAAG,+BAA8B9G,eAAgB,mCAAmC;QAC/FuG,IAAI,EAAC,SAAS;QACdQ,QAAQ;QACRnE,SAAS,EAAC,MAAM;QAChBuE,MAAM,eACJvI,OAAA,CAACrB,MAAM;UACLyJ,IAAI,EAAC,OAAO;UACZH,OAAO,EAAEA,CAAA,KAAMrF,iBAAiB,CAAC,IAAI,CAAE;UAAA6E,QAAA,EACxC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAEFhI,OAAA,CAACf,KAAK;QACJF,OAAO,EAAC,mBAAmB;QAC3BmJ,WAAW,EAAG,WAAUtH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,IAAK,mDAAmD;QACtFyC,IAAI,EAAC,SAAS;QACdQ,QAAQ;QACRnE,SAAS,EAAC;MAAM;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF,EAEA3F,YAAY,iBACXrC,OAAA,CAACf,KAAK;QACJF,OAAO,EAAC,sBAAsB;QAC9BmJ,WAAW,eACTlI,OAAA;UAAAyH,QAAA,gBACEzH,OAAA;YAAAyH,QAAA,EAAG;UAA+D;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtEhI,OAAA,CAACd,QAAQ;YAACsJ,OAAO,EAAEjG,kBAAmB;YAACmE,MAAM,EAAC;UAAQ;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACN;QACDL,IAAI,EAAC,MAAM;QACXQ,QAAQ;QACRnE,SAAS,EAAC;MAAM;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF,eAEDhI,OAAA,CAACxB,IAAI;QACHgC,IAAI,EAAEA,IAAK;QACXiI,MAAM,EAAC,UAAU;QACjBrD,QAAQ,EAAEA,QAAS;QACnBsD,QAAQ,EAAErG,YAAY,IAAI,CAAC1B,WAAW,IAAIG,WAAY;QAAA2G,QAAA,gBAEtDzH,OAAA,CAACpB,GAAG;UAAC+J,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAlB,QAAA,gBACpBzH,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAAAnB,QAAA,eACVzH,OAAA,CAACf,KAAK;cACJF,OAAO,EAAC,gBAAgB;cACxBmJ,WAAW,EAAC,4JAA4J;cACxKP,IAAI,EAAC,MAAM;cACXQ,QAAQ;cACRE,KAAK,EAAE;gBAAEQ,YAAY,EAAE;cAAG;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhI,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArB,QAAA,eAClBzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cACR7D,IAAI,EAAC,QAAQ;cACb+B,KAAK,EAAC,wBAAwB;cAC9B+B,KAAK,EAAC,0EAA0E;cAAAvB,QAAA,eAEhFzH,OAAA,CAACvB,MAAM;gBACLwK,WAAW,EAAC,mCAAmC;gBAC/CC,UAAU;gBAAAzB,QAAA,EAETpG,KAAK,IAAIA,KAAK,CAAC4C,MAAM,GAAG,CAAC,IAAI5C,KAAK,CAACmD,GAAG,CAAEnB,IAAI,IAC3CA,IAAI,IAAIA,IAAI,CAACC,GAAG,gBACdtD,OAAA,CAACC,MAAM;kBAAgBiH,KAAK,EAAE7D,IAAI,CAACC,GAAI;kBAAAmE,QAAA,GACpCpE,IAAI,CAAC6B,IAAI,EAAC,KAAG,EAAC7B,IAAI,CAAC8F,QAAQ;gBAAA,GADjB9F,IAAI,CAACC,GAAG;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CAAC,GACP,IACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENhI,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAArB,QAAA,eACjBzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cAAC9B,KAAK,EAAC,oBAAoB;cAAAQ,QAAA,eACnCzH,OAAA,CAACrB,MAAM;gBACLgJ,IAAI,EAAC,QAAQ;gBACbC,IAAI,eAAE5H,OAAA,CAACZ,OAAO;kBAAAyI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAClBC,OAAO,EAAE9C,qBAAsB;gBAC/BkD,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO,CAAE;gBACzBV,QAAQ,EAAErG,YAAY,IAAI,CAAC1B,WAAW,IAAIG,WAAY;gBAAA2G,QAAA,EACvD;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENhI,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArB,QAAA,eAClBzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cACR7D,IAAI,EAAC,OAAO;cACZ+B,KAAK,EAAC,iBAAiB;cACvBoC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAAwB,CAAC,CAAE;cAAA0I,QAAA,eAE9DzH,OAAA,CAACvB,MAAM;gBACLwK,WAAW,EAAC,wBAAwB;gBACpCM,QAAQ,EAAE/F,iBAAkB;gBAAAiE,QAAA,EAE3BL,YAAY,CAAC5C,GAAG,CAAEgF,MAAM,iBACvBxJ,OAAA,CAACC,MAAM;kBAAoBiH,KAAK,EAAEsC,MAAM,CAACtC,KAAM;kBAAAO,QAAA,EAC5C+B,MAAM,CAACvC;gBAAK,GADFuC,MAAM,CAACtC,KAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENhI,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArB,QAAA,eAClBzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cACR7D,IAAI,EAAC,OAAO;cACZ+B,KAAK,EAAC,OAAO;cACboC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAAwB,CAAC,CAAE;cAAA0I,QAAA,eAE9DzH,OAAA,CAACvB,MAAM;gBACLwK,WAAW,EAAC,cAAc;gBAC1BP,QAAQ,EAAE,CAAC7G,aAAc;gBACzB0H,QAAQ,EAAExF,iBAAkB;gBAAA0D,QAAA,EAE3B5F,aAAa,MAAAvB,qBAAA,GAAI+G,YAAY,CAACxF,aAAa,CAAC,cAAAvB,qBAAA,uBAA3BA,qBAAA,CAA6BkE,GAAG,CAAEiF,GAAG,iBACrDzJ,OAAA,CAACC,MAAM;kBAAWiH,KAAK,EAAEuC,GAAI;kBAAAhC,QAAA,GAAC,QACtB,EAACgC,GAAG;gBAAA,GADCA,GAAG;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENhI,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,EAAG;YAAArB,QAAA,eAClBzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cACR7D,IAAI,EAAC,UAAU;cACf+B,KAAK,EAAC,UAAU;cAChBoC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAAqC,CAAC,CAAE;cAAA0I,QAAA,eAE3EzH,OAAA,CAACvB,MAAM;gBACLiL,IAAI,EAAC,UAAU;gBACfT,WAAW,EAAC,iBAAiB;gBAC7BP,QAAQ,EAAE,CAAC7G,aAAc;gBACzB0H,QAAQ,EAAEpF,oBAAqB;gBAAAsD,QAAA,EAE9BlG,iBAAiB,CAACiD,GAAG,CAAEH,OAAO,iBAC7BrE,OAAA,CAACC,MAAM;kBAAeiH,KAAK,EAAE7C,OAAQ;kBAAAoD,QAAA,EAClCpD;gBAAO,GADGA,OAAO;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEZ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENhI,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAAAnB,QAAA,eACVzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cACR7D,IAAI,EAAC,eAAe;cACpB+B,KAAK,EAAC,gBAAgB;cACtBoC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAA2C,CAAC,CAAE;cAAA0I,QAAA,eAEjFzH,OAAA,CAAClB,QAAQ,CAAC6K,KAAK;gBAACC,OAAO,EAAE5C;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENhI,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAAAnB,QAAA,eACVzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cACR7D,IAAI,EAAC,kBAAkB;cACvB+B,KAAK,EAAC,mBAAmB;cACzBoC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAA8C,CAAC,CAAE;cAAA0I,QAAA,eAEpFzH,OAAA,CAAClB,QAAQ,CAAC6K,KAAK;gBAACC,OAAO,EAAEzC;cAAkB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENhI,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAArB,QAAA,eACjBzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cACR7D,IAAI,EAAC,gBAAgB;cACrB+B,KAAK,EAAC,iBAAiB;cACvBoC,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAA+B,CAAC,EAC3D;gBAAE4I,IAAI,EAAE,QAAQ;gBAAEkC,GAAG,EAAE,CAAC;gBAAEC,GAAG,EAAE,EAAE;gBAAE/K,OAAO,EAAE;cAA2B,CAAC,CACxE;cAAA0I,QAAA,eAEFzH,OAAA,CAACtB,WAAW;gBACVmL,GAAG,EAAE,CAAE;gBACPC,GAAG,EAAE,EAAG;gBACRb,WAAW,EAAC,uBAAuB;gBACnCZ,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhI,OAAA,CAAChB,OAAO;UAAAyI,QAAA,EAAC;QAAqB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,EAEvCnG,aAAa,IAAIE,aAAa,IAAIE,gBAAgB,CAACgC,MAAM,GAAG,CAAC,iBAC5DjE,OAAA,CAACf,KAAK;UACJF,OAAO,EAAC,+BAA+B;UACvCmJ,WAAW,eACTlI,OAAA;YAAAyH,QAAA,gBACEzH,OAAA;cAAAyH,QAAA,gBAAGzH,OAAA;gBAAAyH,QAAA,EAAQ;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACnG,aAAa,CAACkI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnI,aAAa,CAACoI,KAAK,CAAC,CAAC,CAAC;YAAA;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/FhI,OAAA;cAAAyH,QAAA,gBAAGzH,OAAA;gBAAAyH,QAAA,EAAQ;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjG,aAAa;YAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ChI,OAAA;cAAAyH,QAAA,gBAAGzH,OAAA;gBAAAyH,QAAA,EAAQ;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/F,gBAAgB,CAACiI,IAAI,CAAC,IAAI,CAAC;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DhI,OAAA;cAAAyH,QAAA,gBAAGzH,OAAA;gBAAAyH,QAAA,EAAQ;cAAiB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvG,eAAe,CAACwC,MAAM,EAAC,2CAAyC;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3GhI,OAAA;cAAAyH,QAAA,gBAAGzH,OAAA;gBAAAyH,QAAA,EAAQ;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,oEAAgE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnG,CACN;UACDL,IAAI,EAAC,MAAM;UACXQ,QAAQ;UACRE,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAG;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACF,eAEDhI,OAAA,CAACpB,GAAG;UAAC+J,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAlB,QAAA,gBACpBzH,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAArB,QAAA,eACjBzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cACR7D,IAAI,EAAE,CAAC,sBAAsB,EAAE,iBAAiB,CAAE;cAClD+B,KAAK,EAAC,iBAAiB;cAAAQ,QAAA,eAEvBzH,OAAA,CAACtB,WAAW;gBACVmL,GAAG,EAAE,CAAE;gBACPZ,WAAW,EAAC,GAAG;gBACfZ,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENhI,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAArB,QAAA,eACjBzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cACR7D,IAAI,EAAE,CAAC,sBAAsB,EAAE,YAAY,CAAE;cAC7C+B,KAAK,EAAC,mBAAmB;cAAAQ,QAAA,eAEzBzH,OAAA,CAACtB,WAAW;gBACVmL,GAAG,EAAE,CAAE;gBACPZ,WAAW,EAAC,GAAG;gBACfZ,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENhI,OAAA,CAACnB,GAAG;YAAC+J,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAArB,QAAA,eACjBzH,OAAA,CAACxB,IAAI,CAACuK,IAAI;cACR7D,IAAI,EAAE,CAAC,sBAAsB,EAAE,eAAe,CAAE;cAChD+B,KAAK,EAAC,eAAe;cAAAQ,QAAA,eAErBzH,OAAA,CAACtB,WAAW;gBACVmL,GAAG,EAAE,CAAE;gBACPZ,WAAW,EAAC,GAAG;gBACfZ,KAAK,EAAE;kBAAEe,KAAK,EAAE;gBAAO;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhI,OAAA,CAACxB,IAAI,CAACuK,IAAI;UACR7D,IAAI,EAAC,gBAAgB;UACrB+B,KAAK,EAAG,6BAA4BxF,eAAe,CAACwC,MAAO,aAAa;UACxE+E,KAAK,EAAEvH,eAAe,CAACwC,MAAM,KAAK,CAAC,GAAG,2DAA2D,GAAG,0DAA2D;UAAAwD,QAAA,eAE/JzH,OAAA,CAACvB,MAAM;YACLiL,IAAI,EAAC,UAAU;YACfT,WAAW,EAAExH,eAAe,CAACwC,MAAM,KAAK,CAAC,GAAG,+DAA+D,GAAG,+CAAgD;YAC9JoE,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAO,CAAE;YACzBV,QAAQ,EAAEjH,eAAe,CAACwC,MAAM,KAAK,CAAE;YACvCkG,gBAAgB,EAAC,UAAU;YAC3BC,UAAU;YACVC,YAAY,EAAEA,CAACC,KAAK,EAAEd,MAAM,KAC1BA,MAAM,CAAC/B,QAAQ,CAAC8C,WAAW,CAAC,CAAC,CAACC,OAAO,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IAAI,CAC/D;YAAA9C,QAAA,EAEAhG,eAAe,CAAC+C,GAAG,CAAC,CAACC,KAAK,EAAEgG,KAAK,kBAChCzK,OAAA,CAACC,MAAM;cAAsDiH,KAAK,EAAEzC,KAAK,CAACE,SAAU;cAAA8C,QAAA,eAClFzH,OAAA;gBAAAyH,QAAA,gBACEzH,OAAA;kBAAAyH,QAAA,EAAShD,KAAK,CAACE;gBAAS;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAClChI,OAAA;kBAAKqI,KAAK,EAAE;oBAAEqC,QAAQ,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAO,CAAE;kBAAAlD,QAAA,GAC7ChD,KAAK,CAACJ,OAAO,EAAC,sBAAe,EAACI,KAAK,CAACmG,UAAU;gBAAA;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,EACLvD,KAAK,CAACoG,SAAS,IAAIpG,KAAK,CAACoG,SAAS,CAAC5G,MAAM,GAAG,CAAC,iBAC5CjE,OAAA;kBAAKqI,KAAK,EAAE;oBAAEqC,QAAQ,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAO,CAAE;kBAAAlD,QAAA,GAAC,aACpC,EAAChD,KAAK,CAACoG,SAAS,CAACZ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EACjDzF,KAAK,CAACoG,SAAS,CAAC5G,MAAM,GAAG,CAAC,IAAK,KAAIQ,KAAK,CAACoG,SAAS,CAAC5G,MAAM,GAAG,CAAE,OAAM;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAZM,GAAEvD,KAAK,CAACJ,OAAQ,IAAGI,KAAK,CAACE,SAAU,IAAG8F,KAAM,EAAC;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAanD,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZhI,OAAA;UAAKgE,SAAS,EAAC,cAAc;UAAAyD,QAAA,gBAC3BzH,OAAA,CAACrB,MAAM;YAACsJ,OAAO,EAAE9H,MAAO;YAACuI,QAAQ,EAAErG,YAAa;YAAAoF,QAAA,EAAC;UAEjD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThI,OAAA,CAACrB,MAAM;YACLgJ,IAAI,EAAC,SAAS;YACdmD,QAAQ,EAAC,QAAQ;YACjBjK,OAAO,EAAEwB,YAAa;YACtBqG,QAAQ,EAAE,CAAC/H,WAAW,IAAIG,WAAY;YACtC8G,IAAI,eAAE5H,OAAA,CAACZ,OAAO;cAAAyI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAP,QAAA,EAEjBpF,YAAY,GAAG,eAAe,GAAG,CAAC1B,WAAW,GAAG,gBAAgB,GAAG;UAAoB;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPhI,OAAA,CAACJ,qBAAqB;MACpBmL,OAAO,EAAEtI,qBAAsB;MAC/BuI,QAAQ,EAAEA,CAAA,KAAMtI,wBAAwB,CAAC,KAAK,CAAE;MAChDtC,SAAS,EAAE0E,6BAA8B;MACzCmG,aAAa,EAAE;QACbxH,KAAK,EAAE5B,aAAa;QACpB8B,KAAK,EAAE5B,aAAa;QACpB8B,QAAQ,EAAE5B;MACZ;IAAE;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEFhI,OAAA,CAACH,YAAY;MACXkL,OAAO,EAAEpI,cAAe;MACxBqI,QAAQ,EAAEA,CAAA,KAAMpI,iBAAiB,CAAC,KAAK,CAAE;MACzCxC,SAAS,EAAG8K,QAAQ,IAAK;QACvBjK,kBAAkB,CAACiK,QAAQ,CAAC;QAC5BtI,iBAAiB,CAAC,KAAK,CAAC;MAC1B,CAAE;MACF8E,KAAK,EAAC,4BAA4B;MAClCQ,WAAW,EAAC;IAAyH;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAAC3H,EAAA,CAnsBQH,sBAAsB;EAAA,QACZ5B,WAAW,EACbE,IAAI,CAACiC,OAAO,EAcvBX,SAAS;AAAA;AAAAqL,EAAA,GAhBNjL,sBAAsB;AAqsB/B,eAAeA,sBAAsB;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}