import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Tb<PERSON>lock,
  TbChevronLeft,
  TbChevronRight,
  TbCheck,
  TbX,
  TbFlag,
  TbAlertCircle,
} from 'react-icons/tb';

const QuizInterface = ({
  quiz,
  questions = [],
  onSubmit,
  onExit,
  className = ''
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeRemaining, setTimeRemaining] = useState((quiz?.duration || 30) * 60); // Convert to seconds
  const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);

  const currentQuestion = questions[currentQuestionIndex];
  const totalQuestions = questions.length;
  const progress = ((currentQuestionIndex + 1) / totalQuestions) * 100;

  // Timer effect
  useEffect(() => {
    if (timeRemaining <= 0) {
      handleSubmit();
      return;
    }

    const timer = setInterval(() => {
      setTimeRemaining(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeRemaining]);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle answer selection
  const handleAnswerChange = (questionId, answer) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  // Navigation functions
  const goToNext = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const goToQuestion = (index) => {
    setCurrentQuestionIndex(index);
  };

  // Submit quiz
  const handleSubmit = () => {
    onSubmit && onSubmit(answers);
  };

  // Render question based on type
  const renderQuestion = () => {
    if (!currentQuestion) {
      return (
        <div className="text-center py-8 text-gray-500">
          <TbAlertCircle className="w-12 h-12 mx-auto mb-2" />
          <p>Question not available</p>
        </div>
      );
    }

    // Validate question structure
    if (typeof currentQuestion !== 'object' || !currentQuestion._id) {
      console.warn('Invalid question structure:', currentQuestion);
      return (
        <div className="text-center py-8 text-gray-500">
          <TbAlertCircle className="w-12 h-12 mx-auto mb-2" />
          <p>Invalid question data</p>
        </div>
      );
    }

    const questionType = currentQuestion.type || currentQuestion.answerType || currentQuestion.questionType;
    console.log('Question type detected:', questionType, 'Question:', currentQuestion);

    switch (questionType) {
      case 'multiple-choice':
      case 'mcq':
      case 'Options':
        // Handle both object and array formats for options
        let optionsToRender = [];

        console.log('Processing options for MCQ:', currentQuestion.options);

        if (Array.isArray(currentQuestion.options)) {
          // Array format: ['option1', 'option2', ...]
          optionsToRender = currentQuestion.options.map((option, index) => ({
            key: String.fromCharCode(65 + index), // A, B, C, D
            value: option
          }));
        } else if (typeof currentQuestion.options === 'object' && currentQuestion.options !== null) {
          // Object format: {A: 'option1', B: 'option2', ...}
          optionsToRender = Object.entries(currentQuestion.options).map(([key, value]) => ({
            key: key,
            value: value
          }));
        }

        console.log('Options to render:', optionsToRender);

        if (optionsToRender.length === 0) {
          return (
            <div className="text-center py-8 text-gray-500">
              <TbAlertCircle className="w-12 h-12 mx-auto mb-2" />
              <p>No options available for this question</p>
            </div>
          );
        }

        return (
          <div className="space-y-3">
            {optionsToRender.map((option, index) => {
              const isSelected = answers[currentQuestion._id] === option.key;

              return (
                <motion.button
                  key={option.key}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleAnswerChange(currentQuestion._id, option.key)}
                  className={`
                    w-full p-4 rounded-lg border-2 text-left transition-all duration-200 shadow-sm hover:shadow-md
                    ${isSelected
                      ? 'border-blue-500 bg-blue-50 text-blue-900'
                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
                    }
                  `}
                >
                  <div className="flex items-center gap-3">
                    <div className={`
                      w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm
                      ${isSelected
                        ? 'border-blue-500 bg-blue-500 text-white'
                        : 'border-gray-300 text-gray-500'
                      }
                    `}>
                      {option.key}
                    </div>
                    <span className={`flex-1 text-base font-medium leading-relaxed ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>
                      {String(option.value || 'No content')}
                    </span>
                  </div>
                </motion.button>
              );
            })}
          </div>
        );

      case 'fill-in-the-blank':
      case 'text':
      case 'Fill in the Blank':
        const isShortAnswer = currentQuestion.inputType === 'short';

        return (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800 text-sm font-medium mb-2">
                ✏️ Instructions: {isShortAnswer ? 'Type your short answer below' : 'Write your detailed answer below'}
              </p>
              <p className="text-blue-600 text-sm">
                This is a free-text question. {isShortAnswer ? 'A few words or short phrase is expected.' : 'Write your complete answer with explanations.'}
              </p>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Your Answer:
              </label>

              {isShortAnswer ? (
                <input
                  type="text"
                  value={answers[currentQuestion._id] || ''}
                  onChange={(e) => handleAnswerChange(currentQuestion._id, e.target.value)}
                  placeholder="Type your answer here... (e.g., 'Au' for Gold)"
                  className="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 text-base"
                />
              ) : (
                <textarea
                  value={answers[currentQuestion._id] || ''}
                  onChange={(e) => handleAnswerChange(currentQuestion._id, e.target.value)}
                  placeholder="Type your detailed answer here... Explain your reasoning and provide examples if needed."
                  className="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 resize-none transition-all duration-200 text-base"
                  rows={6}
                  style={{ minHeight: '150px' }}
                />
              )}

              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>
                  {answers[currentQuestion._id]?.length || 0} characters
                  {isShortAnswer && answers[currentQuestion._id]?.length > 50 && (
                    <span className="text-amber-600 ml-2">⚠ Consider a shorter answer</span>
                  )}
                </span>
                <span className={answers[currentQuestion._id] ? 'text-green-600' : 'text-gray-500'}>
                  {answers[currentQuestion._id] ? '✓ Answer provided' : '⚠ No answer yet'}
                </span>
              </div>
            </div>
          </div>
        );

      case 'image':
      case 'picture_based':
        // Handle both object and array formats for image questions too
        let imageOptionsToRender = [];

        if (Array.isArray(currentQuestion.options)) {
          imageOptionsToRender = currentQuestion.options.map((option, index) => ({
            key: String.fromCharCode(65 + index),
            value: option
          }));
        } else if (typeof currentQuestion.options === 'object' && currentQuestion.options !== null) {
          imageOptionsToRender = Object.entries(currentQuestion.options).map(([key, value]) => ({
            key: key,
            value: value
          }));
        }

        return (
          <div className="space-y-4">
            {/* Image Display Section */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <div className="text-center mb-4">
                <h4 className="text-lg font-semibold text-blue-900 mb-2">📸 Image Question</h4>
                <p className="text-blue-700 text-sm">Look at the image below and select the correct answer</p>
              </div>

              {(currentQuestion.imageUrl || currentQuestion.image) ? (
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <img
                    src={currentQuestion.imageUrl || currentQuestion.image}
                    alt="Question Image"
                    className="max-w-full h-auto rounded-lg mx-auto shadow-md max-h-64 object-contain"
                    onLoad={(e) => {
                      console.log('Image loaded successfully:', e.target.src);
                    }}
                    onError={(e) => {
                      console.error('Image failed to load:', e.target.src);
                      e.target.style.display = 'none';
                      const fallback = e.target.parentNode.querySelector('.image-fallback');
                      if (fallback) fallback.style.display = 'block';
                    }}
                  />
                  <div className="image-fallback text-center text-gray-500 py-8 hidden">
                    <TbAlertCircle className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                    <p className="text-gray-600">Image could not be loaded</p>
                    <p className="text-sm text-gray-500 mt-1">Please answer based on the question text</p>
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-lg p-8 text-center text-gray-500 border-2 border-dashed border-gray-300">
                  <TbAlertCircle className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                  <p className="text-gray-600">No image provided for this question</p>
                </div>
              )}
            </div>

            {imageOptionsToRender.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <TbAlertCircle className="w-12 h-12 mx-auto mb-2" />
                <p>No options available for this image question</p>
              </div>
            ) : (
              <div className="space-y-3">
                {imageOptionsToRender.map((option) => {
                  const isSelected = answers[currentQuestion._id] === option.key;

                  return (
                    <button
                      key={option.key}
                      onClick={() => handleAnswerChange(currentQuestion._id, option.key)}
                      className={`
                        w-full p-4 rounded-lg border-2 text-left transition-all duration-200
                        ${isSelected
                          ? 'border-blue-500 bg-blue-50 text-blue-900'
                          : 'border-gray-200 bg-white hover:border-gray-300'
                        }
                      `}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`
                          w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm
                          ${isSelected
                            ? 'border-blue-500 bg-blue-500 text-white'
                            : 'border-gray-300 text-gray-500'
                          }
                        `}>
                          {option.key}
                        </div>
                        <span className={`text-base font-medium ${isSelected ? 'text-blue-900' : 'text-gray-800'}`}>
                          {option.value || 'No content'}
                        </span>
                      </div>
                    </button>
                  );
                })}
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            <TbAlertCircle className="w-12 h-12 mx-auto mb-2" />
            <p>Unsupported question type: {currentQuestion.type || currentQuestion.answerType}</p>
          </div>
        );
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 flex flex-col ${className}`}>
      {/* Top Bar */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Quiz Title */}
            <div>
              <h1 className="text-xl font-bold text-gray-900">{quiz?.name || 'Quiz'}</h1>
              <p className="text-sm text-gray-600">{quiz?.subject || 'Subject'}</p>
            </div>

            {/* Timer */}
            <div className={`
              flex items-center gap-2 px-4 py-2 rounded-lg font-mono text-lg font-bold
              ${timeRemaining <= 300 ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'}
            `}>
              <TbClock className="w-5 h-5" />
              {formatTime(timeRemaining)}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>Question {currentQuestionIndex + 1} of {totalQuestions}</span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <motion.div
                className="bg-blue-500 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Question Content */}
      <div className="flex-1 max-w-4xl mx-auto w-full px-4 py-8">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuestionIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-xl shadow-sm border p-6 lg:p-8"
          >
            {/* Question */}
            <div className="mb-8">
              <h2 className="text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed">
                {currentQuestion?.name || currentQuestion?.question || 'Question not available'}
              </h2>
              
              {currentQuestion?.description && (
                <p className="text-gray-600 mb-4">{currentQuestion.description}</p>
              )}
            </div>

            {/* Answer Options */}
            {renderQuestion()}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Bottom Navigation */}
      <div className="bg-white border-t sticky bottom-0">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Previous Button */}
            <button
              onClick={goToPrevious}
              disabled={currentQuestionIndex === 0}
              className={`
                flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors
                ${currentQuestionIndex === 0
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 hover:bg-gray-100'
                }
              `}
            >
              <TbChevronLeft className="w-5 h-5" />
              Previous
            </button>

            {/* Question Numbers */}
            <div className="flex items-center gap-2 overflow-x-auto max-w-md">
              {questions && Array.isArray(questions) ? questions.map((question, index) => {
                // Safety check for question object
                if (!question || typeof question !== 'object') {
                  console.warn('Invalid question at index:', index, question);
                  return null;
                }

                return (
                  <button
                    key={question._id || index}
                    onClick={() => goToQuestion(index)}
                    className={`
                      w-8 h-8 rounded-full text-sm font-medium transition-colors flex-shrink-0
                      ${index === currentQuestionIndex
                        ? 'bg-blue-500 text-white'
                        : answers[question._id]
                        ? 'bg-green-100 text-green-600'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }
                    `}
                  >
                    {index + 1}
                  </button>
                );
              }) : null}
            </div>

            {/* Next/Submit Button */}
            {currentQuestionIndex === totalQuestions - 1 ? (
              <button
                onClick={() => setShowSubmitConfirm(true)}
                className="flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
              >
                <TbCheck className="w-5 h-5" />
                Submit Quiz
              </button>
            ) : (
              <button
                onClick={goToNext}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Next
                <TbChevronRight className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Submit Confirmation Modal */}
      <AnimatePresence>
        {showSubmitConfirm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-xl p-6 max-w-md w-full"
            >
              <h3 className="text-lg font-bold text-gray-900 mb-4">Submit Quiz?</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to submit your quiz? You won't be able to change your answers after submission.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowSubmitConfirm(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmit}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  Submit
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default QuizInterface;
