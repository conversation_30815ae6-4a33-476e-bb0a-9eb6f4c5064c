import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { getExamById } from '../../../apicalls/exams';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { Loading } from '../../../components/modern';
import {
  TbClock,
  TbQuestionMark,
  TbTrophy,
  TbPlayerPlay,
  TbBrain
} from 'react-icons/tb';
import './responsive.css';

const QuizStart = () => {
  const [examData, setExamData] = useState(null);
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  useEffect(() => {
    const fetchExamData = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getExamById({ examId: id });
        dispatch(HideLoading());

        if (response.success) {
          setExamData(response.data);
        } else {
          message.error(response.message);
          navigate('/user/quiz');
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
        navigate('/user/quiz');
      }
    };

    if (id) {
      fetchExamData();
    }
  }, [id, dispatch, navigate]);

  useEffect(() => {
    document.body.classList.add('quiz-fullscreen');
    return () => {
      document.body.classList.remove('quiz-fullscreen');
    };
  }, []);

  const handleStartQuiz = () => {
    navigate(`/quiz/${id}/play`);
  };

  if (!examData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center">
        <Loading fullScreen text="Loading quiz details..." />
      </div>
    );
  }

  return (
    <div className="h-screen bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 flex items-center justify-center p-3 sm:p-4 lg:p-6 relative overflow-y-auto">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 -right-20 sm:-top-40 sm:-right-40 w-40 h-40 sm:w-80 sm:h-80 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-20 -left-20 sm:-bottom-40 sm:-left-40 w-40 h-40 sm:w-80 sm:h-80 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 sm:w-96 sm:h-96 bg-white/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Main Content Card */}
      <motion.div
        initial={{ opacity: 0, y: 30, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10 max-w-2xl w-full mx-auto"
      >
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-2xl border border-white/20 overflow-hidden quiz-start-card">
          {/* Header Section */}
          <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-4 sm:p-6 lg:p-8 text-center relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative z-10"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-white/20 rounded-full mb-4 sm:mb-6 backdrop-blur-sm shadow-xl">
                <TbBrain className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
              </div>
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-black text-white mb-3 sm:mb-4 leading-tight px-2" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.3)' }}>
                {examData.name}
              </h1>
              <p className="text-base sm:text-lg text-white/90 font-medium leading-relaxed px-2" style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.3)' }}>
                Challenge your brain, Beat the rest! 🧠✨
              </p>
            </motion.div>
          </div>

          {/* Quiz Stats */}
          <div className="p-4 sm:p-6 lg:p-8">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-center p-3 sm:p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl sm:rounded-2xl border border-blue-100"
              >
                <TbQuestionMark className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600 mx-auto mb-2 sm:mb-3" />
                <div className="text-xl sm:text-2xl font-black text-gray-800 mb-1">{examData.questions?.length || 0}</div>
                <div className="text-xs sm:text-sm font-semibold text-gray-600">Questions</div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-center p-3 sm:p-4 bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl sm:rounded-2xl border border-emerald-100"
              >
                <TbClock className="w-6 h-6 sm:w-8 sm:h-8 text-emerald-600 mx-auto mb-2 sm:mb-3" />
                <div className="text-xl sm:text-2xl font-black text-gray-800 mb-1">{Math.round((examData.duration || 1800) / 60)}</div>
                <div className="text-xs sm:text-sm font-semibold text-gray-600">Minutes</div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="text-center p-3 sm:p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl sm:rounded-2xl border border-purple-100"
              >
                <TbTrophy className="w-6 h-6 sm:w-8 sm:h-8 text-purple-600 mx-auto mb-2 sm:mb-3" />
                <div className="text-xl sm:text-2xl font-black text-gray-800 mb-1">{examData.passingPercentage || 70}%</div>
                <div className="text-xs sm:text-sm font-semibold text-gray-600">Pass Mark</div>
              </motion.div>
            </div>

            {/* User Info Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-200"
            >
              <div className="flex items-center space-x-3 sm:space-x-4">
                <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg flex-shrink-0">
                  <span className="text-white font-bold text-lg sm:text-xl">
                    {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                  </span>
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="font-bold text-gray-900 text-base sm:text-lg truncate">Welcome, {user?.name || 'Student'}!</h3>
                  <div className="flex flex-col sm:flex-row gap-2 mt-1">
                    <span className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm bg-blue-100 text-blue-800">
                      {user?.level || 'Primary'}
                    </span>
                    <span className="inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm bg-emerald-100 text-emerald-800">
                      Class {user?.class || 'N/A'}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Action Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="text-center"
            >
              <button
                onClick={handleStartQuiz}
                className="group relative inline-flex items-center justify-center w-full sm:w-auto px-8 sm:px-12 py-3 sm:py-4 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white font-bold text-base sm:text-lg rounded-xl sm:rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 overflow-hidden touch-manipulation"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <TbPlayerPlay className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 group-hover:scale-110 transition-transform duration-300" />
                <span className="relative z-10">Start Quiz</span>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
              </button>

              <p className="mt-3 sm:mt-4 text-gray-500 text-xs sm:text-sm font-medium px-4">
                Ready to test your knowledge? Let's begin! 🚀
              </p>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default QuizStart;
