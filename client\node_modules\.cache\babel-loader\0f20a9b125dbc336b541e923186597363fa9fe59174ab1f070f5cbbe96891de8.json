{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\common\\\\NotificationBell.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbBell, TbBellRinging, TbCheck, TbX, TbSettings, TbTrash } from 'react-icons/tb';\nimport { getUserNotifications, getUnreadNotificationCount, markNotificationAsRead, markAllNotificationsAsRead } from '../../apicalls/notifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotificationBell = ({\n  className = ''\n}) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const dropdownRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Fetch unread count periodically\n  useEffect(() => {\n    const fetchUnreadCount = async () => {\n      try {\n        const response = await getUnreadNotificationCount();\n        if (response.success) {\n          setUnreadCount(response.data.unreadCount);\n        }\n      } catch (error) {\n        console.error('Error fetching unread count:', error);\n      }\n    };\n    fetchUnreadCount();\n    const interval = setInterval(fetchUnreadCount, 30000); // Every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Fetch notifications when dropdown opens\n  useEffect(() => {\n    if (isOpen && notifications.length === 0) {\n      fetchNotifications();\n    }\n  }, [isOpen]);\n  const fetchNotifications = async (pageNum = 1, reset = false) => {\n    if (loading) return;\n    setLoading(true);\n    try {\n      const response = await getUserNotifications({\n        page: pageNum,\n        limit: 10\n      });\n      if (response.success) {\n        const newNotifications = response.data.notifications;\n        if (reset || pageNum === 1) {\n          setNotifications(newNotifications);\n        } else {\n          setNotifications(prev => [...prev, ...newNotifications]);\n        }\n        setHasMore(newNotifications.length === 10);\n        setPage(pageNum);\n        setUnreadCount(response.data.unreadCount);\n      }\n    } catch (error) {\n      console.error('Error fetching notifications:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleMarkAsRead = async notificationId => {\n    try {\n      const response = await markNotificationAsRead(notificationId);\n      if (response.success) {\n        setNotifications(prev => prev.map(notif => notif._id === notificationId ? {\n          ...notif,\n          isRead: true\n        } : notif));\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n    }\n  };\n  const handleMarkAllAsRead = async () => {\n    try {\n      const response = await markAllNotificationsAsRead();\n      if (response.success) {\n        setNotifications(prev => prev.map(notif => ({\n          ...notif,\n          isRead: true\n        })));\n        setUnreadCount(0);\n      }\n    } catch (error) {\n      console.error('Error marking all as read:', error);\n    }\n  };\n  const getNotificationIcon = type => {\n    switch (type) {\n      case 'new_exam':\n        return '🎯';\n      case 'new_study_material':\n        return '📚';\n      case 'forum_question_posted':\n        return '❓';\n      case 'forum_answer_received':\n        return '💡';\n      case 'level_up':\n        return '🎉';\n      case 'achievement_unlocked':\n        return '🏆';\n      default:\n        return '📢';\n    }\n  };\n  const formatTimeAgo = date => {\n    const now = new Date();\n    const notifDate = new Date(date);\n    const diffInMinutes = Math.floor((now - notifDate) / (1000 * 60));\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    return notifDate.toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative ${className}`,\n    ref: dropdownRef,\n    children: [/*#__PURE__*/_jsxDEV(motion.button, {\n      whileHover: {\n        scale: 1.05\n      },\n      whileTap: {\n        scale: 0.95\n      },\n      onClick: () => setIsOpen(!isOpen),\n      className: \"relative p-2 text-gray-600 hover:text-gray-800 transition-colors\",\n      children: [unreadCount > 0 ? /*#__PURE__*/_jsxDEV(TbBellRinging, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TbBell, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(motion.span, {\n        initial: {\n          scale: 0\n        },\n        animate: {\n          scale: 1\n        },\n        className: \"absolute -top-1 -right-1 bg-red-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold border-2 border-white shadow-lg\",\n        style: {\n          minWidth: '20px',\n          fontSize: '10px',\n          fontWeight: '700',\n          zIndex: 1000\n        },\n        children: unreadCount > 99 ? '99+' : unreadCount\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -10,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          y: 0,\n          scale: 1\n        },\n        exit: {\n          opacity: 0,\n          y: -10,\n          scale: 0.95\n        },\n        className: \"absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 max-h-96 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 border-b border-gray-100 flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-gray-800\",\n            children: \"Notifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleMarkAllAsRead,\n            className: \"text-sm text-blue-600 hover:text-blue-700 font-medium\",\n            children: \"Mark all read\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-h-80 overflow-y-auto\",\n          children: [loading && notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 text-center text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this), \"Loading notifications...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 17\n          }, this) : notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8 text-center text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(TbBell, {\n              className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No notifications yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 17\n          }, this) : notifications.map(notification => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            className: `p-4 border-b border-gray-50 hover:bg-gray-50 cursor-pointer transition-colors ${!notification.isRead ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''}`,\n            onClick: () => {\n              if (!notification.isRead) {\n                handleMarkAsRead(notification._id);\n              }\n              if (notification.actionUrl) {\n                window.location.href = notification.actionUrl;\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg flex-shrink-0\",\n                children: getNotificationIcon(notification.type)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-800 text-sm line-clamp-1\",\n                  children: notification.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-xs mt-1 line-clamp-2\",\n                  children: notification.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-400 text-xs mt-2\",\n                  children: formatTimeAgo(notification.createdAt)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 23\n              }, this), !notification.isRead && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 21\n            }, this)\n          }, notification._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 19\n          }, this)), hasMore && notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => fetchNotifications(page + 1),\n            disabled: loading,\n            className: \"w-full p-3 text-center text-blue-600 hover:bg-gray-50 text-sm font-medium disabled:opacity-50\",\n            children: loading ? 'Loading...' : 'Load more'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n_s(NotificationBell, \"0TFy8flr5rtOvrDf5KteY3XzPVw=\");\n_c = NotificationBell;\nexport default NotificationBell;\nvar _c;\n$RefreshReg$(_c, \"NotificationBell\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "AnimatePresence", "TbBell", "TbBellRinging", "TbCheck", "TbX", "TbSettings", "TbTrash", "getUserNotifications", "getUnreadNotificationCount", "markNotificationAsRead", "markAllNotificationsAsRead", "jsxDEV", "_jsxDEV", "NotificationBell", "className", "_s", "isOpen", "setIsOpen", "notifications", "setNotifications", "unreadCount", "setUnreadCount", "loading", "setLoading", "page", "setPage", "hasMore", "setHasMore", "dropdownRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "fetchUnreadCount", "response", "success", "data", "error", "console", "interval", "setInterval", "clearInterval", "length", "fetchNotifications", "pageNum", "reset", "limit", "newNotifications", "prev", "handleMarkAsRead", "notificationId", "map", "notif", "_id", "isRead", "Math", "max", "handleMarkAllAsRead", "getNotificationIcon", "type", "formatTimeAgo", "date", "now", "Date", "notifDate", "diffInMinutes", "floor", "diffInHours", "diffInDays", "toLocaleDateString", "ref", "children", "button", "whileHover", "scale", "whileTap", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "span", "initial", "animate", "style", "min<PERSON><PERSON><PERSON>", "fontSize", "fontWeight", "zIndex", "div", "opacity", "y", "exit", "notification", "x", "actionUrl", "window", "location", "href", "title", "message", "createdAt", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/common/NotificationBell.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  TbBell, \n  TbBellRinging, \n  TbCheck, \n  TbX,\n  TbSettings,\n  TbTrash\n} from 'react-icons/tb';\nimport { \n  getUserNotifications, \n  getUnreadNotificationCount,\n  markNotificationAsRead,\n  markAllNotificationsAsRead \n} from '../../apicalls/notifications';\n\nconst NotificationBell = ({ className = '' }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const dropdownRef = useRef(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Fetch unread count periodically\n  useEffect(() => {\n    const fetchUnreadCount = async () => {\n      try {\n        const response = await getUnreadNotificationCount();\n        if (response.success) {\n          setUnreadCount(response.data.unreadCount);\n        }\n      } catch (error) {\n        console.error('Error fetching unread count:', error);\n      }\n    };\n\n    fetchUnreadCount();\n    const interval = setInterval(fetchUnreadCount, 30000); // Every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Fetch notifications when dropdown opens\n  useEffect(() => {\n    if (isOpen && notifications.length === 0) {\n      fetchNotifications();\n    }\n  }, [isOpen]);\n\n  const fetchNotifications = async (pageNum = 1, reset = false) => {\n    if (loading) return;\n    \n    setLoading(true);\n    try {\n      const response = await getUserNotifications({\n        page: pageNum,\n        limit: 10\n      });\n      \n      if (response.success) {\n        const newNotifications = response.data.notifications;\n        \n        if (reset || pageNum === 1) {\n          setNotifications(newNotifications);\n        } else {\n          setNotifications(prev => [...prev, ...newNotifications]);\n        }\n        \n        setHasMore(newNotifications.length === 10);\n        setPage(pageNum);\n        setUnreadCount(response.data.unreadCount);\n      }\n    } catch (error) {\n      console.error('Error fetching notifications:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleMarkAsRead = async (notificationId) => {\n    try {\n      const response = await markNotificationAsRead(notificationId);\n      if (response.success) {\n        setNotifications(prev => \n          prev.map(notif => \n            notif._id === notificationId \n              ? { ...notif, isRead: true }\n              : notif\n          )\n        );\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n    }\n  };\n\n  const handleMarkAllAsRead = async () => {\n    try {\n      const response = await markAllNotificationsAsRead();\n      if (response.success) {\n        setNotifications(prev => \n          prev.map(notif => ({ ...notif, isRead: true }))\n        );\n        setUnreadCount(0);\n      }\n    } catch (error) {\n      console.error('Error marking all as read:', error);\n    }\n  };\n\n  const getNotificationIcon = (type) => {\n    switch (type) {\n      case 'new_exam':\n        return '🎯';\n      case 'new_study_material':\n        return '📚';\n      case 'forum_question_posted':\n        return '❓';\n      case 'forum_answer_received':\n        return '💡';\n      case 'level_up':\n        return '🎉';\n      case 'achievement_unlocked':\n        return '🏆';\n      default:\n        return '📢';\n    }\n  };\n\n  const formatTimeAgo = (date) => {\n    const now = new Date();\n    const notifDate = new Date(date);\n    const diffInMinutes = Math.floor((now - notifDate) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    \n    return notifDate.toLocaleDateString();\n  };\n\n  return (\n    <div className={`relative ${className}`} ref={dropdownRef}>\n      {/* Bell Icon */}\n      <motion.button\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-600 hover:text-gray-800 transition-colors\"\n      >\n        {unreadCount > 0 ? (\n          <TbBellRinging className=\"w-6 h-6\" />\n        ) : (\n          <TbBell className=\"w-6 h-6\" />\n        )}\n        \n        {/* Unread count badge */}\n        {unreadCount > 0 && (\n          <motion.span\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            className=\"absolute -top-1 -right-1 bg-red-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold border-2 border-white shadow-lg\"\n            style={{\n              minWidth: '20px',\n              fontSize: '10px',\n              fontWeight: '700',\n              zIndex: 1000\n            }}\n          >\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </motion.span>\n        )}\n      </motion.button>\n\n      {/* Dropdown */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -10, scale: 0.95 }}\n            animate={{ opacity: 1, y: 0, scale: 1 }}\n            exit={{ opacity: 0, y: -10, scale: 0.95 }}\n            className=\"absolute right-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 max-h-96 overflow-hidden\"\n          >\n            {/* Header */}\n            <div className=\"p-4 border-b border-gray-100 flex items-center justify-between\">\n              <h3 className=\"font-semibold text-gray-800\">Notifications</h3>\n              {unreadCount > 0 && (\n                <button\n                  onClick={handleMarkAllAsRead}\n                  className=\"text-sm text-blue-600 hover:text-blue-700 font-medium\"\n                >\n                  Mark all read\n                </button>\n              )}\n            </div>\n\n            {/* Notifications List */}\n            <div className=\"max-h-80 overflow-y-auto\">\n              {loading && notifications.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  <div className=\"animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2\"></div>\n                  Loading notifications...\n                </div>\n              ) : notifications.length === 0 ? (\n                <div className=\"p-8 text-center text-gray-500\">\n                  <TbBell className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\n                  <p>No notifications yet</p>\n                </div>\n              ) : (\n                notifications.map((notification) => (\n                  <motion.div\n                    key={notification._id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    className={`p-4 border-b border-gray-50 hover:bg-gray-50 cursor-pointer transition-colors ${\n                      !notification.isRead ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''\n                    }`}\n                    onClick={() => {\n                      if (!notification.isRead) {\n                        handleMarkAsRead(notification._id);\n                      }\n                      if (notification.actionUrl) {\n                        window.location.href = notification.actionUrl;\n                      }\n                    }}\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      <span className=\"text-lg flex-shrink-0\">\n                        {getNotificationIcon(notification.type)}\n                      </span>\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"font-medium text-gray-800 text-sm line-clamp-1\">\n                          {notification.title}\n                        </p>\n                        <p className=\"text-gray-600 text-xs mt-1 line-clamp-2\">\n                          {notification.message}\n                        </p>\n                        <p className=\"text-gray-400 text-xs mt-2\">\n                          {formatTimeAgo(notification.createdAt)}\n                        </p>\n                      </div>\n                      {!notification.isRead && (\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2\"></div>\n                      )}\n                    </div>\n                  </motion.div>\n                ))\n              )}\n              \n              {/* Load More */}\n              {hasMore && notifications.length > 0 && (\n                <button\n                  onClick={() => fetchNotifications(page + 1)}\n                  disabled={loading}\n                  className=\"w-full p-3 text-center text-blue-600 hover:bg-gray-50 text-sm font-medium disabled:opacity-50\"\n                >\n                  {loading ? 'Loading...' : 'Load more'}\n                </button>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default NotificationBell;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EACNC,aAAa,EACbC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,OAAO,QACF,gBAAgB;AACvB,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,sBAAsB,EACtBC,0BAA0B,QACrB,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMgC,WAAW,GAAG9B,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMgC,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIF,WAAW,CAACG,OAAO,IAAI,CAACH,WAAW,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtEhB,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAEDiB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhC,SAAS,CAAC,MAAM;IACd,MAAMwC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM9B,0BAA0B,CAAC,CAAC;QACnD,IAAI8B,QAAQ,CAACC,OAAO,EAAE;UACpBlB,cAAc,CAACiB,QAAQ,CAACE,IAAI,CAACpB,WAAW,CAAC;QAC3C;MACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDJ,gBAAgB,CAAC,CAAC;IAClB,MAAMM,QAAQ,GAAGC,WAAW,CAACP,gBAAgB,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEvD,OAAO,MAAMQ,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9C,SAAS,CAAC,MAAM;IACd,IAAImB,MAAM,IAAIE,aAAa,CAAC4B,MAAM,KAAK,CAAC,EAAE;MACxCC,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAAC/B,MAAM,CAAC,CAAC;EAEZ,MAAM+B,kBAAkB,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,EAAEC,KAAK,GAAG,KAAK,KAAK;IAC/D,IAAI3B,OAAO,EAAE;IAEbC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAM/B,oBAAoB,CAAC;QAC1CiB,IAAI,EAAEwB,OAAO;QACbE,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,IAAIZ,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAMY,gBAAgB,GAAGb,QAAQ,CAACE,IAAI,CAACtB,aAAa;QAEpD,IAAI+B,KAAK,IAAID,OAAO,KAAK,CAAC,EAAE;UAC1B7B,gBAAgB,CAACgC,gBAAgB,CAAC;QACpC,CAAC,MAAM;UACLhC,gBAAgB,CAACiC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGD,gBAAgB,CAAC,CAAC;QAC1D;QAEAxB,UAAU,CAACwB,gBAAgB,CAACL,MAAM,KAAK,EAAE,CAAC;QAC1CrB,OAAO,CAACuB,OAAO,CAAC;QAChB3B,cAAc,CAACiB,QAAQ,CAACE,IAAI,CAACpB,WAAW,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,gBAAgB,GAAG,MAAOC,cAAc,IAAK;IACjD,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAM7B,sBAAsB,CAAC6C,cAAc,CAAC;MAC7D,IAAIhB,QAAQ,CAACC,OAAO,EAAE;QACpBpB,gBAAgB,CAACiC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACC,KAAK,IACZA,KAAK,CAACC,GAAG,KAAKH,cAAc,GACxB;UAAE,GAAGE,KAAK;UAAEE,MAAM,EAAE;QAAK,CAAC,GAC1BF,KACN,CACF,CAAC;QACDnC,cAAc,CAAC+B,IAAI,IAAIO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,IAAI,GAAG,CAAC,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D;EACF,CAAC;EAED,MAAMoB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAM5B,0BAA0B,CAAC,CAAC;MACnD,IAAI4B,QAAQ,CAACC,OAAO,EAAE;QACpBpB,gBAAgB,CAACiC,IAAI,IACnBA,IAAI,CAACG,GAAG,CAACC,KAAK,KAAK;UAAE,GAAGA,KAAK;UAAEE,MAAM,EAAE;QAAK,CAAC,CAAC,CAChD,CAAC;QACDrC,cAAc,CAAC,CAAC,CAAC;MACnB;IACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAMqB,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,UAAU;QACb,OAAO,IAAI;MACb,KAAK,oBAAoB;QACvB,OAAO,IAAI;MACb,KAAK,uBAAuB;QAC1B,OAAO,GAAG;MACZ,KAAK,uBAAuB;QAC1B,OAAO,IAAI;MACb,KAAK,UAAU;QACb,OAAO,IAAI;MACb,KAAK,sBAAsB;QACzB,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMC,aAAa,GAAIC,IAAI,IAAK;IAC9B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IAChC,MAAMI,aAAa,GAAGV,IAAI,CAACW,KAAK,CAAC,CAACJ,GAAG,GAAGE,SAAS,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEjE,IAAIC,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;IACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAQ,GAAEA,aAAc,OAAM;IAEtD,MAAME,WAAW,GAAGZ,IAAI,CAACW,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIE,WAAW,GAAG,EAAE,EAAE,OAAQ,GAAEA,WAAY,OAAM;IAElD,MAAMC,UAAU,GAAGb,IAAI,CAACW,KAAK,CAACC,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAQ,GAAEA,UAAW,OAAM;IAE/C,OAAOJ,SAAS,CAACK,kBAAkB,CAAC,CAAC;EACvC,CAAC;EAED,oBACE7D,OAAA;IAAKE,SAAS,EAAG,YAAWA,SAAU,EAAE;IAAC4D,GAAG,EAAE9C,WAAY;IAAA+C,QAAA,gBAExD/D,OAAA,CAACb,MAAM,CAAC6E,MAAM;MACZC,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAK,CAAE;MAC5BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAK,CAAE;MAC1BE,OAAO,EAAEA,CAAA,KAAM/D,SAAS,CAAC,CAACD,MAAM,CAAE;MAClCF,SAAS,EAAC,kEAAkE;MAAA6D,QAAA,GAE3EvD,WAAW,GAAG,CAAC,gBACdR,OAAA,CAACV,aAAa;QAACY,SAAS,EAAC;MAAS;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAErCxE,OAAA,CAACX,MAAM;QAACa,SAAS,EAAC;MAAS;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAC9B,EAGAhE,WAAW,GAAG,CAAC,iBACdR,OAAA,CAACb,MAAM,CAACsF,IAAI;QACVC,OAAO,EAAE;UAAER,KAAK,EAAE;QAAE,CAAE;QACtBS,OAAO,EAAE;UAAET,KAAK,EAAE;QAAE,CAAE;QACtBhE,SAAS,EAAC,wJAAwJ;QAClK0E,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE;QACV,CAAE;QAAAjB,QAAA,EAEDvD,WAAW,GAAG,EAAE,GAAG,KAAK,GAAGA;MAAW;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CACd;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAGhBxE,OAAA,CAACZ,eAAe;MAAA2E,QAAA,EACb3D,MAAM,iBACLJ,OAAA,CAACb,MAAM,CAAC8F,GAAG;QACTP,OAAO,EAAE;UAAEQ,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEjB,KAAK,EAAE;QAAK,CAAE;QAC7CS,OAAO,EAAE;UAAEO,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEjB,KAAK,EAAE;QAAE,CAAE;QACxCkB,IAAI,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEjB,KAAK,EAAE;QAAK,CAAE;QAC1ChE,SAAS,EAAC,gHAAgH;QAAA6D,QAAA,gBAG1H/D,OAAA;UAAKE,SAAS,EAAC,gEAAgE;UAAA6D,QAAA,gBAC7E/D,OAAA;YAAIE,SAAS,EAAC,6BAA6B;YAAA6D,QAAA,EAAC;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC7DhE,WAAW,GAAG,CAAC,iBACdR,OAAA;YACEoE,OAAO,EAAEnB,mBAAoB;YAC7B/C,SAAS,EAAC,uDAAuD;YAAA6D,QAAA,EAClE;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNxE,OAAA;UAAKE,SAAS,EAAC,0BAA0B;UAAA6D,QAAA,GACtCrD,OAAO,IAAIJ,aAAa,CAAC4B,MAAM,KAAK,CAAC,gBACpClC,OAAA;YAAKE,SAAS,EAAC,+BAA+B;YAAA6D,QAAA,gBAC5C/D,OAAA;cAAKE,SAAS,EAAC;YAA8F;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,4BAEtH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GACJlE,aAAa,CAAC4B,MAAM,KAAK,CAAC,gBAC5BlC,OAAA;YAAKE,SAAS,EAAC,+BAA+B;YAAA6D,QAAA,gBAC5C/D,OAAA,CAACX,MAAM;cAACa,SAAS,EAAC;YAAmC;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDxE,OAAA;cAAA+D,QAAA,EAAG;YAAoB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,GAENlE,aAAa,CAACqC,GAAG,CAAE0C,YAAY,iBAC7BrF,OAAA,CAACb,MAAM,CAAC8F,GAAG;YAETP,OAAO,EAAE;cAAEQ,OAAO,EAAE,CAAC;cAAEI,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCX,OAAO,EAAE;cAAEO,OAAO,EAAE,CAAC;cAAEI,CAAC,EAAE;YAAE,CAAE;YAC9BpF,SAAS,EAAG,iFACV,CAACmF,YAAY,CAACvC,MAAM,GAAG,yCAAyC,GAAG,EACpE,EAAE;YACHsB,OAAO,EAAEA,CAAA,KAAM;cACb,IAAI,CAACiB,YAAY,CAACvC,MAAM,EAAE;gBACxBL,gBAAgB,CAAC4C,YAAY,CAACxC,GAAG,CAAC;cACpC;cACA,IAAIwC,YAAY,CAACE,SAAS,EAAE;gBAC1BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGL,YAAY,CAACE,SAAS;cAC/C;YACF,CAAE;YAAAxB,QAAA,eAEF/D,OAAA;cAAKE,SAAS,EAAC,wBAAwB;cAAA6D,QAAA,gBACrC/D,OAAA;gBAAME,SAAS,EAAC,uBAAuB;gBAAA6D,QAAA,EACpCb,mBAAmB,CAACmC,YAAY,CAAClC,IAAI;cAAC;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACPxE,OAAA;gBAAKE,SAAS,EAAC,gBAAgB;gBAAA6D,QAAA,gBAC7B/D,OAAA;kBAAGE,SAAS,EAAC,gDAAgD;kBAAA6D,QAAA,EAC1DsB,YAAY,CAACM;gBAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACJxE,OAAA;kBAAGE,SAAS,EAAC,yCAAyC;kBAAA6D,QAAA,EACnDsB,YAAY,CAACO;gBAAO;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACJxE,OAAA;kBAAGE,SAAS,EAAC,4BAA4B;kBAAA6D,QAAA,EACtCX,aAAa,CAACiC,YAAY,CAACQ,SAAS;gBAAC;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACL,CAACa,YAAY,CAACvC,MAAM,iBACnB9C,OAAA;gBAAKE,SAAS,EAAC;cAAqD;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAC3E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GAjCDa,YAAY,CAACxC,GAAG;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkCX,CACb,CACF,EAGA1D,OAAO,IAAIR,aAAa,CAAC4B,MAAM,GAAG,CAAC,iBAClClC,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAMjC,kBAAkB,CAACvB,IAAI,GAAG,CAAC,CAAE;YAC5CkF,QAAQ,EAAEpF,OAAQ;YAClBR,SAAS,EAAC,+FAA+F;YAAA6D,QAAA,EAExGrD,OAAO,GAAG,YAAY,GAAG;UAAW;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;AAACrE,EAAA,CA7QIF,gBAAgB;AAAA8F,EAAA,GAAhB9F,gBAAgB;AA+QtB,eAAeA,gBAAgB;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}