const axios = require("axios");
const { AIQuestionGeneration, QuestionTemplate } = require("../models/aiQuestionGenerationModel");
const Question = require("../models/questionModel");
const WebImageService = require("./webImageService");
const QuestionValidationService = require("./questionValidationService");
const SyllabusService = require("./syllabusService");
const {
  primarySyllabus,
  secondarySyllabus,
  advanceSyllabus,
  questionGenerationGuidelines,
  subjectQuestionTypes
} = require("../data/tanzaniaSyllabus");

class AIQuestionGenerationService {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.model = process.env.OPENAI_MODEL || "gpt-3.5-turbo";
    this.webImageService = new WebImageService();
    this.validationService = new QuestionValidationService();

    // Debug logging for API key and model
    if (!this.openaiApiKey) {
      console.error("❌ OpenAI API key not found in environment variables");
    } else {
      console.log("✅ OpenAI API key loaded successfully");
      console.log(`✅ Using OpenAI model: ${this.model}`);
    }
  }

  // Main method to generate questions
  async generateQuestions(generationParams, requestedBy, examId) {
    const startTime = Date.now();
    console.log(`🚀 Starting AI question generation for ${generationParams.totalQuestions} questions...`);
    
    try {
      // Create generation record
      const generationRecord = new AIQuestionGeneration({
        requestedBy,
        examId: examId || null, // Allow null for standalone question generation
        generationParams,
        generationStatus: "in_progress",
        aiModel: this.model,
      });
      await generationRecord.save();

      const generatedQuestions = [];
      const { questionDistribution } = generationParams;

      // Generate multiple choice questions
      if (questionDistribution.multiple_choice > 0) {
        try {
          console.log(`📝 Generating ${questionDistribution.multiple_choice} multiple choice questions...`);
          const mcQuestions = await this.generateMultipleChoiceQuestions(
            generationParams,
            questionDistribution.multiple_choice
          );
          generatedQuestions.push(...mcQuestions);
          console.log(`✅ Generated ${mcQuestions.length}/${questionDistribution.multiple_choice} multiple choice questions`);
        } catch (error) {
          console.error("❌ Failed to generate multiple choice questions:", error.message);
        }
      }

      // Generate fill in the blank questions
      if (questionDistribution.fill_blank > 0) {
        try {
          console.log(`📝 Generating ${questionDistribution.fill_blank} fill-in-the-blank questions...`);
          const fillBlankQuestions = await this.generateFillBlankQuestions(
            generationParams,
            questionDistribution.fill_blank
          );
          generatedQuestions.push(...fillBlankQuestions);
          console.log(`✅ Generated ${fillBlankQuestions.length}/${questionDistribution.fill_blank} fill in the blank questions`);
        } catch (error) {
          console.error("❌ Failed to generate fill in the blank questions:", error.message);
        }
      }

      // Generate picture-based questions
      if (questionDistribution.picture_based > 0) {
        try {
          console.log(`📝 Generating ${questionDistribution.picture_based} picture-based questions...`);
          const pictureQuestions = await this.generatePictureBasedQuestions(
            generationParams,
            questionDistribution.picture_based
          );
          generatedQuestions.push(...pictureQuestions);
          console.log(`✅ Generated ${pictureQuestions.length}/${questionDistribution.picture_based} picture-based questions`);
        } catch (error) {
          console.error("❌ Failed to generate picture-based questions:", error.message);
          console.log("ℹ️ Picture-based questions require image services. Continuing with other question types...");
        }
      }

      // Check if any questions were generated
      if (generatedQuestions.length === 0) {
        throw new Error("No questions could be generated. Please check your internet connection and try again.");
      }

      console.log(`📊 Total questions generated: ${generatedQuestions.length}`);

      // Validate generated questions
      const validatedQuestions = generatedQuestions.map(question => {
        const validation = this.validationService.validateQuestion(
          question,
          generationParams.level,
          generationParams.class,
          generationParams.subjects[0] // Use first subject for validation
        );

        return {
          ...question,
          validation,
        };
      });

      // Update generation record
      const endTime = Date.now();
      generationRecord.generatedQuestions = validatedQuestions.map(q => ({
        generatedContent: q,
        approved: false,
        qualityScore: q.validation?.score || 0,
      }));
      generationRecord.generationStatus = "completed";
      generationRecord.generationTime = endTime - startTime;

      // Calculate overall quality score
      const avgQualityScore = validatedQuestions.reduce((sum, q) => sum + (q.validation?.score || 0), 0) / validatedQuestions.length;
      generationRecord.qualityScore = Math.round(avgQualityScore);

      await generationRecord.save();

      return {
        success: true,
        generationId: generationRecord._id,
        questions: generatedQuestions,
        generationTime: endTime - startTime,
      };

    } catch (error) {
      console.error("Question generation error:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Generate multiple choice questions
  async generateMultipleChoiceQuestions(params, count) {
    const { level, class: className, subjects, difficultyLevels, syllabusTopics, selectedSyllabusId } = params;
    const questions = [];

    for (let i = 0; i < count; i++) {
      const subject = subjects[Math.floor(Math.random() * subjects.length)];
      const difficulty = difficultyLevels[Math.floor(Math.random() * difficultyLevels.length)];

      const prompt = await this.buildMultipleChoicePrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId);
      
      try {
        const response = await this.callOpenAI(prompt);
        const questionData = this.parseJSONResponse(response);

        questions.push({
          name: questionData.question,
          type: "mcq", // New unified type field
          correctAnswer: questionData.correctOption, // Unified correct answer field
          options: questionData.options,
          topic: questionData.topic || subject,
          classLevel: `${level} ${className}`,
          // Legacy fields for backward compatibility
          answerType: "Options",
          correctOption: questionData.correctOption,
          isAIGenerated: true,
          generationSource: "ai_bulk",
          difficultyLevel: difficulty,
          syllabusTopics: questionData.syllabusTopics || [],
          questionType: "multiple_choice",
          createdBy: "ai",
        });
      } catch (error) {
        console.error(`❌ Error generating MC question ${i + 1}:`, error.message);
        // Continue with next question
      }
    }

    return questions;
  }

  // Generate fill in the blank questions
  async generateFillBlankQuestions(params, count) {
    const { level, class: className, subjects, difficultyLevels, syllabusTopics, selectedSyllabusId } = params;
    const questions = [];

    for (let i = 0; i < count; i++) {
      const subject = subjects[Math.floor(Math.random() * subjects.length)];
      const difficulty = difficultyLevels[Math.floor(Math.random() * difficultyLevels.length)];

      const prompt = await this.buildFillBlankPrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId);
      
      try {
        const response = await this.callOpenAI(prompt);
        const questionData = this.parseJSONResponse(response);

        questions.push({
          name: questionData.question,
          type: "fill", // New unified type field
          correctAnswer: questionData.correctAnswer,
          topic: questionData.topic || subject,
          classLevel: `${level} ${className}`,
          // Legacy fields for backward compatibility
          answerType: "Fill in the Blank",
          isAIGenerated: true,
          generationSource: "ai_bulk",
          difficultyLevel: difficulty,
          syllabusTopics: questionData.syllabusTopics || [],
          questionType: "fill_blank",
          createdBy: "ai",
        });
      } catch (error) {
        console.error(`❌ Error generating fill blank question ${i + 1}:`, error.message);
        // Continue with next question
      }
    }

    return questions;
  }

  // Generate picture-based questions
  async generatePictureBasedQuestions(params, count) {
    const { level, class: className, subjects, difficultyLevels, syllabusTopics, selectedSyllabusId } = params;
    const questions = [];

    for (let i = 0; i < count; i++) {
      const subject = subjects[Math.floor(Math.random() * subjects.length)];
      const difficulty = difficultyLevels[Math.floor(Math.random() * difficultyLevels.length)];

      const prompt = await this.buildPictureBasedPrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId);
      
      try {
        const response = await this.callOpenAI(prompt);
        const questionData = this.parseJSONResponse(response);

        // Search for appropriate images
        let imageUrl = null;
        try {
          const images = await this.webImageService.searchImagesForQuestion(
            subject,
            questionData.syllabusTopics?.[0] || "general",
            "picture_based",
            difficulty
          );

          if (images.length > 0) {
            // Use the most relevant image
            const selectedImage = images[0];

            // Download and upload to S3 for permanent storage
            const uploadResult = await this.webImageService.downloadAndUploadImage(
              selectedImage.url,
              `temp-${Date.now()}`
            );

            if (uploadResult.success) {
              imageUrl = uploadResult.imageUrl;
            }
          }
        } catch (imageError) {
          console.error("⚠️ Image service unavailable:", imageError.message);
          // Continue without image - the question can still be used as text-based
          console.log("ℹ️ Generating picture-based question without image (can be added manually later)");
        }

        questions.push({
          name: questionData.question,
          type: "image", // New unified type field
          correctAnswer: questionData.correctOption, // Unified correct answer field
          options: questionData.options,
          imageUrl: imageUrl, // New imageUrl field
          topic: questionData.topic || subject,
          classLevel: `${level} ${className}`,
          // Legacy fields for backward compatibility
          answerType: "Options",
          correctOption: questionData.correctOption,
          image: imageUrl,
          isAIGenerated: true,
          generationSource: "ai_bulk",
          difficultyLevel: difficulty,
          syllabusTopics: questionData.syllabusTopics || [],
          questionType: "picture_based",
          imageDescription: questionData.imageDescription,
          createdBy: "ai",
        });
      } catch (error) {
        console.error(`❌ Error generating picture question ${i + 1}:`, error.message);
        // Continue with next question
      }
    }

    return questions;
  }

  // Build prompts for different question types
  async buildMultipleChoicePrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId = null) {
    const guidelines = questionGenerationGuidelines[level];
    const syllabusData = await this.getSyllabusData(level, className, subject, selectedSyllabusId);

    // Build enhanced prompt with PDF content if available
    let syllabusContent = "";
    if (syllabusData.source === 'pdf' && syllabusData.extractedText) {
      syllabusContent = `\n\nSYLLABUS CONTENT FROM PDF:\n${syllabusData.extractedText.substring(0, 2000)}${syllabusData.extractedText.length > 2000 ? '...' : ''}`;
    }

    return `Generate a multiple choice question for Tanzania ${level} education, Class ${className}, Subject: ${subject}.${syllabusContent}

REQUIREMENTS:
- Difficulty: ${difficulty}
- Language Level: ${guidelines.languageLevel}
- Context: ${guidelines.contextualReferences}
- Cultural Sensitivity: ${guidelines.culturalSensitivity}
- Assessment Focus: ${guidelines.assessmentFocus}

SYLLABUS TOPICS: ${syllabusTopics.join(", ") || "General curriculum topics"}

AVAILABLE TOPICS: ${JSON.stringify(syllabusData)}

Generate a question that:
1. Follows Tanzania National Curriculum standards
2. Uses appropriate language for the level
3. Includes relevant Tanzanian context
4. Has exactly 4 options (A, B, C, D)
5. Has one clearly correct answer
6. Has plausible distractors

Return ONLY valid JSON in this format:
{
  "question": "Question text here",
  "options": {
    "A": "Option A text",
    "B": "Option B text", 
    "C": "Option C text",
    "D": "Option D text"
  },
  "correctOption": "A",
  "syllabusTopics": ["topic1", "topic2"]
}`;
  }

  async buildFillBlankPrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId = null) {
    const guidelines = questionGenerationGuidelines[level];
    const syllabusData = await this.getSyllabusData(level, className, subject, selectedSyllabusId);

    // Build enhanced prompt with PDF content if available
    let syllabusContent = "";
    if (syllabusData.source === 'pdf' && syllabusData.extractedText) {
      syllabusContent = `\n\nSYLLABUS CONTENT FROM PDF:\n${syllabusData.extractedText.substring(0, 1500)}${syllabusData.extractedText.length > 1500 ? '...' : ''}`;
    }

    return `Generate a fill-in-the-blank question for Tanzania ${level} education, Class ${className}, Subject: ${subject}.${syllabusContent}

REQUIREMENTS:
- Difficulty: ${difficulty}
- Language Level: ${guidelines.languageLevel}
- Context: ${guidelines.contextualReferences}
- Use underscores (____) for blanks
- Single word or short phrase answers

SYLLABUS TOPICS: ${syllabusTopics.join(", ") || "General curriculum topics"}

Return ONLY valid JSON in this format:
{
  "question": "The capital city of Tanzania is ____.",
  "correctAnswer": "Dodoma",
  "syllabusTopics": ["Geography", "Tanzania"]
}`;
  }

  async buildPictureBasedPrompt(level, className, subject, difficulty, syllabusTopics, selectedSyllabusId = null) {
    const guidelines = questionGenerationGuidelines[level];
    const syllabusData = await this.getSyllabusData(level, className, subject, selectedSyllabusId);

    // Build enhanced prompt with PDF content if available
    let syllabusContent = "";
    if (syllabusData.source === 'pdf' && syllabusData.extractedText) {
      syllabusContent = `\n\nSYLLABUS CONTENT FROM PDF:\n${syllabusData.extractedText.substring(0, 1500)}${syllabusData.extractedText.length > 1500 ? '...' : ''}`;
    }

    return `Generate a picture-based multiple choice question for Tanzania ${level} education, Class ${className}, Subject: ${subject}.${syllabusContent}

REQUIREMENTS:
- Difficulty: ${difficulty}
- Question should reference an image/diagram
- Include description of what image should show
- 4 options (A, B, C, D)

Return ONLY valid JSON in this format:
{
  "question": "Looking at the diagram, what type of animal is shown?",
  "options": {
    "A": "Mammal",
    "B": "Bird", 
    "C": "Reptile",
    "D": "Fish"
  },
  "correctOption": "A",
  "imageDescription": "A diagram showing a cow with labels pointing to its features",
  "syllabusTopics": ["Animals", "Classification"]
}`;
  }

  // Get syllabus data for specific level/class/subject
  async getSyllabusData(level, className, subject, selectedSyllabusId = null) {
    try {
      // If a specific syllabus is selected, use it directly
      if (selectedSyllabusId) {
        console.log(`🎯 Using selected syllabus: ${selectedSyllabusId}`);
        const Syllabus = require("../models/syllabusModel");
        const selectedSyllabus = await Syllabus.findById(selectedSyllabusId);

        if (selectedSyllabus && selectedSyllabus.processingStatus === 'completed') {
          console.log(`📚 Using selected syllabus: ${selectedSyllabus.title}`);
          return {
            topics: selectedSyllabus.getTopicsForAI(),
            extractedText: selectedSyllabus.extractedText,
            learningObjectives: selectedSyllabus.learningObjectives,
            competencies: selectedSyllabus.competencies,
            source: 'selected_pdf',
            syllabusId: selectedSyllabus._id,
            syllabusTitle: selectedSyllabus.title,
          };
        } else {
          console.warn(`⚠️ Selected syllabus ${selectedSyllabusId} not found or not completed`);
        }
      }

      // First try to get PDF-based syllabus content
      const pdfSyllabus = await SyllabusService.getSyllabusForAI(level, className, subject);

      if (pdfSyllabus && pdfSyllabus.topics && Object.keys(pdfSyllabus.topics).length > 0) {
        console.log(`📚 Using PDF syllabus for ${level} ${subject} Class ${className}`);
        return {
          topics: pdfSyllabus.topics,
          extractedText: pdfSyllabus.extractedText,
          learningObjectives: pdfSyllabus.learningObjectives,
          competencies: pdfSyllabus.competencies,
          source: 'pdf',
          syllabusId: pdfSyllabus.syllabusId,
        };
      }

      // Fallback to hardcoded syllabus data
      console.log(`📖 Using hardcoded syllabus for ${level} ${subject} Class ${className}`);
      let syllabusSource;
      switch (level) {
        case "primary":
          syllabusSource = primarySyllabus;
          break;
        case "secondary":
          syllabusSource = secondarySyllabus;
          break;
        case "advance":
          syllabusSource = advanceSyllabus;
          break;
        default:
          return { source: 'none' };
      }

      const hardcodedData = syllabusSource[subject]?.[className] || {};
      return {
        ...hardcodedData,
        source: 'hardcoded',
      };

    } catch (error) {
      console.error("Error getting syllabus data:", error);
      // Return empty object as fallback
      return { source: 'error', error: error.message };
    }
  }

  // Call OpenAI API
  async callOpenAI(prompt) {
    // Check if API key is available
    if (!this.openaiApiKey) {
      throw new Error("OpenAI API key is not configured. Please check your environment variables.");
    }

    console.log("🤖 Making OpenAI API call...");
    const startTime = Date.now();

    try {
      const response = await axios.post(
        "https://api.openai.com/v1/chat/completions",
        {
          model: this.model,
          messages: [
            {
              role: "system",
              content: "You are an expert in Tanzania education curriculum and question generation. Always return valid JSON responses without markdown formatting."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 1000,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${this.openaiApiKey}`,
          },
          timeout: 60000, // 60 seconds timeout for individual OpenAI calls
        }
      );

      const endTime = Date.now();
      console.log(`✅ OpenAI API call completed in ${endTime - startTime}ms`);
      return response;
    } catch (error) {
      const endTime = Date.now();
      console.error(`❌ OpenAI API call failed after ${endTime - startTime}ms:`, error.message);

      if (error.code === 'ECONNABORTED') {
        throw new Error("OpenAI API request timed out. Please try again.");
      }

      throw error;
    }

    let content = response.data.choices[0].message.content.trim();

    // Clean up markdown code blocks if present
    content = content.replace(/```json\s*/g, '').replace(/```\s*$/g, '');

    return content;
  }

  // Helper method to safely parse JSON response
  parseJSONResponse(response) {
    try {
      // Extract the content from OpenAI response
      let content;
      if (response.data && response.data.choices && response.data.choices[0]) {
        content = response.data.choices[0].message.content;
      } else if (typeof response === 'string') {
        content = response;
      } else {
        throw new Error('Invalid response format from OpenAI API');
      }

      // Additional cleanup for any remaining markdown or formatting
      let cleanResponse = content.trim();

      // Remove any leading/trailing backticks or code block markers
      cleanResponse = cleanResponse.replace(/^```(?:json)?\s*/, '');
      cleanResponse = cleanResponse.replace(/\s*```$/, '');

      // Parse the JSON
      const parsed = JSON.parse(cleanResponse);
      console.log('✅ Successfully parsed OpenAI response');
      return parsed;
    } catch (error) {
      console.error('JSON parsing error:', error.message);
      console.error('Raw response type:', typeof response);
      console.error('Raw response:', response);
      throw new Error(`Failed to parse AI response as JSON: ${error.message}`);
    }
  }
}

module.exports = AIQuestionGenerationService;
